# Generated by Django 5.1.5 on 2025-01-30 17:33

import django.core.validators
import django_countries.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('checkout', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='billing_country',
            field=django_countries.fields.CountryField(max_length=2),
        ),
        migrations.AlterField(
            model_name='order',
            name='billing_postcode',
            field=models.CharField(help_text='Enter a 4-6 digit postcode.', max_length=6, validators=[django.core.validators.RegexValidator(message='Postcode must be 4-6 characters. Only letters and numbers', regex='^[A-Za-z0-9]{4,6}$')]),
        ),
    ]
