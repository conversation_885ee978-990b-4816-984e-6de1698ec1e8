/* Contest App Styles */

/* Star Rating */
.star-rating {
    margin-bottom: 15px;
}

.rating-container {
    display: inline-flex;
    flex-direction: row;
    font-size: 1.5rem;
}

.rating-container i {
    cursor: pointer;
    margin-right: 5px;
    color: #ccc;
    transition: color 0.2s ease;
}

.rating-container i.fas {
    color: #ffc107; /* Gold color for selected stars */
}

.rating-container i:hover,
.rating-container i:hover ~ i.hover-effect {
    color: #ffc107;
}

/* Contest Cards */
.contest-card {
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.contest-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* Submission Gallery */
.submission-card {
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid #e0e0e0;
    overflow: hidden;
}

.submission-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.submission-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    padding: 0.75rem;
}

.submission-card .card-title {
    max-width: 70%;
    font-size: 1.1rem;
    line-height: 1.3;
    word-wrap: break-word;
}

.submission-image {
    overflow: hidden;
    position: relative;
}

.submission-image img {
    transition: transform 0.5s ease;
    height: 250px;
    object-fit: cover;
}

.submission-card:hover .submission-image img {
    transform: scale(1.05);
}

.comment-count {
    font-size: 0.9rem;
}

/* Gallery Filters */
.dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

/* Category Buttons */
.btn-group .btn-outline-primary {
    background-color: rgba(0, 0, 0, 0.75);
    border-color: #fff;
    color: #fff;
}

.btn-group .btn-outline-primary:hover,
.btn-group .btn-outline-primary.active {
    background-color: rgba(13, 110, 253, 0.85);
    border-color: #0d6efd;
    color: #fff;
}

/* Sort Buttons */
.btn-outline-secondary {
    background-color: #f8f9fa;
    border-color: #6c757d;
    color: #495057;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover,
.btn-outline-secondary.active {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #fff;
}

/* Book Part Dropdown */
.dropdown-toggle.btn-outline-secondary {
    background-color: #f8f9fa;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Leaderboard */
.leaderboard-card {
    transition: all 0.3s ease;
    height: 100%;
}

.leaderboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.leaderboard-card .card-header {
    font-weight: bold;
}

.leaderboard-card .list-group-item {
    transition: background-color 0.2s ease;
}

.leaderboard-card .list-group-item:hover {
    background-color: #f8f9fa;
}

/* Book Part Selection */
#id_book_part {
    max-height: 200px;
    overflow-y: auto;
}

#id_book_part option {
    padding: 8px;
    border-bottom: 1px solid #f0f0f0;
}

#id_book_part option:hover {
    background-color: #f8f9fa;
}

/* Submission Detail */
.submission-image-container {
    margin-bottom: 20px;
}

.submission-image-container img {
    max-width: 100%;
    max-height: 600px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.book-fragment {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
    padding: 15px;
    margin-bottom: 20px;
}

/* Navigation Buttons */
.nav-buttons {
    display: flex;
    flex-wrap: wrap;
}

.btn-nav {
    background-color: #f8f9fa;
    color: #212529;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-nav:hover {
    background-color: #e9ecef;
    color: #0d6efd;
    border-color: #0d6efd;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Like Button Animation */
@keyframes like-animation {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.like-btn.liked {
    animation: like-animation 0.5s ease;
}

/* Category Badges */
.category-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-right: 5px;
    margin-bottom: 5px;
}

/* Jury Scoring Form */
.jury-score-form .form-control-range {
    height: 30px;
}

.score-value {
    font-size: 1.5rem;
    font-weight: bold;
    text-align: center;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .submission-image-container img {
        max-height: 400px;
    }

    .submission-card .card-img-top {
        height: 180px;
    }

    /* Improve card title display on mobile */
    .submission-card .card-title {
        max-width: 100%;
        font-size: 1rem;
        white-space: normal;
        overflow: visible;
        text-overflow: clip;
    }

    /* Fix rating stars layout */
    .rating {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 5px;
    }

    /* Adjust book part text */
    .submission-card .text-muted {
        display: block;
        margin-top: 5px;
        font-size: 0.8rem;
    }

    /* Better category buttons */
    .btn-group {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
    }

    .btn-group .btn {
        margin-bottom: 5px;
        flex: 1 0 auto;
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Improve sort buttons */
    .btn-outline-secondary {
        padding: 0.375rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Adjust card layout */
    .submission-card .card-body {
        padding: 0.75rem;
    }

    .submission-card .card-footer {
        padding: 0.5rem;
    }

    /* Fix category badge position */
    .category-badge {
        padding: 3px 8px;
        font-size: 0.7rem;
    }

    /* Responsive navigation buttons */
    .nav-buttons {
        margin-top: 10px;
        width: 100%;
        justify-content: space-between;
    }

    .btn-nav {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
        flex: 1;
        text-align: center;
        margin-bottom: 5px;
    }
}

/* File Upload Preview */
.file-preview {
    max-width: 100%;
    max-height: 200px;
    margin-top: 10px;
    border-radius: 5px;
}

/* 3D Model Placeholder */
.model-placeholder {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
}

/* Terms and Conditions */
.terms-section {
    margin-bottom: 30px;
}

.terms-section h3 {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

/* Contest Timeline */
.contest-timeline {
    position: relative;
    padding-left: 30px;
    margin-bottom: 30px;
}

.contest-timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #dee2e6;
}

.timeline-item {
    position: relative;
    padding-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -34px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #007bff;
}

.timeline-date {
    font-weight: bold;
    margin-bottom: 5px;
}

/* Breadcrumb Styling */
.breadcrumb {
    background-color: rgba(0, 0, 0, 0.75);
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
}

.breadcrumb-item a {
    color: #fff;
}

.breadcrumb-item.active {
    color: #ccc;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: #fff;
}

.font-normal {
    font-style: normal;
}