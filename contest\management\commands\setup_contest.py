from django.core.management.base import BaseCommand
from django.utils import timezone
from contest.models import Contest, Category
import datetime


class Command(BaseCommand):
    help = 'Sets up a sample contest with categories for testing'

    def handle(self, *args, **kwargs):
        # Create a contest
        start_date = timezone.now().date()
        end_date = start_date + datetime.timedelta(days=90)  # 3 months duration
        
        contest, created = Contest.objects.get_or_create(
            name='Tempus Quest Illustration Contest',
            defaults={
                'slug': 'tempus-quest-illustration-contest',
                'description': (
                    'Welcome to the Tempus Quest Illustration Contest! '
                    'Create illustrations inspired by The Age of New Era book series '
                    'and win exciting prizes. Submit your artwork in one of our categories '
                    'and let your creativity shine!'
                ),
                'start_date': start_date,
                'end_date': end_date,
                'is_active': True,
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS(f'Created contest: {contest.name}'))
        else:
            self.stdout.write(self.style.WARNING(f'Contest already exists: {contest.name}'))
        
        # Create categories
        categories = [
            {
                'name': 'Digital Illustration',
                'description': 'Digital artwork created using software like Photoshop, Procreate, or similar tools.'
            },
            {
                'name': 'Traditional Painting',
                'description': 'Artwork created using traditional media such as oils, acrylics, watercolors, etc.'
            },
            {
                'name': 'Drawing/Sketch',
                'description': 'Pencil, pen, charcoal, or other drawing media.'
            },
            {
                'name': '3D Model/Render',
                'description': '3D models created using software like Blender, Maya, 3DS Max, etc.'
            },
            {
                'name': 'Sculpture',
                'description': 'Physical 3D artwork created in clay, stone, metal, or other materials.'
            },
            {
                'name': 'Graphic Design',
                'description': 'Vector artwork, typography, or AI-assisted designs.'
            },
            {
                'name': 'Mixed Media',
                'description': 'Artwork that combines multiple techniques or materials.'
            },
            {
                'name': 'Traditional Graphic Design',
                'description': 'Vector artwork and typography created using traditional design tools.'
            },
            {
                'name': 'AI-Assisted Art',
                'description': 'Artwork created or enhanced using AI tools (Midjourney, DALL-E, etc). Must include tool details and process.'
            },
        ]
        
        for category_data in categories:
            category, created = Category.objects.get_or_create(
                name=category_data['name'],
                contest=contest,
                defaults={
                    'description': category_data['description']
                }
            )
            
            if created:
                self.stdout.write(self.style.SUCCESS(f'Created category: {category.name}'))
            else:
                self.stdout.write(self.style.WARNING(f'Category already exists: {category.name}'))
        
        self.stdout.write(self.style.SUCCESS('Contest setup complete!'))
