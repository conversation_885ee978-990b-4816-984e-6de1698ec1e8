# Generated by Django 5.1.5 on 2025-06-30 11:45

import image_cropping.fields
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0006_postimage'),
    ]

    operations = [
        migrations.AddField(
            model_name='post',
            name='card_cropping',
            field=image_cropping.fields.ImageRatioField('post_featured_image', '4x3', adapt_rotation=False, allow_fullsize=False, free_crop=False, help_text='Crop for blog cards', hide_image_field=False, size_warning=False, verbose_name='card cropping'),
        ),
        migrations.AddField(
            model_name='post',
            name='detail_cropping',
            field=image_cropping.fields.ImageRatioField('post_featured_image', '3x2', adapt_rotation=False, allow_fullsize=False, free_crop=False, help_text='Crop for post detail view', hide_image_field=False, size_warning=False, verbose_name='detail cropping'),
        ),
        migrations.AddField(
            model_name='post',
            name='featured_cropping',
            field=image_cropping.fields.ImageRatioField('post_featured_image', '16x9', adapt_rotation=False, allow_fullsize=False, free_crop=False, help_text='Crop for featured post section', hide_image_field=False, size_warning=False, verbose_name='featured cropping'),
        ),
    ]
