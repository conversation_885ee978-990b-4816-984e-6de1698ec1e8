<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Tempus Author Platform</title>
    <style>
        :root {
            --primary-color: #4a69bd;
            --secondary-color: #1e3799;
            --text-color: #333;
            --background-color: #f5f6fa;
            --card-color: #fff;
            --accent-color: #e84118;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            text-align: center;
        }
        
        .header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .logo {
            max-width: 200px;
            margin: 0 auto;
        }
        
        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .offline-card {
            background-color: var(--card-color);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 2rem 0;
            width: 100%;
            max-width: 500px;
        }
        
        .offline-icon {
            width: 120px;
            height: 120px;
            margin-bottom: 1.5rem;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.5;
            margin-bottom: 1.5rem;
        }
        
        .button {
            display: inline-block;
            background-color: var(--primary-color);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            text-decoration: none;
            font-weight: 600;
            transition: background-color 0.2s;
            margin: 0.5rem;
            border: none;
            cursor: pointer;
        }
        
        .button:hover {
            background-color: var(--secondary-color);
        }
        
        .button.secondary {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }
        
        .button.secondary:hover {
            background-color: rgba(74, 105, 189, 0.1);
        }
        
        .footer {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem;
            font-size: 0.9rem;
        }
        
        .cached-content {
            margin-top: 2rem;
            width: 100%;
        }
        
        .cached-list {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }
        
        .cached-item {
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .cached-item:last-child {
            border-bottom: none;
        }
        
        .cached-icon {
            margin-right: 0.75rem;
            color: var(--primary-color);
        }
        
        @media (max-width: 600px) {
            .container {
                padding: 1rem;
            }
            
            .offline-card {
                padding: 1.5rem;
                margin: 1rem 0;
            }
            
            .offline-icon {
                width: 80px;
                height: 80px;
            }
            
            h1 {
                font-size: 1.5rem;
            }
            
            p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">TEMPUS AUTHOR PLATFORM</div>
    </div>
    
    <div class="container">
        <div class="offline-card">
            <svg class="offline-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="#4a69bd" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M1 1l22 22"></path>
                <path d="M16.72 11.06A10.94 10.94 0 0 1 19 12.55"></path>
                <path d="M5 12.55a10.94 10.94 0 0 1 5.17-2.39"></path>
                <path d="M10.71 5.05A16 16 0 0 1 22.58 9"></path>
                <path d="M1.42 9a15.91 15.91 0 0 1 4.7-2.88"></path>
                <path d="M8.53 16.11a6 6 0 0 1 6.95 0"></path>
                <line x1="12" y1="20" x2="12.01" y2="20"></line>
            </svg>
            
            <h1>You're Offline</h1>
            <p>It looks like you don't have an internet connection right now. Some features may be limited until you're back online.</p>
            
            <button class="button" id="reload-button">Try Again</button>
            <a href="/" class="button secondary">Go to Homepage</a>
        </div>
        
        <div class="cached-content">
            <h2>Available Offline</h2>
            <p>You can still access these items while offline:</p>
            
            <div id="cached-items-container">
                <p>Loading available content...</p>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <p>&copy; Tempus Author Platform</p>
    </div>
    
    <script>
        // Check if we're online and reload the page if we are
        function checkOnlineStatus() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                console.log('Still offline');
            }
        }
        
        // Add event listener to reload button
        document.getElementById('reload-button').addEventListener('click', checkOnlineStatus);
        
        // Listen for online status changes
        window.addEventListener('online', function() {
            window.location.reload();
        });
        
        // Function to display cached items
        async function displayCachedItems() {
            const container = document.getElementById('cached-items-container');
            
            if (!('caches' in window)) {
                container.innerHTML = '<p>Your browser does not support offline storage.</p>';
                return;
            }
            
            try {
                // Get all cache storage names
                const cacheNames = await caches.keys();
                
                if (cacheNames.length === 0) {
                    container.innerHTML = '<p>No cached content available.</p>';
                    return;
                }
                
                // Create HTML for cached items
                let html = '<ul class="cached-list">';
                
                // Check for audio cache
                const audioCache = cacheNames.find(name => name.includes('audio'));
                if (audioCache) {
                    const cache = await caches.open(audioCache);
                    const keys = await cache.keys();
                    
                    if (keys.length > 0) {
                        html += `
                            <li class="cached-item">
                                <span class="cached-icon">🎵</span>
                                <span>${keys.length} audio files available offline</span>
                            </li>
                        `;
                    }
                }
                
                // Check for HTML cache
                const htmlCache = cacheNames.find(name => name.includes('html'));
                if (htmlCache) {
                    const cache = await caches.open(htmlCache);
                    const keys = await cache.keys();
                    
                    if (keys.length > 0) {
                        html += `
                            <li class="cached-item">
                                <span class="cached-icon">📄</span>
                                <span>${keys.length} pages available offline</span>
                            </li>
                        `;
                    }
                }
                
                // Check for static cache
                const staticCache = cacheNames.find(name => name.includes('static'));
                if (staticCache) {
                    html += `
                        <li class="cached-item">
                            <span class="cached-icon">🏠</span>
                            <span>Homepage</span>
                        </li>
                    `;
                }
                
                html += '</ul>';
                container.innerHTML = html;
                
            } catch (error) {
                console.error('Error displaying cached items:', error);
                container.innerHTML = '<p>Error loading cached content.</p>';
            }
        }
        
        // Display cached items when page loads
        document.addEventListener('DOMContentLoaded', displayCachedItems);
    </script>
</body>
</html> 