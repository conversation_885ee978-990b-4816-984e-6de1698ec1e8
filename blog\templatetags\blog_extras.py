from django import template

register = template.Library()


@register.filter
def get_featured_image_url(post, crop_type='featured'):
    """
    Template filter to get cropped featured image URL.
    Usage: {{ post|get_featured_image_url:'card' }}
    """
    if hasattr(post, 'get_featured_image_url'):
        try:
            url = post.get_featured_image_url(crop_type)
            # Debug: print to console in development
            import logging
            logger = logging.getLogger(__name__)
            logger.debug(f"Generated {crop_type} URL for {post.post_title}: {url}")
            return url
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating {crop_type} URL for {post.post_title}: {e}")
            return post.post_featured_image.url if post.post_featured_image else None
    return post.post_featured_image.url if post.post_featured_image else None
