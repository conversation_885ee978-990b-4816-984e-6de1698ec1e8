from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.shortcuts import redirect
from django.contrib import messages
from django.http import HttpResponseRedirect
from django.urls import reverse
import threading
from .models import Order, OrderItem
from .combined_email import send_combined_order_email
from .free_book_email import send_free_book_email


class OrderLineItemAdminInline(admin.TabularInline):
    """
    Inline admin for managing order items within the order admin interface.
    """
    model = OrderItem
    fields = ('product', 'price',)
    readonly_fields = ('price',)
    extra = 1

    def get_readonly_fields(self, request, obj=None):
        """
        Make product field readonly for existing orders.
        """
        if obj:
            return ['product', 'price']
        return ['price']


class OrderAdmin(admin.ModelAdmin):
    """
    Admin configuration for managing customer orders.
    """
    inlines = (OrderLineItemAdminInline,)
    readonly_fields = ('id', 'total_amount', 'order_date', 'resend_email_field')
    actions = ['mark_as_free_book', 'send_free_book_email', 'resend_order_email']

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                'resend-email/<int:order_id>/',
                self.admin_site.admin_view(self.resend_email_view),
                name='resend-order-email',
            ),
        ]
        return custom_urls + urls

    def resend_email_view(self, request, order_id):
        """View to handle resending an order email"""
        try:
            order = Order.objects.get(id=order_id)

            # Reset email status
            order.email_delivery_status = 'pending'
            order.save(update_fields=['email_delivery_status'])

            # Send the appropriate email based on payment status
            if order.payment_status == 'free_book':
                email_thread = threading.Thread(
                    target=send_free_book_email,
                    args=(order,)
                )
                email_thread.daemon = True
                email_thread.start()
                message = f"Free book email is being sent for order #{order_id}."
            else:
                email_thread = threading.Thread(
                    target=send_combined_order_email,
                    args=(order,)
                )
                email_thread.daemon = True
                email_thread.start()
                message = f"Order email is being sent for order #{order_id}."

            messages.success(request, message)
        except Order.DoesNotExist:
            messages.error(request, f"Order #{order_id} not found.")

        # Redirect back to the order change page
        return HttpResponseRedirect(
            reverse('admin:checkout_order_change', args=[order_id])
        )

    fieldsets = (
        ('Order Information', {
            'fields': (
                'id',
                'user',
                'order_date',
                'total_amount',
                'payment_status',
                'email_delivery_status',
                'resend_email_field',
            )
        }),
        ('Customer Information', {
            'fields': (
                'full_name',
                'email',
                'phone_number'
            )
        }),
        ('Billing Address', {
            'fields': (
                'billing_address1',
                'billing_address2',
                'billing_city',
                'billing_postcode',
                'billing_country'
            )
        })
    )

    list_display = (
        'id',
        'full_name',
        'email',
        'total_amount',
        'order_date',
        'payment_status',
        'email_status_colored',
        'resend_email_button'
    )

    list_filter = ('payment_status', 'email_delivery_status')

    search_fields = (
        'full_name',
        'email',
        'billing_postcode'
    )

    ordering = ('-order_date',)

    def email_status_colored(self, obj):
        """
        Display email delivery status with color coding.
        """
        colors = {
            'pending': 'orange',
            'sent': 'green',
            'failed': 'red',
        }
        color = colors.get(obj.email_delivery_status, 'black')
        return format_html(
            '<span style="color: {};">{}</span>',
            color,
            obj.get_email_delivery_status_display()
        )
    email_status_colored.short_description = 'Email Status'

    def resend_email_button(self, obj):
        """
        Display a button to resend the email for this order.
        """
        url = reverse('admin:resend-order-email', args=[obj.id])
        return format_html(
            '<a class="button" href="{}">Resend Email</a>',
            url
        )
    resend_email_button.short_description = 'Resend'

    def resend_email_field(self, obj):
        """
        Display a button in the form to resend the email.
        """
        if obj and obj.id:
            url = reverse('admin:resend-order-email', args=[obj.id])
            return format_html(
                '<a class="button" href="{}" style="background-color: #417690; '
                'color: white; padding: 10px 15px; text-decoration: none; '
                'border-radius: 4px; display: inline-block; margin-top: 10px;">'
                'Resend Order Email</a>',
                url
            )
        return "Save the order first to enable email resending."
    resend_email_field.short_description = 'Resend Email'

    def mark_as_free_book(self, request, queryset):
        """
        Action to mark selected orders as free books.
        """
        updated = queryset.update(
            payment_status='free_book',
            email_delivery_status='pending'
        )
        self.message_user(
            request,
            f"{updated} order(s) marked as free books. "
            f"The free book emails will be sent automatically."
        )
    mark_as_free_book.short_description = "Mark selected orders as free books"

    def send_free_book_email(self, request, queryset):
        """
        Action to manually send free book emails for selected orders.
        """
        from .free_book_email import send_free_book_email
        import threading

        count = 0
        for order in queryset:
            if order.payment_status == 'free_book':
                # Reset email status if it failed previously
                if order.email_delivery_status == 'failed':
                    order.email_delivery_status = 'pending'
                    order.save(update_fields=['email_delivery_status'])

                # Start a thread to send the email
                email_thread = threading.Thread(
                    target=send_free_book_email,
                    args=(order,)
                )
                email_thread.daemon = True
                email_thread.start()
                count += 1

        if count > 0:
            self.message_user(
                request,
                f"Free book emails are being sent for {count} order(s)."
            )
        else:
            self.message_user(
                request,
                "No free book orders selected. Please select orders with "
                "payment status 'Free Book'."
            )
    send_free_book_email.short_description = "Send free book emails for selected orders"

    def resend_order_email(self, request, queryset):
        """
        Action to resend order emails for selected orders.
        """
        count = 0
        for order in queryset:
            # Reset email status
            order.email_delivery_status = 'pending'
            order.save(update_fields=['email_delivery_status'])

            # Send the appropriate email based on payment status
            if order.payment_status == 'free_book':
                email_thread = threading.Thread(
                    target=send_free_book_email,
                    args=(order,)
                )
                email_thread.daemon = True
                email_thread.start()
            else:
                email_thread = threading.Thread(
                    target=send_combined_order_email,
                    args=(order,)
                )
                email_thread.daemon = True
                email_thread.start()

            count += 1

        if count > 0:
            self.message_user(
                request,
                f"Emails are being resent for {count} order(s)."
            )
        else:
            self.message_user(
                request,
                "No orders selected."
            )
    resend_order_email.short_description = "Resend emails for selected orders"


admin.site.register(Order, OrderAdmin)
