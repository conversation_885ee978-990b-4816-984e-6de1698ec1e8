import logging
import os
import socket
import time
import threading
from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from .models import Order

# Set up logger
logger = logging.getLogger(__name__)


@receiver(post_save, sender=Order)
def send_digital_content_on_payment_confirmation(sender, instance, created,
                                                **kwargs):
    """
    Send digital content (ebooks and audiobook links) to customers when an
    order is marked as paid.
    
    This signal is triggered whenever an Order is saved. It checks if the
    payment status is 'paid' and if so, sends an email with any ebook files
    attached to the products in the order and includes links to audiobooks.
    """
    logger.info(
        f"Signal triggered for order {instance.id} "
        f"with payment status: {instance.payment_status}"
    )
    
    if instance.payment_status == 'paid':
        logger.info(
            f"Processing digital content delivery for order {instance.id}"
        )
        
        # Launch email sending in a background thread to prevent webhook timeouts
        email_thread = threading.Thread(
            target=send_digital_content_email,
            args=(instance.id,)
        )
        email_thread.daemon = True
        email_thread.start()
        
        logger.info(
            f"Email delivery started in background for order {instance.id}"
        )


def send_digital_content_email(order_id):
    """
    Send digital content email in a background thread.
    This function handles all the email sending logic separate from the signal.
    """
    try:
        # Retrieve the order
        try:
            instance = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            logger.error(
                f"Order {order_id} not found when trying to send digital content email"
            )
            return
            
        # Get all items in the order
        order_items = instance.orderitem_set.all()
        
        digital_items = []
        for item in order_items:
            logger.info(f"Checking product {item.product.name} for digital content")
            if item.product.ebook_file or item.product.audiobook_link:
                logger.info(f"Product {item.product.name} has digital content")
                digital_items.append(item)
        
        # If there is digital content, send an email
        if digital_items:
            logger.info(f"Found {len(digital_items)} items with digital content in order")
            
            # Prepare email content
            context = {
                'order': instance,
                'digital_items': digital_items,
                'contact_email': settings.DEFAULT_FROM_EMAIL,
            }
            
            # Render email templates
            html_content = render_to_string(
                'checkout/emails/ebook_delivery_email.html', context
            )
            text_content = strip_tags(html_content)
            
            # Create email
            subject = (f'Your digital content purchase from '
                      f'{settings.CONTACT_DISPLAY_NAME}')
            from_email = settings.DEFAULT_FROM_EMAIL
            to_email = instance.email
            
            logger.info(f"Preparing to send digital content email to {to_email}")
            
            # Create email message
            email = EmailMultiAlternatives(
                subject,
                text_content,
                from_email,
                [to_email]
            )
            
            email.attach_alternative(html_content, "text/html")
            
            # Attach ebook files
            attachments_successful = []
            attachments_failed = []
            
            for item in digital_items:
                if item.product.ebook_file:
                    file_path = os.path.join(
                        settings.MEDIA_ROOT, str(item.product.ebook_file)
                    )
                    logger.info(f"Attaching ebook file: {file_path}")
                    
                    try:
                        file_name = os.path.basename(file_path)
                        
                        with open(file_path, 'rb') as f:
                            file_content = f.read()
                            file_size_kb = len(file_content) / 1024
                            logger.info(f"File size: {file_size_kb:.2f} KB")
                            email.attach(file_name, file_content, 'application/pdf')
                            attachments_successful.append(file_name)
                    except FileNotFoundError:
                        logger.error(f"Ebook file not found: {file_path}")
                        attachments_failed.append(file_path)
                        continue
                    except Exception as e:
                        logger.error(f"Error attaching file {file_path}: {str(e)}")
                        attachments_failed.append(file_path)
                        continue
            
            # Send email with retry logic
            max_retries = 3
            retry_count = 0
            delivery_success = False
            
            while retry_count < max_retries and not delivery_success:
                try:
                    logger.info(
                        f"Sending digital content delivery email "
                        f"(attempt {retry_count + 1})"
                    )
                    email.send(fail_silently=False)
                    logger.info("Digital content delivery email sent successfully")
                    delivery_success = True
                    
                    # If we have a successful delivery, log details
                    logger.info(
                        f"Successfully attached {len(attachments_successful)} files: "
                        f"{', '.join(attachments_successful)}"
                    )
                    if attachments_failed:
                        logger.warning(
                            f"Failed to attach {len(attachments_failed)} files: "
                            f"{', '.join(attachments_failed)}"
                        )
                        
                    # Add a record of the email delivery to the order
                    instance.email_delivery_status = 'sent'
                    instance.save(update_fields=['email_delivery_status'])
                    
                except socket.error as e:
                    retry_count += 1
                    logger.error(
                        f"Error sending digital content delivery email "
                        f"(attempt {retry_count}): {str(e)}"
                    )
                    logger.error(f"Email error details: {str(e)}")
                    
                    if retry_count >= max_retries:
                        logger.error(f"Failed to send email after {max_retries} attempts")
                        
                        # In development or when debugging, print the email content to console
                        if 'DEVELOPMENT' in os.environ or settings.DEBUG:
                            logger.info(
                                "Development mode detected. Printing email content to console:"
                            )
                            logger.info(f"Subject: {subject}")
                            logger.info(f"To: {to_email}")
                            logger.info(f"From: {from_email}")
                            logger.info(f"Content: {text_content[:500]}...")
                            logger.info(f"Attached {len(attachments_successful)} files")
                            
                        # Update order with email failure status
                        instance.email_delivery_status = 'failed'
                        instance.save(update_fields=['email_delivery_status'])
                    else:
                        # Wait before retrying
                        time.sleep(2)
                
                except Exception as e:
                    retry_count += 1
                    logger.error(
                        f"Unexpected error sending digital content delivery email "
                        f"(attempt {retry_count}): {str(e)}"
                    )
                    
                    if retry_count >= max_retries:
                        logger.error(f"Failed to send email after {max_retries} attempts")
                        instance.email_delivery_status = 'failed'
                        instance.save(update_fields=['email_delivery_status'])
                    else:
                        # Wait before retrying
                        time.sleep(2)
        else:
            logger.info("No digital content found in order, skipping email delivery")
    except Exception as e:
        logger.error(
            f"Critical error in background email sending process for order {order_id}: "
            f"{str(e)}"
        )
