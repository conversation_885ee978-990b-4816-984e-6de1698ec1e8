# Generated by Django 5.1.5 on 2025-02-23 18:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('newsletter', '0003_alter_emailtracking_unique_together_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='NewsletterTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('subject_template', models.CharField(help_text='You can use {{ subscriber.name }} etc.', max_length=200)),
                ('content_template', models.TextField(help_text='HTML content with template variables')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
