from django_select2 import forms as s2forms
from taggit.models import Tag

class TagWidget(s2forms.ModelSelect2TagWidget):
    search_fields = ['name__icontains']
    queryset = Tag.objects.all()

    def value_from_datadict(self, data, files, name):
        values = super().value_from_datadict(data, files, name)
        if values is None:
            return None
        
        # Convert the string values to a list
        if isinstance(values, str):
            values = values.split(',')
        
        # Clean up the values
        values = [v.strip() for v in values if v.strip()]
        
        # Get existing tags
        existing_tags = self.queryset.filter(name__in=values)
        existing_names = set(tag.name for tag in existing_tags)
        
        # Create new tags for values that don't exist
        new_tags = []
        for value in values:
            if value not in existing_names:
                new_tag = Tag.objects.create(name=value)
                new_tags.append(new_tag)
        
        # Combine existing and new tags
        return [str(tag.pk) for tag in list(existing_tags) + new_tags]

    class Media:
        css = {
            'all': (
                'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css',
                'https://cdnjs.cloudflare.com/ajax/libs/select2-bootstrap-5-theme/1.3.0/select2-bootstrap-5-theme.min.css',
            )
        }
        js = (
            'https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.full.min.js',
        )
