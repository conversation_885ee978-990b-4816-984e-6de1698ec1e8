# Generated by Django 5.1.5 on 2025-02-28 15:50

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('works', '0009_productattachment'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='audiobook_link',
            field=models.URLField(blank=True, help_text='Link to the audiobook (MP3) that customers will access after purchase', max_length=1024, null=True),
        ),
        migrations.AlterField(
            model_name='product',
            name='ebook_file',
            field=models.FileField(blank=True, help_text='Upload the ebook file (PDF) that customers will receive after purchase', null=True, upload_to='ebooks/'),
        ),
    ]
