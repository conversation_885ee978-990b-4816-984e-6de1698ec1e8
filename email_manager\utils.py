"""
Email utility functions for sending emails with rate limiting and queueing.
"""
import json
import logging
from django.core.mail import EmailMultiAlternatives
from django.utils.html import strip_tags
from django.conf import settings
from django.utils import timezone
from .models import EmailCounter, QueuedEmail, EmailConfiguration

logger = logging.getLogger(__name__)

def send_email_with_rate_limit(subject, recipient, html_content, text_content=None,
                              priority=3, content_type=None, object_id=None,
                              attachments=None, from_email=None):
    """
    Send an email with rate limiting.
    If we're at the limit, queue the email for later.

    Args:
        subject (str): Email subject
        recipient (str): Recipient email address
        html_content (str): HTML content of the email
        text_content (str, optional): Plain text content. If None, generated from HTML.
        priority (int, optional): Priority level (1-4, 1 being highest). Defaults to 3.
        content_type (str, optional): Type of content (e.g., 'order', 'verification').
        object_id (str, optional): ID of related object (e.g., order ID).
        attachments (list, optional): List of attachment dicts with 'path' and 'filename'.
        from_email (str, optional): Sender email. Defaults to settings.DEFAULT_FROM_EMAIL.

    Returns:
        tuple: (success, message)
    """
    # If text_content not provided, generate from HTML
    if text_content is None:
        text_content = strip_tags(html_content)

    # Use default from_email if not provided
    if from_email is None:
        from_email = settings.DEFAULT_FROM_EMAIL

    # Process attachments
    has_attachments = False
    attachment_data = None

    if attachments:
        has_attachments = True
        attachment_data = json.dumps(attachments)

    # Check if we can send now
    try:
        can_send = EmailCounter.can_send_email()
    except Exception:
        # If we can't check the counter, assume we can send
        # This ensures emails still work during migrations
        can_send = True

    if can_send:
        try:
            # Create email message
            email = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=from_email,
                to=[recipient]
            )
            email.attach_alternative(html_content, "text/html")

            # Add attachments if any
            if attachments:
                for attachment in attachments:
                    if 'path' in attachment and 'filename' in attachment:
                        email.attach_file(
                            attachment['path'],
                            attachment.get('mimetype', None)
                        )

            # Send the email
            email.send(fail_silently=False)

            # Increment counter
            EmailCounter.increment()

            logger.info(f"Email sent: {subject} to {recipient}")
            return True, "Email sent successfully"
        except Exception as e:
            logger.error(f"Error sending email: {str(e)}")

            # Queue the email if sending fails
            QueuedEmail.objects.create(
                subject=subject,
                recipient=recipient,
                html_content=html_content,
                text_content=text_content,
                from_email=from_email,
                priority=priority,
                content_type=content_type,
                object_id=object_id,
                status='failed',
                error_message=str(e),
                has_attachments=has_attachments,
                attachment_data=attachment_data
            )
            return False, str(e)
    else:
        # Queue the email for later
        QueuedEmail.objects.create(
            subject=subject,
            recipient=recipient,
            html_content=html_content,
            text_content=text_content,
            from_email=from_email,
            priority=priority,
            content_type=content_type,
            object_id=object_id,
            has_attachments=has_attachments,
            attachment_data=attachment_data
        )
        logger.info(f"Email queued: {subject} to {recipient} (hourly limit reached)")
        return False, "Email queued for later delivery due to rate limits"


def check_email_verification_required():
    """Check if email verification is required based on configuration"""
    try:
        config = EmailConfiguration.get_config()
        return config.require_email_verification
    except Exception:
        # Default to True if we can't access the database
        return True


def should_send_welcome_email():
    """Check if welcome emails should be sent based on configuration"""
    try:
        config = EmailConfiguration.get_config()
        return config.send_welcome_emails
    except Exception:
        # Default to True if we can't access the database
        return True


def should_send_newsletter_confirmation():
    """Check if newsletter confirmation emails should be sent"""
    try:
        config = EmailConfiguration.get_config()
        return config.send_newsletter_confirmation
    except Exception:
        # Default to False if we can't access the database
        return False


def should_combine_order_emails():
    """Check if order confirmation and digital content emails should be combined"""
    try:
        config = EmailConfiguration.get_config()
        return config.combine_order_emails
    except Exception:
        # Default to True if we can't access the database
        return True


def get_email_stats():
    """Get statistics about email sending"""
    try:
        now = timezone.now()
        current_hour_count = EmailCounter.get_current_count()

        try:
            config = EmailConfiguration.get_config()
            limit = config.hourly_email_limit
        except Exception:
            limit = 90  # Default limit

        # Get counts for today
        try:
            today_counters = EmailCounter.objects.filter(date=now.date())
            today_total = sum(counter.count for counter in today_counters)
        except Exception:
            today_total = 0

        # Get pending emails count
        try:
            pending_count = QueuedEmail.objects.filter(status='pending').count()
            failed_count = QueuedEmail.objects.filter(status='failed').count()
        except Exception:
            pending_count = 0
            failed_count = 0

        return {
            'current_hour': current_hour_count,
            'limit': limit,
            'percent_used': (current_hour_count / limit) * 100 if limit else 0,
            'today_total': today_total,
            'pending_emails': pending_count,
            'failed_emails': failed_count,
        }
    except Exception:
        # Return default stats if we can't access the database
        return {
            'current_hour': 0,
            'limit': 90,
            'percent_used': 0,
            'today_total': 0,
            'pending_emails': 0,
            'failed_emails': 0,
        }
