from django.conf import settings
import os


def google_form_url(request):
    """
    Context processor to add Google Form URL to templates
    """
    return {
        'GOOGLE_FORM_URL': os.environ.get('GOOGLE_FORM_URL', '')
    }


def static_url_with_host(request):
    """
    Context processor to add a fully qualified static URL with host
    """
    # Get the static URL from settings
    static_url = settings.STATIC_URL
    
    # If it's already an absolute URL (starts with http), return it as is
    if static_url.startswith(('http://', 'https://')):
        return {'STATIC_URL_WITH_HOST': static_url}
    
    # Otherwise, prepend the scheme and host
    host = request.get_host()
    scheme = request.scheme
    full_url = f"{scheme}://{host}{static_url}"
    
    return {'STATIC_URL_WITH_HOST': full_url}


def media_url_with_host(request):
    """
    Context processor to add a fully qualified media URL with host
    """
    # Get the media URL from settings
    media_url = settings.MEDIA_URL
    
    # If it's already an absolute URL (starts with http), return it as is
    if media_url.startswith(('http://', 'https://')):
        return {'MEDIA_URL_WITH_HOST': media_url}
    
    # Otherwise, prepend the scheme and host
    host = request.get_host()
    scheme = request.scheme
    full_url = f"{scheme}://{host}{media_url}"
    
    return {'MEDIA_URL_WITH_HOST': full_url}
