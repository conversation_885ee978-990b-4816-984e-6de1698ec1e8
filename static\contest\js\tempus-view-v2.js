/**
 * TEMPUS View - Enhanced gallery viewing experience v2.0
 *
 * This script implements a lightbox-style gallery with a detail sidebar
 * for the Tempus Quest Illustration Contest.
 *
 * Version 2.0: Complete rewrite with robust error handling
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('TEMPUS View v2.0 initializing...');

    // Initialize variables
    let currentIndex = 0;
    let submissions = [];
    let isOpen = false;
    let isZoomed = false;
    let slideshowInterval = null;
    let isSlideshow = false;
    let isFullscreen = false;
    let areControlsHidden = false;
    let isMobileDevice = false;
    let isSmallScreen = false;
    let screenWidth = window.innerWidth;
    let screenHeight = window.innerHeight;

    // Zoom and pan variables
    let currentZoomLevel = 1;
    const maxZoomLevel = 3;
    let isPanning = false;
    let startX, startY, startTranslateX = 0, startTranslateY = 0;
    let translateX = 0, translateY = 0;

    // Handle right-click on images to toggle controls
    document.addEventListener('contextmenu', function(e) {
        const target = e.target;

        // If right-clicking in the TEMPUS View, toggle controls
        if (isOpen && target.closest('.tempus-view')) {
            e.preventDefault();
            toggleControls();
            return false;
        }

        // Otherwise prevent right-click on gallery images
        if (target.tagName === 'IMG' && target.closest('.submission-image-container')) {
            e.preventDefault();
            alert('Image downloading is not permitted according to our terms and conditions.');
            return false;
        }
    }, false);

    // Function to toggle controls visibility
    function toggleControls() {
        // Refresh DOM elements
        elements = getDOMElements();

        areControlsHidden = !areControlsHidden;

        // Toggle 'hidden' class on all controls
        if (elements.controlsBar) {
            elements.controlsBar.classList.toggle('hidden', areControlsHidden);
        }

        if (elements.tempusViewDetails) {
            elements.tempusViewDetails.classList.toggle('hidden', areControlsHidden);
        }

        if (elements.tempusViewClose) {
            elements.tempusViewClose.classList.toggle('hidden', areControlsHidden);
        }

        if (elements.tempusViewPrev) {
            elements.tempusViewPrev.classList.toggle('hidden', areControlsHidden);
        }

        if (elements.tempusViewNext) {
            elements.tempusViewNext.classList.toggle('hidden', areControlsHidden);
        }

        // Change cursor to indicate controls can be shown
        if (elements.tempusViewContainer) {
            elements.tempusViewContainer.style.cursor = areControlsHidden ? 'context-menu' : 'default';
        }

        // Show a brief hint for mobile users
        if (isMobileDevice && areControlsHidden) {
            const hint = document.createElement('div');
            hint.style.position = 'absolute';
            hint.style.top = '50%';
            hint.style.left = '50%';
            hint.style.transform = 'translate(-50%, -50%)';
            hint.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            hint.style.color = 'white';
            hint.style.padding = '10px 15px';
            hint.style.borderRadius = '20px';
            hint.style.fontSize = isSmallPhone ? '0.8rem' : '1rem';
            hint.style.zIndex = '10001';
            hint.style.opacity = '0';
            hint.style.transition = 'opacity 0.3s ease';
            hint.textContent = 'Tap to show controls';

            elements.tempusViewContainer.appendChild(hint);

            // Fade in
            setTimeout(() => {
                hint.style.opacity = '1';
            }, 10);

            // Fade out and remove
            setTimeout(() => {
                hint.style.opacity = '0';
                setTimeout(() => {
                    if (hint.parentNode) {
                        hint.parentNode.removeChild(hint);
                    }
                }, 300);
            }, 1500);
        }
    }

    // DOM elements - get them fresh each time to avoid stale references
    function getDOMElements() {
        return {
            tempusViewBtn: document.getElementById('tempus-view-btn'),
            tempusViewContainer: document.getElementById('tempus-view-container'),
            tempusViewClose: document.getElementById('tempus-view-close'),
            tempusViewImage: document.getElementById('tempus-view-image'),
            tempusViewPrev: document.getElementById('tempus-view-prev'),
            tempusViewNext: document.getElementById('tempus-view-next'),
            tempusViewDetails: document.getElementById('tempus-view-details'),
            tempusViewTitle: document.getElementById('tempus-view-title'),
            tempusViewArtist: document.getElementById('tempus-view-artist'),
            tempusViewCategory: document.getElementById('tempus-view-category'),
            tempusViewLikes: document.getElementById('tempus-view-likes'),
            tempusViewDetailsLink: document.getElementById('tempus-view-details-link'),
            slideshowBtn: document.getElementById('tempus-view-slideshow'),
            zoomBtn: document.getElementById('tempus-view-zoom'),
            fullscreenBtn: document.getElementById('tempus-view-fullscreen'),
            controlsBar: document.querySelector('.tempus-view-controls')
        };
    }

    // Get initial DOM elements
    let elements = getDOMElements();

    // Collect all submission data from the gallery
    function collectSubmissions() {
        console.log('Collecting submissions data...');
        submissions = [];

        try {
            // Get all submission cards
            const cards = document.querySelectorAll('.submission-card');
            console.log(`Found ${cards.length} submission cards`);

            // Process each card
            Array.from(cards).forEach((card, index) => {
                try {
                    // First, check if this card has an image we can use
                    let imageUrl = null;

                    // Try multiple selectors to find the image
                    const imageSelectors = [
                        '.card-img-top',
                        '.submission-image img',
                        'img'
                    ];

                    for (const selector of imageSelectors) {
                        const img = card.querySelector(selector);
                        if (img && img.src) {
                            imageUrl = img.src;
                            break;
                        }
                    }

                    // Skip if no image found
                    if (!imageUrl) {
                        console.log(`Skipping card ${index} - no image found`);
                        return;
                    }

                    // Create a submission object with default values
                    const submission = {
                        index,
                        imageUrl,
                        title: 'Untitled Submission',
                        artist: 'Unknown Artist',
                        category: 'Uncategorized',
                        description: '',
                        likes: '0',
                        date: '',
                        detailsLink: '#'
                    };

                    // Try to extract title
                    const titleElement = card.querySelector('.card-title');
                    if (titleElement && titleElement.textContent) {
                        submission.title = titleElement.textContent.trim();
                    }

                    // Try to extract artist and date
                    const textElements = card.querySelectorAll('.text-muted');
                    textElements.forEach(el => {
                        const text = el.textContent.trim();
                        if (text.includes('By:')) {
                            const parts = text.split('By:');
                            if (parts.length > 1) {
                                submission.artist = parts[1].split('\n')[0].trim();
                            }

                            // Try to get date
                            const lines = text.split('\n').filter(line => line.trim() !== '');
                            if (lines.length > 1) {
                                submission.date = lines[lines.length - 1].trim();
                            }
                        }
                    });

                    // Try to extract category
                    const categoryElement = card.querySelector('.badge.bg-secondary');
                    if (categoryElement && categoryElement.textContent) {
                        submission.category = categoryElement.textContent.trim();
                    }

                    // Try to extract description
                    const descElement = card.querySelector('.card-text');
                    if (descElement && descElement.textContent) {
                        submission.description = descElement.textContent.trim();
                    }

                    // Try to extract likes
                    const likesElement = card.querySelector('.badge.bg-primary.rounded-pill');
                    if (likesElement && likesElement.textContent) {
                        submission.likes = likesElement.textContent.trim();
                    }

                    // Try to extract details link
                    const detailsLink = card.querySelector('.btn.btn-primary');
                    if (detailsLink && detailsLink.getAttribute('href')) {
                        submission.detailsLink = detailsLink.getAttribute('href');
                    }

                    // Add to submissions array
                    submissions.push(submission);
                    console.log(`Added submission ${index}: ${submission.title}`);
                } catch (error) {
                    console.error(`Error processing card ${index}:`, error);
                }
            });

            console.log(`Successfully collected ${submissions.length} submissions`);
        } catch (error) {
            console.error('Error collecting submissions:', error);
        }

        return submissions;
    }

    // Open the TEMPUS View
    function openTempusView(startIndex = 0) {
        console.log(`Opening TEMPUS View with startIndex: ${startIndex}`);

        // Refresh DOM elements
        elements = getDOMElements();

        // Always recollect submissions to ensure we have the latest data
        collectSubmissions();

        if (submissions.length === 0) {
            console.warn('No submissions found, showing alert');
            alert('No image submissions found in the current gallery view.');
            return;
        }

        // Validate the startIndex
        if (startIndex < 0 || startIndex >= submissions.length) {
            console.warn(`Invalid startIndex ${startIndex}, defaulting to 0`);
            startIndex = 0;
        }

        currentIndex = startIndex;
        console.log(`Setting current index to ${currentIndex}`);

        // Update screen detection to adjust UI accordingly
        updateScreenDetection();

        // Make sure the container exists
        if (!elements.tempusViewContainer) {
            console.error('TEMPUS View container not found!');
            return;
        }

        // Update the view
        updateTempusView();

        // Show the container
        elements.tempusViewContainer.classList.remove('d-none');
        document.body.classList.add('tempus-view-open');
        isOpen = true;

        // Reset controls visibility - only for larger screens now
        areControlsHidden = false;
        if (elements.controlsBar) elements.controlsBar.classList.remove('hidden');
        if (elements.tempusViewDetails) elements.tempusViewDetails.classList.remove('hidden');
        if (elements.tempusViewClose) elements.tempusViewClose.classList.remove('hidden');
        if (elements.tempusViewPrev) elements.tempusViewPrev.classList.remove('hidden');
        if (elements.tempusViewNext) elements.tempusViewNext.classList.remove('hidden');

        // Add show-hint class for mobile devices to display usage instructions
        updateScreenDetection();
        if (isMobileDevice) {
            elements.tempusViewContainer.classList.add('show-hint');

            // Remove the class after 5 seconds
            setTimeout(() => {
                if (elements.tempusViewContainer) {
                    elements.tempusViewContainer.classList.remove('show-hint');
                }
            }, 5000);
        }

        // Reset cursor
        if (elements.tempusViewContainer) {
            elements.tempusViewContainer.style.cursor = 'default';
        }

        // Reset any inline styles that might interfere with CSS
        if (elements.tempusViewImage) {
            elements.tempusViewImage.style.maxWidth = '';
            elements.tempusViewImage.style.maxHeight = '';
            elements.tempusViewImage.style.marginTop = '';
        }

        if (elements.controlsBar) {
            elements.controlsBar.style.bottom = '';
            elements.controlsBar.style.transform = '';
        }

        if (elements.tempusViewDetails) {
            elements.tempusViewDetails.style.maxHeight = '';
        }

        if (elements.tempusViewPrev && elements.tempusViewNext) {
            elements.tempusViewPrev.style.top = '';
            elements.tempusViewNext.style.top = '';
        }

        // Add keyboard event listeners
        document.removeEventListener('keydown', handleKeyDown); // Remove any existing listener
        document.addEventListener('keydown', handleKeyDown);

        console.log('TEMPUS View opened successfully');
    }

    // Close the TEMPUS View
    function closeTempusView() {
        // Refresh DOM elements
        elements = getDOMElements();

        if (elements.tempusViewContainer) {
            elements.tempusViewContainer.classList.add('d-none');

            // Reset fullscreen mode if active
            if (isFullscreen) {
                elements.tempusViewContainer.classList.remove('tempus-view-fullscreen');
                isFullscreen = false;

                if (elements.fullscreenBtn) {
                    elements.fullscreenBtn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i>';
                    elements.fullscreenBtn.title = 'Fullscreen Mode';
                }
            }
        }

        document.body.classList.remove('tempus-view-open');
        isOpen = false;

        // Stop slideshow if it's running
        if (isSlideshow) {
            isSlideshow = false;
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
            if (elements.slideshowBtn) {
                elements.slideshowBtn.innerHTML = '<i class="fas fa-play"></i>';
                elements.slideshowBtn.title = 'Start Slideshow';
            }
        }

        // Reset zoom if active
        isZoomed = false;
        currentZoomLevel = 1;
        translateX = 0;
        translateY = 0;

        if (elements.tempusViewImage) {
            elements.tempusViewImage.classList.remove('zoomed');
            elements.tempusViewImage.style.transform = 'scale(1)';
            elements.tempusViewImage.style.transformOrigin = 'center center';
        }

        if (elements.zoomBtn) {
            elements.zoomBtn.innerHTML = '<i class="fas fa-search-plus"></i>';
            elements.zoomBtn.title = 'Zoom Image';
        }

        // Remove keyboard event listeners
        document.removeEventListener('keydown', handleKeyDown);

        console.log('TEMPUS View closed');
    }

    // Update the TEMPUS View with the current submission
    function updateTempusView() {
        console.log(`Updating TEMPUS View with submission at index ${currentIndex}`);

        // Refresh DOM elements
        elements = getDOMElements();

        // Safety check
        if (!submissions || submissions.length === 0 || currentIndex < 0 || currentIndex >= submissions.length) {
            console.error('Invalid submission data or index');
            return;
        }

        try {
            const submission = submissions[currentIndex];

            // Reset zoom state when changing images
            isZoomed = false;
            currentZoomLevel = 1;
            translateX = 0;
            translateY = 0;

            if (elements.tempusViewImage) {
                elements.tempusViewImage.classList.remove('zoomed');
                elements.tempusViewImage.style.transform = 'scale(1)';
                elements.tempusViewImage.style.transformOrigin = 'center center';

                // Update image with error handling
                if (submission.imageUrl) {
                    elements.tempusViewImage.src = submission.imageUrl;
                    elements.tempusViewImage.alt = submission.title || 'Submission Image';
                } else {
                    console.warn('No image URL for this submission');
                    elements.tempusViewImage.src = '/static/images/logo.png';
                    elements.tempusViewImage.alt = 'Image not available';
                }
            }

            // Update details with null checks
            if (elements.tempusViewTitle) elements.tempusViewTitle.textContent = submission.title || 'Untitled';
            if (elements.tempusViewArtist) elements.tempusViewArtist.textContent = submission.artist || 'Unknown Artist';
            if (elements.tempusViewCategory) elements.tempusViewCategory.textContent = submission.category || 'Uncategorized';
            if (elements.tempusViewDescription) elements.tempusViewDescription.textContent = submission.description || '';
            if (elements.tempusViewLikes) elements.tempusViewLikes.textContent = submission.likes || '0';
            if (elements.tempusViewDate) elements.tempusViewDate.textContent = submission.date || '';
            if (elements.tempusViewDetailsLink) elements.tempusViewDetailsLink.href = submission.detailsLink || '#';

            // Update navigation state
            if (elements.tempusViewPrev) elements.tempusViewPrev.classList.toggle('disabled', currentIndex === 0);
            if (elements.tempusViewNext) elements.tempusViewNext.classList.toggle('disabled', currentIndex === submissions.length - 1);

            // Preload adjacent images for smoother navigation
            preloadAdjacentImages();

            console.log('TEMPUS View updated successfully');
        } catch (error) {
            console.error('Error updating TEMPUS View:', error);
        }
    }

    // Navigate to the previous submission
    function goToPrevious() {
        if (currentIndex > 0) {
            currentIndex--;
            updateTempusView();
        }
    }

    // Navigate to the next submission
    function goToNext() {
        if (currentIndex < submissions.length - 1) {
            currentIndex++;
            updateTempusView();
        }
    }

    // Update zoom level and transform
    function updateZoom(clientX, clientY) {
        if (!isOpen) return;

        // Refresh DOM elements
        elements = getDOMElements();

        if (!elements.tempusViewImage) return;

        // Get image dimensions and position
        const rect = elements.tempusViewImage.getBoundingClientRect();

        if (currentZoomLevel === 1) {
            // Reset transform when at level 1
            elements.tempusViewImage.style.transform = 'scale(1)';
            translateX = 0;
            translateY = 0;
            isZoomed = false;

            // Reset cursor
            if (elements.tempusViewContainer) {
                elements.tempusViewContainer.style.cursor = 'default';
            }

            // Update zoom button icon
            if (elements.zoomBtn) {
                elements.zoomBtn.innerHTML = '<i class="fas fa-search-plus"></i>';
                elements.zoomBtn.title = 'Zoom Image';
            }

            // Remove zoomed class
            elements.tempusViewImage.classList.remove('zoomed');
            return;
        }

        // We're zoomed in
        isZoomed = true;

        if (clientX && clientY && currentZoomLevel > 1) {
            // Calculate relative position within the image (0 to 1)
            const relativeX = (clientX - rect.left) / rect.width;
            const relativeY = (clientY - rect.top) / rect.height;

            // Set transform origin based on click position
            elements.tempusViewImage.style.transformOrigin = `${relativeX * 100}% ${relativeY * 100}%`;
        }

        // Apply zoom and translation
        elements.tempusViewImage.style.transform = `scale(${currentZoomLevel}) translate(${translateX}px, ${translateY}px)`;

        // Add zoomed class
        elements.tempusViewImage.classList.add('zoomed');

        // Update cursor
        if (elements.tempusViewContainer) {
            elements.tempusViewContainer.style.cursor = currentZoomLevel > 1 ? 'move' : 'default';
        }

        // Update zoom button icon
        if (elements.zoomBtn) {
            elements.zoomBtn.innerHTML = '<i class="fas fa-search-minus"></i>';
            elements.zoomBtn.title = 'Reset Zoom';
        }

        // On mobile, hide controls when zoomed for better viewing
        if (isMobileDevice && !areControlsHidden && isZoomed) {
            toggleControls();
        }
    }

    // Zoom in at a specific point
    function zoomIn(clientX, clientY) {
        if (currentZoomLevel < maxZoomLevel) {
            currentZoomLevel++;
            updateZoom(clientX, clientY);
        }
    }

    // Zoom out
    function zoomOut() {
        if (currentZoomLevel > 1) {
            currentZoomLevel--;
            updateZoom();
        }
    }

    // Reset zoom
    function resetZoom() {
        currentZoomLevel = 1;
        translateX = 0;
        translateY = 0;
        updateZoom();
    }

    // Toggle zoom on the current image
    function toggleZoom(e) {
        if (!isOpen) return;

        // If we have event coordinates, use them for targeted zooming
        if (e && e.clientX && e.clientY) {
            if (!isZoomed) {
                zoomIn(e.clientX, e.clientY);
            } else {
                resetZoom();
            }
        } else {
            // Simple toggle without targeting
            if (!isZoomed) {
                currentZoomLevel = 2; // Default zoom level
                updateZoom();
            } else {
                resetZoom();
            }
        }
    }

    // Toggle fullscreen mode
    function toggleFullscreen() {
        if (!isOpen) return;

        // Refresh DOM elements
        elements = getDOMElements();

        isFullscreen = !isFullscreen;

        if (elements.tempusViewContainer) {
            elements.tempusViewContainer.classList.toggle('tempus-view-fullscreen', isFullscreen);
        }

        // Update fullscreen button icon
        if (elements.fullscreenBtn) {
            if (isFullscreen) {
                elements.fullscreenBtn.innerHTML = '<i class="fas fa-compress-arrows-alt"></i>';
                elements.fullscreenBtn.title = 'Exit Fullscreen';
            } else {
                elements.fullscreenBtn.innerHTML = '<i class="fas fa-expand-arrows-alt"></i>';
                elements.fullscreenBtn.title = 'Fullscreen Mode';
            }
        }
    }

    // Toggle slideshow mode
    function toggleSlideshow() {
        // Refresh DOM elements
        elements = getDOMElements();

        isSlideshow = !isSlideshow;

        if (isSlideshow) {
            if (elements.slideshowBtn) {
                elements.slideshowBtn.innerHTML = '<i class="fas fa-pause"></i>';
                elements.slideshowBtn.title = 'Pause Slideshow';
            }

            // Start the slideshow interval
            slideshowInterval = setInterval(() => {
                if (currentIndex < submissions.length - 1) {
                    goToNext();
                } else {
                    // Stop at the end or loop back to the beginning
                    currentIndex = -1;
                    goToNext();
                }
            }, 3000); // Change slide every 3 seconds
        } else {
            if (elements.slideshowBtn) {
                elements.slideshowBtn.innerHTML = '<i class="fas fa-play"></i>';
                elements.slideshowBtn.title = 'Start Slideshow';
            }

            // Clear the slideshow interval
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
        }
    }

    // Preload adjacent images for smoother navigation
    function preloadAdjacentImages() {
        if (submissions.length === 0) return;

        // Preload next image
        if (currentIndex < submissions.length - 1) {
            const nextImg = new Image();
            nextImg.src = submissions[currentIndex + 1].imageUrl;
        }

        // Preload previous image
        if (currentIndex > 0) {
            const prevImg = new Image();
            prevImg.src = submissions[currentIndex - 1].imageUrl;
        }
    }

    // Handle keyboard navigation
    function handleKeyDown(e) {
        if (!isOpen) return;

        switch (e.key) {
            case 'ArrowLeft':
                goToPrevious();
                break;
            case 'ArrowRight':
                goToNext();
                break;
            case 'Escape':
                if (isFullscreen) {
                    toggleFullscreen();
                } else if (areControlsHidden) {
                    toggleControls();
                } else if (isZoomed) {
                    resetZoom();
                } else {
                    closeTempusView();
                }
                break;
            case ' ': // Spacebar
                toggleSlideshow();
                e.preventDefault(); // Prevent page scrolling
                break;
            case 'z':
            case 'Z':
                toggleZoom();
                break;
            case '+':
            case '=': // = key is + without shift
                zoomIn();
                e.preventDefault(); // Prevent page zoom
                break;
            case '-':
            case '_': // _ key is - with shift
                zoomOut();
                e.preventDefault(); // Prevent page zoom
                break;
            case '0':
                resetZoom();
                break;
            case 'f':
            case 'F':
                toggleFullscreen();
                break;
            case 'h':
            case 'H':
                toggleControls();
                break;
        }
    }

    // Set up event listeners
    function setupEventListeners() {
        console.log('Setting up TEMPUS View event listeners');

        // Refresh DOM elements
        elements = getDOMElements();

        // TEMPUS View button
        if (elements.tempusViewBtn) {
            elements.tempusViewBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Update screen detection to adjust UI accordingly
                updateScreenDetection();

                // Open TEMPUS View on all screen sizes
                openTempusView();
            });
        }

        // Close button
        if (elements.tempusViewClose) {
            elements.tempusViewClose.addEventListener('click', function(e) {
                e.preventDefault();
                closeTempusView();
            });
        }

        // Previous button
        if (elements.tempusViewPrev) {
            elements.tempusViewPrev.addEventListener('click', function(e) {
                e.preventDefault();
                goToPrevious();
            });
        }

        // Next button
        if (elements.tempusViewNext) {
            elements.tempusViewNext.addEventListener('click', function(e) {
                e.preventDefault();
                goToNext();
            });
        }

        // Slideshow button
        if (elements.slideshowBtn) {
            elements.slideshowBtn.addEventListener('click', function(e) {
                e.preventDefault();
                toggleSlideshow();
            });
        }

        // Image click for zoom
        if (elements.tempusViewImage) {
            elements.tempusViewImage.addEventListener('click', function(e) {
                e.preventDefault();
                toggleZoom(e);
            });

            // Add panning functionality
            elements.tempusViewImage.addEventListener('mousedown', function(e) {
                if (currentZoomLevel > 1) {
                    e.preventDefault();
                    isPanning = true;
                    startX = e.clientX;
                    startY = e.clientY;
                    startTranslateX = translateX;
                    startTranslateY = translateY;

                    // Change cursor
                    if (elements.tempusViewContainer) {
                        elements.tempusViewContainer.style.cursor = 'grabbing';
                    }
                }
            });

            // Mouse wheel zoom
            elements.tempusViewImage.addEventListener('wheel', function(e) {
                if (isOpen) {
                    e.preventDefault();
                    if (e.deltaY < 0) {
                        // Scroll up - zoom in
                        zoomIn(e.clientX, e.clientY);
                    } else {
                        // Scroll down - zoom out
                        zoomOut();
                    }
                }
            });
        }

        // Document-level mouse events for panning
        document.addEventListener('mousemove', function(e) {
            if (isPanning && currentZoomLevel > 1) {
                e.preventDefault();
                const dx = (e.clientX - startX) / currentZoomLevel;
                const dy = (e.clientY - startY) / currentZoomLevel;
                translateX = startTranslateX + dx;
                translateY = startTranslateY + dy;

                // Apply the transform
                if (elements.tempusViewImage) {
                    elements.tempusViewImage.style.transform = `scale(${currentZoomLevel}) translate(${translateX}px, ${translateY}px)`;
                }
            }
        });

        document.addEventListener('mouseup', function() {
            if (isPanning) {
                isPanning = false;

                // Reset cursor
                if (elements.tempusViewContainer) {
                    elements.tempusViewContainer.style.cursor = 'move';
                }
            }
        });

        // Zoom button
        if (elements.zoomBtn) {
            elements.zoomBtn.addEventListener('click', function(e) {
                e.preventDefault();
                toggleZoom();
            });
        }

        // Fullscreen button
        if (elements.fullscreenBtn) {
            elements.fullscreenBtn.addEventListener('click', function(e) {
                e.preventDefault();
                toggleFullscreen();
            });
        }

        // Touch gesture support
        if (elements.tempusViewContainer) {
            elements.tempusViewContainer.addEventListener('touchstart', handleTouchStart, { passive: false });
            elements.tempusViewContainer.addEventListener('touchmove', handleTouchMove, { passive: false });
            elements.tempusViewContainer.addEventListener('touchend', handleTouchEnd, { passive: false });
        }

        // Gallery image click
        document.addEventListener('click', function(e) {
            if (e.target.tagName === 'IMG' && e.target.closest('.submission-image')) {
                e.preventDefault();

                const card = e.target.closest('.submission-card');
                if (card) {
                    // Update screen detection to adjust UI accordingly
                    updateScreenDetection();

                    // Use TEMPUS View for all screen sizes
                    const allCards = Array.from(document.querySelectorAll('.submission-card'));
                    const index = allCards.indexOf(card);
                    if (index !== -1) {
                        openTempusView(index);
                    }
                }
            }
        });
    }

    // Touch event handlers
    let touchStartX = 0;
    let touchStartY = 0;
    let initialPinchDistance = 0;
    let isPinching = false;
    let isTouchPanning = false;
    let touchStartTranslateX = 0;
    let touchStartTranslateY = 0;

    function handleTouchStart(e) {
        // Refresh DOM elements
        elements = getDOMElements();

        if (e.touches.length === 1) {
            // Single touch - track for swipe or pan
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isPinching = false;

            // If zoomed in, prepare for panning
            if (currentZoomLevel > 1 && e.target === elements.tempusViewImage) {
                isTouchPanning = true;
                touchStartTranslateX = translateX;
                touchStartTranslateY = translateY;
                e.preventDefault(); // Prevent default behavior
            }
        } else if (e.touches.length === 2) {
            // Two touches - track for pinch zoom
            isPinching = true;
            isTouchPanning = false;

            // Calculate initial distance between touches
            initialPinchDistance = Math.hypot(
                e.touches[0].clientX - e.touches[1].clientX,
                e.touches[0].clientY - e.touches[1].clientY
            );

            // Calculate center point between the two touches
            const centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2;
            const centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2;

            // Store this as our pinch center point
            touchStartX = centerX;
            touchStartY = centerY;

            e.preventDefault(); // Prevent default behavior
        }
    }

    function handleTouchMove(e) {
        if (!isOpen) return;

        // Handle pinch-to-zoom
        if (isPinching && e.touches.length === 2) {
            e.preventDefault(); // Prevent default scrolling

            // Calculate current distance between touches
            const currentDistance = Math.hypot(
                e.touches[0].clientX - e.touches[1].clientX,
                e.touches[0].clientY - e.touches[1].clientY
            );

            // Calculate center point between the two touches
            const centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2;
            const centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2;

            // Determine if pinching in or out
            if (initialPinchDistance && currentDistance) {
                const pinchRatio = currentDistance / initialPinchDistance;

                // If significant pinch detected
                if (pinchRatio > 1.1 && currentZoomLevel < maxZoomLevel) {
                    // Pinch out - zoom in
                    currentZoomLevel = Math.min(currentZoomLevel + 0.1, maxZoomLevel);
                    updateZoom(centerX, centerY);
                } else if (pinchRatio < 0.9 && currentZoomLevel > 1) {
                    // Pinch in - zoom out
                    currentZoomLevel = Math.max(currentZoomLevel - 0.1, 1);
                    updateZoom(centerX, centerY);
                }

                // Update initial distance for next move event
                initialPinchDistance = currentDistance;
            }
        }
        // Handle panning when zoomed in
        else if (isTouchPanning && e.touches.length === 1 && currentZoomLevel > 1) {
            e.preventDefault(); // Prevent default scrolling

            // Calculate the distance moved
            const dx = (e.touches[0].clientX - touchStartX) / currentZoomLevel;
            const dy = (e.touches[0].clientY - touchStartY) / currentZoomLevel;

            // Update translation
            translateX = touchStartTranslateX + dx;
            translateY = touchStartTranslateY + dy;

            // Apply the transform
            if (elements.tempusViewImage) {
                elements.tempusViewImage.style.transform = `scale(${currentZoomLevel}) translate(${translateX}px, ${translateY}px)`;
            }
        }
    }

    function handleTouchEnd(e) {
        if (!isOpen) return;

        // Reset touch panning state
        isTouchPanning = false;

        if (isPinching) {
            isPinching = false;
            return;
        }

        // Handle tap and swipe only if not pinching
        if (e.changedTouches.length === 1) {
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const swipeThreshold = 50;
            const tapThreshold = 10; // Maximum movement to consider it a tap

            // Calculate horizontal and vertical distance
            const deltaX = touchEndX - touchStartX;
            const deltaY = touchEndY - touchStartY;
            const totalMovement = Math.abs(deltaX) + Math.abs(deltaY);

            // Check if it's a tap (very little movement)
            if (totalMovement < tapThreshold) {
                // Single tap on image - toggle controls on mobile
                if (isMobileDevice && areControlsHidden) {
                    // Only show controls on tap if they're hidden
                    toggleControls();
                    return;
                } else if (isMobileDevice && e.target === elements.tempusViewImage && currentZoomLevel === 1) {
                    // If tapping directly on the image when controls are visible and not zoomed, toggle zoom
                    toggleZoom({
                        clientX: touchEndX,
                        clientY: touchEndY
                    });
                    return;
                }
            }

            // Only register as horizontal swipe if horizontal movement is greater than vertical
            // and we're not zoomed in (to avoid conflict with panning)
            if (currentZoomLevel === 1 && Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > swipeThreshold) {
                if (deltaX < 0) {
                    // Swipe left - go to next
                    goToNext();
                } else {
                    // Swipe right - go to previous
                    goToPrevious();
                }
            }
        }
    }

    // Handle orientation changes
    function handleOrientationChange() {
        if (!isOpen) return;

        // Update screen detection
        updateScreenDetection();

        // Reset zoom if active
        isZoomed = false;
        currentZoomLevel = 1;
        translateX = 0;
        translateY = 0;

        elements = getDOMElements();
        if (elements.tempusViewImage) {
            elements.tempusViewImage.classList.remove('zoomed');
            elements.tempusViewImage.style.transform = 'scale(1)';
            elements.tempusViewImage.style.transformOrigin = 'center center';
        }

        if (elements.zoomBtn) {
            elements.zoomBtn.innerHTML = '<i class="fas fa-search-plus"></i>';
            elements.zoomBtn.title = 'Zoom Image';
        }

        // On small screens in landscape, auto-hide controls for more viewing space
        if (isSmallScreen && screenWidth > screenHeight && !areControlsHidden) {
            toggleControls();
        }

        // Refresh elements
        elements = getDOMElements();

        // Let CSS handle the responsive design
        // Just ensure the image container is properly positioned
        setTimeout(() => {
            // Refresh elements
            elements = getDOMElements();

            // Reset any inline styles that might interfere with CSS
            if (elements.tempusViewImage) {
                elements.tempusViewImage.style.maxWidth = '';
                elements.tempusViewImage.style.maxHeight = '';
                elements.tempusViewImage.style.marginTop = '';
            }

            if (elements.controlsBar) {
                elements.controlsBar.style.bottom = '';
                elements.controlsBar.style.transform = '';
            }

            if (elements.tempusViewDetails) {
                elements.tempusViewDetails.style.maxHeight = '';
            }

            if (elements.tempusViewPrev && elements.tempusViewNext) {
                elements.tempusViewPrev.style.top = '';
                elements.tempusViewNext.style.top = '';
            }
        }, 100);

        console.log('Orientation changed, adjusting view');
    }

    // Update screen size detection
    function updateScreenDetection() {
        screenWidth = window.innerWidth;
        screenHeight = window.innerHeight;
        isMobileDevice = screenWidth <= 768;
        isSmallScreen = screenWidth <= 480;

        console.log(`Screen detection updated: Width: ${screenWidth}, Height: ${screenHeight}, Mobile: ${isMobileDevice}, Small: ${isSmallScreen}`);
    }

    // Initialize
    function initialize() {
        console.log('Initializing TEMPUS View');

        // Update screen detection
        updateScreenDetection();

        collectSubmissions();
        setupEventListeners();

        // Add orientation change and resize listeners
        window.addEventListener('orientationchange', function() {
            updateScreenDetection();
            handleOrientationChange();
        });
        window.addEventListener('resize', function() {
            updateScreenDetection();
            handleOrientationChange();
        });
    }

    // Call initialize on page load
    initialize();

    // Re-initialize on DOM changes
    const observer = new MutationObserver(function(mutations) {
        let shouldReinit = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && node.classList.contains('submission-card') ||
                            node.querySelector && node.querySelector('.submission-card')) {
                            shouldReinit = true;
                            break;
                        }
                    }
                }
            }
        });

        if (shouldReinit) {
            console.log('DOM changed, reinitializing TEMPUS View');
            initialize();
        }
    });

    observer.observe(document.body, { childList: true, subtree: true });

    console.log('TEMPUS View v2.0 initialized');
});
