/**
 * Collaboration Form Enhancement Script
 * 
 * This script enhances the collaboration form by:
 * 1. Adding a star rating system for book reviews
 * 2. Showing/hiding fields based on the selected collaboration type
 * 3. Customizing the message field based on the collaboration type
 */
document.addEventListener('DOMContentLoaded', function() {
    const collaborationTypeSelect = document.getElementById('id_collaboration_type');
    const bookReviewFields = document.querySelectorAll('.book-review-field');
    const messageLabel = document.querySelector('label[for="id_message"]');
    const messagePlaceholder = document.getElementById('id_message');
    const reviewNotice = document.getElementById('review-notice');

    // Function to handle star rating
    function setupStarRating() {
        const stars = document.querySelectorAll('.star-rating');
        const ratingValue = document.querySelector('.rating-value');
        const ratingInputs = document.querySelectorAll('input[name="rating"]');

        // Check if there's a pre-selected rating (e.g., from form validation error)
        const checkedInput = Array.from(ratingInputs).find(input => input.checked);
        const checkedValue = checkedInput ? parseInt(checkedInput.value) : 0;
        
        // Update the rating value display and star colors based on the pre-selected value
        if (checkedValue > 0) {
            ratingValue.textContent = checkedValue;
            stars.forEach((s, i) => {
                s.style.color = i < checkedValue ? '#FFD700' : '#ccc';
            });
        }

        stars.forEach((star, index) => {
            star.addEventListener('click', () => {
                const value = index + 1;
                ratingValue.textContent = value;
                ratingInputs[index].checked = true;
                
                // Update star colors
                stars.forEach((s, i) => {
                    s.style.color = i <= index ? '#FFD700' : '#ccc';
                });
            });

            star.addEventListener('mouseover', () => {
                stars.forEach((s, i) => {
                    s.style.color = i <= index ? '#FFD700' : '#ccc';
                });
            });

            star.addEventListener('mouseout', () => {
                const currentCheckedInput = Array.from(ratingInputs).find(input => input.checked);
                const currentCheckedValue = currentCheckedInput ? parseInt(currentCheckedInput.value) : 0;
                
                stars.forEach((s, i) => {
                    s.style.color = i < currentCheckedValue ? '#FFD700' : '#ccc';
                });
            });
        });
    }

    // Function to toggle review fields
    function toggleReviewFields() {
        const isReview = collaborationTypeSelect.value === 'REVIEW';
        
        bookReviewFields.forEach(field => {
            field.style.display = isReview ? 'block' : 'none';
        });
        
        if (isReview) {
            setupStarRating();
            if (reviewNotice) reviewNotice.style.display = 'block';
            if (messageLabel) messageLabel.textContent = 'Review Text';
            if (messagePlaceholder) messagePlaceholder.placeholder = 'Share your thoughts about the book...';
        } else {
            if (reviewNotice) reviewNotice.style.display = 'none';
            if (messageLabel) messageLabel.textContent = 'Message';
            if (messagePlaceholder) messagePlaceholder.placeholder = 'Tell me about your project or idea...';
        }
    }

    // Initial setup
    if (collaborationTypeSelect) {
        toggleReviewFields();
        collaborationTypeSelect.addEventListener('change', toggleReviewFields);
    }
});

/**
 * Sets up a star rating system for the given field
 * @param {HTMLElement} ratingField - The form group containing the rating field
 */
function setupStarRating(ratingField) {
    // Remove any existing star containers to avoid duplication
    const existingContainer = ratingField.querySelector('.stars-container');
    if (existingContainer) {
        existingContainer.remove();
    }
    
    // Find the radio inputs
    const ratingInputs = ratingField.querySelectorAll('input[type="radio"]');
    
    // If no radio inputs found, exit
    if (!ratingInputs || ratingInputs.length === 0) {
        console.error('No rating inputs found');
        return;
    }
    
    // Add a style tag to hide the original radio buttons and labels
    const style = document.createElement('style');
    style.textContent = `
        .form-group ul {
            display: none !important;
        }
        .rating-input {
            display: none !important;
        }
        /* Make sure select elements are visible */
        .form-group select {
            display: block !important;
        }
    `;
    document.head.appendChild(style);
    
    // Create a container for the stars
    const starsContainer = document.createElement('div');
    starsContainer.className = 'stars-container';
    starsContainer.style.display = 'flex';
    starsContainer.style.alignItems = 'center';
    starsContainer.style.gap = '10px';
    starsContainer.style.marginTop = '10px';
    
    // Check if there's a pre-selected rating
    let selectedValue = 0;
    ratingInputs.forEach((input) => {
        if (input.checked) {
            selectedValue = parseInt(input.value);
        }
    });
    
    // Add stars for each rating option
    ratingInputs.forEach((input, index) => {
        // Create star element
        const star = document.createElement('label');
        star.htmlFor = input.id;
        star.className = 'star-rating';
        star.innerHTML = '★';
        star.style.fontSize = '28px';
        star.style.cursor = 'pointer';
        star.style.color = index < selectedValue ? '#FFD700' : '#ccc';
        star.style.transition = 'color 0.2s';
        star.dataset.value = input.value;
        
        // Add hover effect
        star.addEventListener('mouseover', function() {
            // Highlight this star and all stars before it
            const stars = starsContainer.querySelectorAll('.star-rating');
            for (let i = 0; i <= index; i++) {
                stars[i].style.color = '#FFD700';
            }
        });
        
        star.addEventListener('mouseout', function() {
            // Reset stars if not selected
            const stars = starsContainer.querySelectorAll('.star-rating');
            
            // Find the currently selected value
            let currentSelectedValue = 0;
            ratingInputs.forEach((input) => {
                if (input.checked) {
                    currentSelectedValue = parseInt(input.value);
                }
            });
            
            stars.forEach((s, i) => {
                s.style.color = i < currentSelectedValue ? '#FFD700' : '#ccc';
            });
        });
        
        // Handle click
        star.addEventListener('click', function() {
            input.checked = true;
            const value = index + 1;
            
            // Reset all stars
            const stars = starsContainer.querySelectorAll('.star-rating');
            
            // Highlight selected stars
            stars.forEach((s, i) => {
                s.style.color = i < value ? '#FFD700' : '#ccc';
            });
            
            // Update rating value text
            ratingValue.textContent = value;
        });
        
        // Add star to container
        starsContainer.appendChild(star);
    });
    
    // Add rating value text
    const ratingValue = document.createElement('span');
    ratingValue.className = 'rating-value';
    ratingValue.textContent = selectedValue || '0';
    ratingValue.style.marginLeft = '10px';
    ratingValue.style.fontSize = '18px';
    starsContainer.appendChild(ratingValue);
    
    // Add the stars container to the rating field
    ratingField.appendChild(starsContainer);
}
