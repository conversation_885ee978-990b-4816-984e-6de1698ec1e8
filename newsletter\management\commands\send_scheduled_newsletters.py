from django.core.management.base import BaseCommand
from django.utils import timezone
from newsletter.models import Newsletter


class Command(BaseCommand):
    help = 'Sends all scheduled newsletters that are due'

    def handle(self, *args, **options):
        now = timezone.now()
        self.stdout.write(f"Current time: {now}")
        
        scheduled_newsletters = Newsletter.objects.filter(
            status='scheduled',
            scheduled_for__lte=now
        )
        
        self.stdout.write(f"Found {scheduled_newsletters.count()} newsletters to send")
        
        for newsletter in scheduled_newsletters:
            self.stdout.write(f"Attempting to send newsletter: {newsletter.subject} (scheduled for {newsletter.scheduled_for})")
            success, message = newsletter.send_to_all()
            
            if success:
                self.stdout.write(
                    self.style.SUCCESS(f"Successfully sent newsletter: {message}")
                )
            else:
                self.stdout.write(
                    self.style.ERROR(f"Failed to send newsletter: {message}")
                )
