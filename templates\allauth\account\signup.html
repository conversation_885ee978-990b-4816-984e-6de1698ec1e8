{% extends "account/base_entrance.html" %}
{% load allauth i18n static %}
{% block extra_css %}
    {{ block.super }}
    <link rel="stylesheet" href="{% static 'css/signup.css' %}">
    <style>
        .invalid-feedback {
            display: none;
            width: 100%;
            margin-top: 0.25rem;
            font-size: 0.875em;
            color: #dc3545;
        }
        .is-invalid {
            border-color: #dc3545;
            padding-right: calc(1.5em + 0.75rem);
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }
    </style>
{% endblock %}

{% block head_title %}
    {% trans "Sign Up" %}
{% endblock head_title %}

{% block inner_content %}
    <div class="container-fluid px-3 ">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-6 col-xl-9 bg-black bg-opacity-75 p-3 rounded mb-5">
                {% element h1 %}
                    {% trans "Sign Up" %}
                {% endelement %}
                
                <!-- Community Rules Section -->
                <div class="card mb-4" role="region" aria-label="Community Guidelines">
                    <div class="card-header">
                        <h5 class="mb-0">Community Guidelines</h5>
                    </div>
                    <div class="card-body">
                        <p class="card-text">Before joining our community, please read and agree to our guidelines:</p>
                        <ul class="list-group list-group-flush" role="list">
                            <li class="list-group-item d-flex align-items-start" role="listitem">
                                <i class="fas fa-check text-success me-2 mt-1" aria-hidden="true"></i>
                                <span>Be respectful and courteous to other members</span>
                            </li>
                            <li class="list-group-item d-flex align-items-start" role="listitem">
                                <i class="fas fa-check text-success me-2 mt-1" aria-hidden="true"></i>
                                <span>Share authentic and original content</span>
                            </li>
                            <li class="list-group-item d-flex align-items-start" role="listitem">
                                <i class="fas fa-check text-success me-2 mt-1" aria-hidden="true"></i>
                                <span>Protect your account information</span>
                            </li>
                            <li class="list-group-item d-flex align-items-start" role="listitem">
                                <i class="fas fa-check text-success me-2 mt-1" aria-hidden="true"></i>
                                <span>Report any suspicious activity</span>
                            </li>
                        </ul>
                        <p class="mt-3 text-muted small">
                            By creating an account, you agree to follow these guidelines and our 
                            <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#tosModal" aria-label="View Terms of Service">Terms of Service</a> and 
                            <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#privacyModal" aria-label="Our short Terms of Service ">Privacy Policy</a>.
                        </p>
                    </div>
                </div>

                {% setvar link %}
                    <a href="{{ login_url }}" aria-label="Go to sign in page">
                    {% endsetvar %}
                    {% setvar end_link %}
                    </a>
                {% endsetvar %}
                {% element p %}
                    {% blocktranslate %}Already have an account? Then please {{ link }}sign in{{ end_link }}.{% endblocktranslate %}
                {% endelement %}

                {% if not SOCIALACCOUNT_ONLY %}
                    {% url 'account_signup' as action_url %}
                    {% element form form=form method="post" action=action_url tags="entrance,signup" id="signupForm" %}
                        {% slot body %}
                            {% csrf_token %}
                            {% element fields form=form unlabeled=True %}
                            {% endelement %}
                            {{ redirect_field }}
                            
                            <!-- Privacy Policy Checkbox -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="privacyCheck" required>
                                    <label class="form-check-label" for="privacyCheck">
                                        I have read and agree to the 
                                        <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Newsletter Signup -->
                            <div class="mb-4 mt-4">
                                <div class="form-check p-3 bg-dark rounded border border-primary border-opacity-25 newsletter-box text-white">
                                    <input type="checkbox" class="form-check-input" id="newsletterSignup" name="newsletter_signup"
                                           data-form-url="{{ GOOGLE_FORM_URL }}" data-email-field="input[name='email']">
                                    <label class="form-check-label fw-semibold" for="newsletterSignup">
                                        <i class="fas fa-envelope-open-text me-2 text-primary"></i>
                                        Sign up to the TEMPUS mailing list to receive updates and exclusive content (optional)
                                    </label>
                                    <!-- Loading Spinner (hidden by default) -->
                                    <div id="newsletterSpinner" class="d-none text-primary mt-2">
                                        <div class="spinner-border spinner-border-sm me-2" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <small>Subscribing to newsletter...</small>
                                    </div>
                                </div>
                            </div>
                            <!-- Newsletter Toast Notification -->
                            <div class="toast-container position-fixed bottom-0 end-0 p-3">
                                <div id="newsletterToast" class="toast align-items-center text-bg-primary border-0" role="alert" aria-live="assertive" aria-atomic="true">
                                    <div class="d-flex">
                                        <div class="toast-body">
                                            <i class="fas fa-check-circle me-2"></i>Successfully subscribed to the TEMPUS newsletter!
                                        </div>
                                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                                    </div>
                                </div>
                            </div>
                        {% endslot %}
                        {% slot actions %}
                            <div class="mt-4 mb-4 signup-button-wrapper">
                                <div class="col-12 col-md-8 col-lg-6 mx-auto">
                                    {% element button tags="prominent,signup" type="submit" %}
                                        <div class="d-flex align-items-center justify-content-center py-1 w-100">
                                            <i class="fas fa-user-plus me-2"></i>
                                            <span class="fw-bold">{% trans "Sign Up" %}</span>
                                        </div>
                                    {% endelement %}
                                </div>
                            </div>
                        {% endslot %}
                    {% endelement %}
                {% endif %}
                
                <!-- Privacy Policy Modal -->
                <div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-scrollable modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="privacyModalLabel">Privacy Policy</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                {% include 'privacy-policy.html' %}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                <button type="button" class="btn btn-primary" data-bs-dismiss="modal" onclick="document.getElementById('privacyCheck').checked = true;">Accept</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Terms of Service Modal -->
                <div class="modal fade" id="tosModal" tabindex="-1" aria-labelledby="tosModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="tosModalLabel">Terms of Service</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <h6 class="mb-3">By joining our community, you agree to:</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Follow our community guidelines
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Use the platform responsibly and legally
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-check-circle text-success me-2"></i>
                                        Understand that we are not liable for user-generated content
                                    </li>
                                    <li class="list-group-item d-flex align-items-center">
                                        <i class="fas fa-exclamation-circle text-warning me-2"></i>
                                        Acknowledge that violations may result in account suspension or termination
                                    </li>
                                </ul>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">I Understand</button>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if PASSKEY_SIGNUP_ENABLED %}
                    {% element hr %}
                    {% endelement %}
                    {% element button href=signup_by_passkey_url tags="prominent,signup,outline,primary" %}
                        {% trans "Sign up using a passkey" %}
                    {% endelement %}
                {% endif %}
                {% if SOCIALACCOUNT_ENABLED %}
                    {% include "socialaccount/snippets/login.html" with page_layout="entrance" %}
                {% endif %}
            </div>
        </div>
    </div>
{% endblock inner_content %}

{% block extra_js %}
    {{ block.super }}
    {% load static %}
    <script src="{% static 'js/signup_script.js' %}"></script>
{% endblock %}