# Collaboration Guidelines

## Communication Best Practices

### For Project Owners

1. **Provide Clear Requirements**
   - Clearly state what you want to achieve
   - Include specific examples when possible
   - Mention any constraints or limitations

2. **Share Context**
   - Explain the purpose behind the request
   - Provide background information about the project
   - Share any relevant design decisions or architectural considerations

3. **Prioritize Tasks**
   - Indicate which aspects are most important
   - Specify what's essential vs. what's nice-to-have
   - Set clear expectations about deadlines

4. **Give Feedback**
   - Be specific about what works and what doesn't
   - Explain why something isn't meeting expectations
   - Acknowledge good work and solutions

5. **Ask Questions**
   - If you're unsure about a recommendation, ask for clarification
   - Request explanations for technical decisions
   - Seek alternatives if a proposed solution doesn't seem right

### For Developers (AI or Human)

1. **Understand Before Coding**
   - Make sure you understand the requirements before implementing
   - Ask clarifying questions when needed
   - Summarize your understanding before proceeding

2. **Explain Your Approach**
   - Outline your plan before implementing it
   - Explain the reasoning behind technical decisions
   - Mention alternatives considered and why they were rejected

3. **Provide Context in Code**
   - Write clear comments explaining complex logic
   - Document assumptions and edge cases
   - Include references to relevant documentation or resources

4. **Communicate Challenges**
   - Be upfront about potential difficulties
   - Suggest workarounds for limitations
   - Explain trade-offs in different approaches

5. **Follow Up**
   - Check if the solution meets the requirements
   - Offer suggestions for improvements
   - Provide documentation for future reference

## Code Style Guidelines

### General Principles

1. **Consistency**
   - Follow established patterns in the codebase
   - Use consistent naming conventions
   - Maintain consistent formatting

2. **Readability**
   - Write code that is easy to understand
   - Use descriptive variable and function names
   - Break complex operations into smaller, well-named functions

3. **Maintainability**
   - Write code that is easy to modify and extend
   - Avoid deep nesting and complex conditionals
   - Keep functions focused on a single responsibility

### Django-Specific Guidelines

1. **Models**
   - Include docstrings explaining the purpose of each model
   - Define `__str__` methods for all models
   - Use descriptive field names
   - Set appropriate `related_name` for relationships

2. **Views**
   - Keep views focused on a single responsibility
   - Use class-based views for complex functionality
   - Include docstrings explaining the purpose and behavior
   - Handle errors gracefully

3. **Templates**
   - Use template inheritance to avoid duplication
   - Keep logic in templates minimal
   - Use consistent naming for template blocks
   - Include comments for complex template sections

4. **URLs**
   - Use descriptive URL names
   - Organize URLs logically
   - Use namespaces for app-specific URLs

5. **Forms**
   - Include validation messages
   - Use appropriate widgets
   - Provide helpful error messages

### JavaScript Guidelines

1. **Organization**
   - Use modules to organize code
   - Keep functions small and focused
   - Use consistent event handling patterns

2. **DOM Manipulation**
   - Minimize direct DOM manipulation
   - Use event delegation where appropriate
   - Cache DOM selections for performance

3. **Error Handling**
   - Include try/catch blocks for error-prone operations
   - Provide meaningful error messages
   - Gracefully degrade functionality when errors occur

### CSS/SCSS Guidelines

1. **Organization**
   - Use a consistent methodology (BEM, SMACSS, etc.)
   - Organize styles logically
   - Use variables for colors, spacing, etc.

2. **Responsiveness**
   - Design mobile-first when appropriate
   - Use media queries consistently
   - Test on multiple screen sizes

3. **Performance**
   - Minimize specificity
   - Avoid deeply nested selectors
   - Use efficient selectors

## Documentation Standards

### Code Documentation

1. **Docstrings**
   - Include docstrings for all functions, classes, and methods
   - Explain parameters, return values, and exceptions
   - Provide examples for complex functionality

2. **Comments**
   - Comment complex logic
   - Explain why, not what
   - Keep comments up-to-date with code changes

3. **README Files**
   - Include setup instructions
   - Document dependencies
   - Provide usage examples

### Project Documentation

1. **Architecture Documentation**
   - Document system components and their interactions
   - Explain design decisions
   - Include diagrams when helpful

2. **User Documentation**
   - Provide clear instructions for end-users
   - Include screenshots
   - Document common workflows

3. **API Documentation**
   - Document endpoints, parameters, and responses
   - Include authentication requirements
   - Provide example requests and responses

## Feedback on Our Collaboration

### What Worked Well

1. **Clear Communication**
   - You provided detailed requirements and context
   - You were responsive to questions and suggestions
   - You gave specific feedback on implementations

2. **Iterative Approach**
   - We tackled problems step by step
   - We refined solutions based on feedback
   - We built on previous work effectively

3. **Problem Solving**
   - We identified root causes of issues
   - We explored multiple solutions when needed
   - We prioritized fixing critical issues first

### Areas for Improvement

1. **Requirements Gathering**
   - Sometimes requirements evolved during implementation
   - More upfront discussion of edge cases would be helpful
   - Clearer prioritization would help focus efforts

2. **Testing**
   - More systematic testing of changes
   - Better documentation of test cases
   - More thorough testing of edge cases

3. **Documentation**
   - More consistent documentation of changes
   - Better organization of project documentation
   - More comprehensive user documentation

## Augmented Code Guidelines

When sharing code snippets in discussions, use the following format to improve readability and context:

```
<augment_code_snippet path="path/to/file.py" mode="EXCERPT">
```python
def example_function():
    """
    This is an example function.
    """
    return "Hello, world!"
```
</augment_code_snippet>
```

This format provides:
- Clear indication of the file path
- Syntax highlighting
- Context about the code (excerpt, full file, etc.)

For larger code discussions, consider:
1. Creating a separate document with the code
2. Using line numbers for reference
3. Highlighting specific sections that need attention

## Conclusion

Effective collaboration requires clear communication, consistent coding practices, and thorough documentation. By following these guidelines, we can work together more efficiently and produce higher-quality results. These guidelines should evolve over time based on project needs and team feedback.
