from django.contrib import admin
from .models import Post, Comment, Category, PostImage
from django.utils.html import format_html
from taggit.models import Tag
from django import forms
from django.utils.text import slugify
from .forms import PostImageForm
from image_cropping import ImageCroppingMixin


class TagWidget(forms.SelectMultiple):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.attrs['class'] = 'django-select2'
        self.attrs['data-placeholder'] = 'Type to search or add new tags...'
        self.attrs['data-tags'] = 'true'
        self.attrs['multiple'] = 'multiple'

    def value_from_datadict(self, data, files, name):
        values = super().value_from_datadict(data, files, name)
        if not values:
            return []
        # Convert all values to strings and clean them
        clean_values = []
        for value in values:
            if value:
                clean_value = str(value).strip('[]\'\"').strip()
                if clean_value:
                    clean_values.append(clean_value)
        return clean_values

    def format_value(self, value):
        if not value:
            return []
        if isinstance(value, str):
            return [value]
        return value


class PostImageInline(admin.TabularInline):
    """
    Inline admin for PostImage model, allows adding multiple images to a post.
    """
    model = PostImage
    form = PostImageForm
    extra = 1
    fields = ('image', 'image_preview', 'alt_text', 'caption', 'width', 'alignment')
    readonly_fields = ('image_preview',)
    template = 'admin/blog/postimage/tabular.html'
    
    def image_preview(self, obj):
        if obj and obj.image:
            return format_html(
                '<img src="{}" style="max-height: 100px;" />', 
                obj.image.url
            )
        return "No image"
    image_preview.short_description = 'Preview'


class PostAdminForm(forms.ModelForm):
    tags = forms.CharField(
        required=False,
        widget=TagWidget
    )

    class Meta:
        model = Post
        fields = '__all__'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            self.initial['tags'] = [tag.name for tag in self.instance.tags.all()]

    def clean_tags(self):
        tags = self.cleaned_data.get('tags', [])
        if not tags:
            return []

        # Convert string to list if needed
        if isinstance(tags, str):
            tags = [tag.strip() for tag in tags.split(',') if tag.strip()]

        # Ensure all tags are strings and unique
        clean_tags = []
        seen = set()
        for tag in tags:
            if tag:
                tag_name = str(tag).strip('[]\'\"').strip()
                if tag_name and tag_name.lower() not in seen:
                    # Create or get the tag
                    tag_slug = slugify(tag_name)
                    tag_obj, _ = Tag.objects.get_or_create(
                        slug=tag_slug,
                        defaults={'name': tag_name}
                    )
                    clean_tags.append(tag_obj)
                    seen.add(tag_name.lower())
        return clean_tags

    def save(self, commit=True):
        instance = super().save(commit=False)
        if commit:
            instance.save()
            instance.tags.set(self.cleaned_data.get('tags', []))
        return instance


class PostAdmin(ImageCroppingMixin, admin.ModelAdmin):
    """
    Admin configuration for Post model.
    """
    form = PostAdminForm
    list_display = ('post_title', 'post_slug', 'post_status', 'post_created_on')
    list_filter = ('post_status', 'post_created_on')
    search_fields = ['post_title', 'post_content']
    prepopulated_fields = {'post_slug': ('post_title',)}
    date_hierarchy = 'post_created_on'
    inlines = [PostImageInline]

    fieldsets = (
        ('Post Information', {
            'fields': ('post_title', 'post_slug', 'post_content', 'post_excerpt')
        }),
        ('Media', {
            'description': (
                'Upload an image and use the cropping tools below to set how it appears in different contexts. '
                'Recommended minimum size: 1200x800 pixels for best quality.'
            ),
            'fields': (
                'post_featured_image',
                'featured_cropping',
                'card_cropping',
                'detail_cropping'
            )
        }),
        ('Categorization', {
            'fields': ('category', 'tags'),
            'description': 'Type to search existing tags or create new ones. Press Enter to add a new tag.',
            'classes': ('wide',)
        }),
        ('Publishing', {
            'fields': ('post_status', 'post_author'),
        }),
    )

    class Media:
        css = {
            'all': (
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
                'https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css',
                'blog/css/select2-dark.css',
            )
        }
        js = (
            'blog/js/select2-init.js',
            'blog/js/post-images.js',
        )

    def get_tags(self, obj):
        tags = obj.tags.all()
        return ", ".join(tag.name for tag in tags) if tags else "-"
    get_tags.short_description = 'Tags'

    def preview_image(self, obj):
        if obj.post_featured_image:
            return format_html('<img src="{}" style="max-height: 50px;"/>', obj.post_featured_image.url)
        return ""
    preview_image.short_description = 'Preview'

    def get_form(self, request, obj=None, **kwargs):
        form = super().get_form(request, obj, **kwargs)
        if obj:
            form.base_fields['tags'].initial = [tag.name for tag in obj.tags.all()]
        return form

    def change_view(self, request, object_id, form_url='', extra_context=None):
        """
        Add extra context for post images HTML snippets and tags
        """
        extra_context = extra_context or {}
        
        # Get current post object
        obj = self.get_object(request, object_id)
        if obj:
            # Get current post's tags
            current_tags = [tag.name for tag in obj.tags.all()]
            # Get all available tags for dropdown
            all_tags = list(Tag.objects.values_list('name', flat=True))
            
            # Get all associated images
            images = obj.content_images.all()
            image_snippets = []
            
            # Generate HTML snippets for each image
            for img in images:
                image_snippets.append({
                    'id': img.id,
                    'html': img.get_html(),
                })
            
            # Update context with both tags and image snippets
            extra_context.update({
                'existing_tags': all_tags,  # All tags for dropdown options
                'current_tags': current_tags,  # Only this post's tags
                'image_snippets': image_snippets
            })
        
        return super().change_view(request, object_id, form_url, extra_context)

    def add_view(self, request, form_url='', extra_context=None):
        extra_context = extra_context or {}
        # For new posts, just provide all tags for dropdown
        extra_context.update({
            'existing_tags': list(Tag.objects.values_list('name', flat=True)),
            'current_tags': []
        })
        return super().add_view(request, form_url, extra_context)

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        if form.cleaned_data.get('tags'):
            obj.tags.clear()
            for tag_name in form.cleaned_data['tags']:
                tag, _ = Tag.objects.get_or_create(
                    name=tag_name,
                    defaults={'slug': slugify(tag_name)}
                )
                obj.tags.add(tag)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'post_count')
    prepopulated_fields = {'slug': ('name',)}
    search_fields = ('name', 'description')

    def post_count(self, obj):
        return obj.posts.count()
    post_count.short_description = 'Number of Posts'

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('author', 'post', 'created_on', 'active')
    list_filter = ('active', 'created_on')
    search_fields = ('author__username', 'content')
    actions = ['approve_comments', 'hide_comments']

    def approve_comments(self, request, queryset):
        queryset.update(active=True)
    approve_comments.short_description = "Approve selected comments"

    def hide_comments(self, request, queryset):
        queryset.update(active=False)
    hide_comments.short_description = "Hide selected comments"


class TagAdmin(admin.ModelAdmin):
    list_display = ('name', 'slug', 'post_count')
    search_fields = ('name',)
    prepopulated_fields = {'slug': ('name',)}

    def post_count(self, obj):
        return Post.objects.filter(tags=obj).count()
    post_count.short_description = 'Number of Posts'

    def save_model(self, request, obj, form, change):
        if not obj.slug:
            obj.slug = slugify(obj.name)
        super().save_model(request, obj, form, change)


try:
    admin.site.unregister(Tag)
except admin.sites.NotRegistered:
    pass
admin.site.register(Tag, TagAdmin)
admin.site.register(Post, PostAdmin)
