// Enhanced Audio Caching Service Worker
console.log('🎵 Enhanced Audio Caching Service Worker v2.0 loading...');

// Cache names
const AUDIO_CACHE = 'audio-cache-v2';
const STATIC_CACHE = 'static-cache-v2';
const HTML_CACHE = 'html-cache-v1';
const API_CACHE = 'api-cache-v1';
const VERSION = '2.0.0';

// Offline page path
const OFFLINE_PAGE = '/offline/';

// Resources we want to cache when service worker is installed
const STATIC_RESOURCES = [
  '/',
  '/works/',
  '/library/',
  '/static/css/style.css',
  '/static/works/css/audio_player.css',
  '/static/works/js/audio_player.js',
  '/static/works/js/offline_audio_manager.js',
  '/static/works/js/media_session_manager.js',
  '/static/js/main.js',
  '/static/js/sw_manager.js',
  '/manifest.json',
  '/static/images/logo.png',
  '/static/images/default-cover.jpg',
  '/static/images/offline.svg',
  OFFLINE_PAGE,
  // Bootstrap and other common libraries
  '/static/css/bootstrap.min.css',
  '/static/js/bootstrap.bundle.min.js',
  '/static/js/jquery-3.6.0.min.js',
  // Font Awesome
  '/static/css/fontawesome.min.css',
  '/static/webfonts/fa-solid-900.woff2',
  '/static/webfonts/fa-regular-400.woff2'
];

// Audio file extensions
const AUDIO_EXTENSIONS = ['.mp3', '.m4a', '.ogg', '.wav', '.flac', '.aac'];

// Image file extensions
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];

// Check if a URL is for an audio file
function isAudioFile(url) {
  return AUDIO_EXTENSIONS.some(ext => url.toLowerCase().endsWith(ext));
}

// Check if a URL is for an image file
function isImageFile(url) {
  return IMAGE_EXTENSIONS.some(ext => url.toLowerCase().endsWith(ext));
}

// Check if URL should be excluded from caching
const NEVER_CACHE_PATHS = [
  '/accounts/',
  '/profile/',
  '/admin/',
  '/checkout/',
  '/auth/',
  '/cart/',
  '/api/auth/',
  '/sitemap.xml'
];

// API paths that can be cached
const CACHEABLE_API_PATHS = [
  '/api/works/',
  '/api/library/'
];

// CDN domains to exclude (bypass service worker)
const BYPASS_DOMAINS = [
  'code.jquery.com',
  'cdn.jsdelivr.net',
  'cdnjs.cloudflare.com',
  'fonts.gstatic.com',
  'fonts.googleapis.com'
];

function shouldExcludeFromCache(url) {
  const urlObj = new URL(url);
  
  // Exclude by path
  if (NEVER_CACHE_PATHS.some(path => url.includes(path))) {
    return true;
  }
  
  // Exclude CDN domains
  if (BYPASS_DOMAINS.some(domain => urlObj.hostname.includes(domain))) {
    return true;
  }
  
  return false;
}

// Check if URL is from S3
function isS3Url(url) {
  return url.includes('s3.') || url.includes('tempus-valleyberg-bucket');
}

// Check if URL is an API endpoint
function isApiUrl(url) {
  return url.includes('/api/');
}

// Check if API URL is cacheable
function isCacheableApiUrl(url) {
  return CACHEABLE_API_PATHS.some(path => url.includes(path));
}

// Extract filename from URL
function getFilenameFromUrl(url) {
  const urlObj = new URL(url);
  const pathParts = urlObj.pathname.split('/');
  return pathParts[pathParts.length - 1];
}

// Get cache storage estimate
async function getCacheStorageEstimate() {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        usage: estimate.usage || 0,
        quota: estimate.quota || 0,
        percent: estimate.quota ? Math.round((estimate.usage / estimate.quota) * 100) : 0
      };
    } catch (error) {
      console.error('Error estimating storage:', error);
      return { usage: 0, quota: 0, percent: 0 };
    }
  }
  return { usage: 0, quota: 0, percent: 0 };
}

// Format bytes to human-readable format
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Broadcast cache status to all clients
async function broadcastCacheStatus() {
  try {
    const clients = await self.clients.matchAll();
    if (clients.length === 0) return;
    
    const audioCache = await caches.open(AUDIO_CACHE);
    const audioKeys = await audioCache.keys();
    
    const staticCache = await caches.open(STATIC_CACHE);
    const staticKeys = await staticCache.keys();
    
    const htmlCache = await caches.open(HTML_CACHE);
    const htmlKeys = await htmlCache.keys();
    
    const storageEstimate = await getCacheStorageEstimate();
    
    clients.forEach(client => {
      client.postMessage({
        type: 'CACHE_STATUS',
        audioFiles: audioKeys.length,
        staticFiles: staticKeys.length,
        htmlFiles: htmlKeys.length,
        storage: {
          used: formatBytes(storageEstimate.usage),
          total: formatBytes(storageEstimate.quota),
          percent: storageEstimate.percent
        },
        version: VERSION
      });
    });
  } catch (error) {
    console.error('Error broadcasting cache status:', error);
  }
}

// Install event - cache static resources
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('📦 Service Worker: Caching static files');
        return cache.addAll(STATIC_RESOURCES);
      })
      .then(() => {
        console.log('✅ Service Worker: Installation complete');
        return self.skipWaiting();
      })
      .catch(error => {
        console.error('❌ Service Worker: Installation failed:', error);
        // Continue installation even if some resources fail to cache
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker: Activating...');
  
  const currentCaches = [AUDIO_CACHE, STATIC_CACHE, HTML_CACHE, API_CACHE];
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return cacheNames.filter(cacheName => !currentCaches.includes(cacheName));
      })
      .then(cachesToDelete => {
        return Promise.all(cachesToDelete.map(cacheToDelete => {
          console.log(`🗑️ Service Worker: Deleting old cache: ${cacheToDelete}`);
          return caches.delete(cacheToDelete);
        }));
      })
      .then(() => {
        console.log('✅ Service Worker: Activation complete');
        // Broadcast initial cache status
        return broadcastCacheStatus();
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Message event - handle messages from clients
self.addEventListener('message', (event) => {
  console.log('📨 Service Worker: Received message', event.data);
  
  if (!event.data) return;
  
  // Handle skipWaiting command
  if (event.data.command === 'skipWaiting') {
    self.skipWaiting();
    return;
  }
  
  // Handle cache initialization request
  if (event.data.type === 'INIT_CACHE') {
    const urls = event.data.urls || [];
    if (urls.length > 0) {
      event.waitUntil(
        caches.open(STATIC_CACHE)
          .then(cache => {
            console.log('📦 Pre-caching specific resources:', urls);
            return cache.addAll(urls);
          })
          .then(() => {
            // Notify the client that caching is complete
            event.source.postMessage({
              type: 'CACHE_COMPLETE',
              urls: urls
            });
          })
          .catch(error => {
            console.error('❌ Pre-caching failed:', error);
            // Notify the client of the error
            event.source.postMessage({
              type: 'CACHE_ERROR',
              error: error.message
            });
          })
      );
    }
    return;
  }
  
  // Handle cache status request
  if (event.data.type === 'GET_CACHE_STATUS') {
    event.waitUntil(
      broadcastCacheStatus()
    );
    return;
  }
  
  // Handle clear cache request
  if (event.data.type === 'CLEAR_CACHE') {
    const cacheType = event.data.cacheType;
    
    if (cacheType === 'all') {
      event.waitUntil(
        caches.keys()
          .then(cacheNames => {
            return Promise.all(
              cacheNames.map(cacheName => caches.delete(cacheName))
            );
          })
          .then(() => {
            event.source.postMessage({
              type: 'CACHE_CLEARED',
              cacheType: 'all'
            });
            return broadcastCacheStatus();
          })
      );
    } else if (['audio', 'static', 'html', 'api'].includes(cacheType)) {
      const cacheName = {
        'audio': AUDIO_CACHE,
        'static': STATIC_CACHE,
        'html': HTML_CACHE,
        'api': API_CACHE
      }[cacheType];
      
      event.waitUntil(
        caches.delete(cacheName)
          .then(() => {
            event.source.postMessage({
              type: 'CACHE_CLEARED',
              cacheType: cacheType
            });
            return broadcastCacheStatus();
          })
      );
    }
  }
});

// Fetch event - respond with cached data or network
self.addEventListener('fetch', (event) => {
  const url = event.request.url;
  
  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }
  
  // Skip paths that should never be cached
  if (shouldExcludeFromCache(url)) {
    return;
  }
  
  // Special handling for audio files
  if (isAudioFile(url)) {
    event.respondWith(handleAudioFetch(event.request, url));
    return;
  }
  
  // Special handling for HTML requests
  if (event.request.headers.get('Accept') && 
      event.request.headers.get('Accept').includes('text/html') &&
      !shouldExcludeFromCache(url)) {
    
    event.respondWith(handleHtmlFetch(event.request, url));
    return;
  }
  
  // Special handling for API requests
  if (isApiUrl(url) && isCacheableApiUrl(url)) {
    event.respondWith(handleApiRequest(event.request, url));
    return;
  }
  
  // Special handling for image files
  if (isImageFile(url)) {
    event.respondWith(handleImageFetch(event.request, url));
    return;
  }
  
  // For all other requests, try the cache first, then network
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }
        
        return fetch(event.request)
          .then((networkResponse) => {
            // Cache successful responses for our domain only
            if (networkResponse && networkResponse.ok && !shouldExcludeFromCache(url)) {
              const responseToCache = networkResponse.clone();
              caches.open(STATIC_CACHE)
                .then((cache) => {
                  cache.put(event.request, responseToCache);
                });
            }
            return networkResponse;
          })
          .catch((error) => {
            console.error('❌ Network fetch failed for', url, ':', error);
            
            // Special handling for font files
            if (url.match(/\.(woff2?|ttf|eot|otf)$/i)) {
              return new Response('', { 
                status: 200, 
                headers: { 'Content-Type': 'application/font-woff' } 
              });
            }
            
            // For script files, return empty script to prevent errors
            if (url.match(/\.(js)$/i)) {
              return new Response('console.log("Script fallback from service worker");', { 
                status: 200, 
                headers: { 'Content-Type': 'application/javascript' } 
              });
            }
            
            // For CSS files, return empty CSS to prevent errors
            if (url.match(/\.(css)$/i)) {
              return new Response('/* CSS fallback from service worker */', { 
                status: 200, 
                headers: { 'Content-Type': 'text/css' } 
              });
            }
            
            // Default error response
            return new Response('Network error occurred', {
              status: 503,
              statusText: 'Service Unavailable',
              headers: { 'Content-Type': 'text/plain' }
            });
          });
      })
  );
});

// Handle HTML fetch with network-first strategy
async function handleHtmlFetch(request, url) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache the response
    const responseToCache = networkResponse.clone();
    const cache = await caches.open(HTML_CACHE);
    await cache.put(request, responseToCache);
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed for HTML, trying cache:', url);
    
    // If network fails, try the cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If not in cache, return the offline page
    console.log('Page not in cache, serving offline page');
    return caches.match(OFFLINE_PAGE) || caches.match('/');
  }
}

// Handle API requests with network-first strategy and caching
async function handleApiRequest(request, url) {
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Only cache successful responses
    if (networkResponse.ok) {
      const responseToCache = networkResponse.clone();
      const cache = await caches.open(API_CACHE);
      await cache.put(request, responseToCache);
      
      // Log successful caching
      console.log(`Successfully cached API response: ${url}`);
    } else {
      console.warn(`Not caching API response with status ${networkResponse.status}: ${url}`);
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed for API, trying cache:', url);
    
    // If network fails, try the cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If not in cache, return an empty JSON response
    return new Response(JSON.stringify({
      error: 'You are offline and this data is not cached',
      offline: true
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Handle image fetch with cache-first strategy
async function handleImageFetch(request, url) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // If not in cache, try network
    const networkResponse = await fetch(request);
    
    // Cache the response
    const responseToCache = networkResponse.clone();
    const cache = await caches.open(STATIC_CACHE);
    await cache.put(request, responseToCache);
    
    return networkResponse;
  } catch (error) {
    console.log('Failed to fetch image:', url);
    
    // Return a placeholder SVG
    return new Response(
      `<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
        <rect width="200" height="200" fill="#f0f0f0"/>
        <text x="50%" y="50%" font-family="sans-serif" font-size="24" text-anchor="middle" dominant-baseline="middle" fill="#999">Image</text>
      </svg>`,
      { 
        status: 200, 
        headers: { 'Content-Type': 'image/svg+xml' } 
      }
    );
  }
}

// Handle audio file fetches with special care for S3 URLs
async function handleAudioFetch(request, url) {
  try {
    const isS3Resource = isS3Url(url);
    const filename = getFilenameFromUrl(url);
    
    // First, check the audio cache directly
    try {
      const audioCache = await caches.open(AUDIO_CACHE);
      
      // If it's an S3 URL, we need to check all cache entries
      if (isS3Resource) {
        const cacheKeys = await audioCache.keys();
        
        // Find by filename which is more reliable for cross-origin URLs
        const matchingKey = cacheKeys.find(key => key.url.includes(filename));
        
        if (matchingKey) {
          console.log('📦 Service Worker: Found audio in cache by filename', filename);
          return await audioCache.match(matchingKey);
        }
      } else {
        // For same-origin URLs, we can check directly
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
          console.log('📦 Service Worker: Found audio in cache', url);
          return cachedResponse;
        }
      }
      
      // If not in cache, try to fetch from network
      console.log('🌐 Service Worker: Fetching audio from network', url);
      const networkResponse = await fetch(request);
      
      // Cache the response for future offline use
      if (networkResponse.ok) {
        console.log('💾 Service Worker: Caching audio file', url);
        const responseToCache = networkResponse.clone();
        await audioCache.put(request, responseToCache);
        
        // Broadcast updated cache status
        setTimeout(() => broadcastCacheStatus(), 1000);
      }
      
      return networkResponse;
    } catch (error) {
      console.error('❌ Error accessing audio cache:', error);
      throw error;
    }
  } catch (error) {
    console.error('❌ Audio fetch failed:', error);
    
    // Return a specific error response for audio files
    return new Response('Audio file not available offline', {
      status: 503,
      statusText: 'Service Unavailable',
      headers: { 'Content-Type': 'text/plain' }
    });
  }
}

// Periodically broadcast cache status
setInterval(() => {
  broadcastCacheStatus();
}, 60000); // Update every minute

// Handle errors
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event.message);
  
  // Notify all clients about the error
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({
        type: 'SW_ERROR',
        message: event.message
      });
    });
  });
}); 