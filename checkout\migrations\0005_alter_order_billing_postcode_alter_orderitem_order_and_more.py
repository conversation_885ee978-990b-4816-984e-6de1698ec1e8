# Generated by Django 5.1.5 on 2025-01-31 13:44

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('checkout', '0004_order_user_profile_alter_order_user'),
        ('works', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='billing_postcode',
            field=models.CharField(help_text='Enter a 4-6 characrters of postcode.', max_length=6, validators=[django.core.validators.RegexValidator(message='Postcode must be 4-6 characters and contain only letters and numbers', regex='^[A-Za-z0-9]{4,6}$')]),
        ),
        migrations.AlterField(
            model_name='orderitem',
            name='order',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='checkout.order'),
        ),
        migrations.<PERSON>er<PERSON>ield(
            model_name='orderitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='works.product'),
        ),
    ]
