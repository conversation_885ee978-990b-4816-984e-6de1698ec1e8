/**
 * Navbar Dimming Effect
 * 
 * This script creates a dimming effect for the mobile menu by adding an overlay
 * that darkens the background when the mobile menu is open.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the necessary elements
    const overlay = document.querySelector('.mobile-menu-overlay');
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.getElementById('navbarNav');
    
    // Check if all elements exist
    if (!overlay || !navbarToggler || !navbarCollapse) {
        console.warn('Mobile menu overlay elements not found');
        return;
    }
    
    // Function to show the overlay
    function showOverlay() {
        overlay.classList.add('show');
    }
    
    // Function to hide the overlay
    function hideOverlay() {
        overlay.classList.remove('show');
    }
    
    // Toggle overlay when navbar toggler is clicked
    navbarToggler.addEventListener('click', function() {
        // Use setTimeout to wait for <PERSON><PERSON><PERSON> to toggle the navbar
        setTimeout(function() {
            if (navbarCollapse.classList.contains('show')) {
                showOverlay();
            } else {
                hideOverlay();
            }
        }, 0);
    });
    
    // Hide overlay when clicking on it
    overlay.addEventListener('click', function() {
        if (navbarCollapse.classList.contains('show')) {
            // Use Bootstrap's collapse API to hide the navbar
            const bsCollapse = new bootstrap.Collapse(navbarCollapse);
            bsCollapse.hide();
            hideOverlay();
        }
    });
    
    // Listen for Bootstrap collapse events
    navbarCollapse.addEventListener('shown.bs.collapse', function() {
        showOverlay();
    });
    
    navbarCollapse.addEventListener('hidden.bs.collapse', function() {
        hideOverlay();
    });
}); 