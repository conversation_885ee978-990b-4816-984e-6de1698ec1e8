{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'about/css/about.css' %}">
{% endblock %}

{% block content %}
<div class="container bg-black bg-opacity-75 pt-4 pb-1 rounded mb-4">
    <!-- Author Introduction Section -->
    <section class="mb-4">
        <div class="row align-items-center">
            <div class="col-md-4">
                <img src="{% static 'about/img/banner1.png' %}" alt="Website Logo Photo" class="img-fluid rounded-circle shadow">
            </div>
            <div class="col-md-8">
                <h1 class="display-4 mb-3">Welcome to The Tempus Author Platform</h1>
                <p class="lead">I'm an author passionate about writing and storytelling.</p>
                <p>For decades, I have explored speculative fiction and the struggles that shape our world. My greatest work, THE AGE OF NEW ERA, does not fit into a single genre—it blends science, vision, and global progress. Because of this, I created The Tempus Author Platform, a place where reality meets ideas that drive societal evolution.</p>
                <p>Here, you will find books, audiobooks, and discussions on world development, futuristic science, and the potential for technological unity. Join a community of readers and thinkers shaping the future through storytelling.</p>
            </div>
        </div>
    </section>

    <!-- Works Section -->
    <section class="mb-4">
        <h2 class="mb-4">Find here:</h2>
        <div class="row">
            <!-- Digital Books -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h3 class="h5">Digital Books</h3>
                        <p>Digital books and publications of the Valleyberg Publishing House.</p>
                    </div>
                </div>
            </div>
            
            <!-- Audiobooks -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h3 class="h5">Audiobooks</h3>
                        <p>Audiobooks and productions coming from the Valleyberg Studio</p>
                    </div>
                </div>
            </div>
            
            <!-- Blog -->
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h3 class="h5">Blog & Articles</h3>
                        <p>Tempus Gradus—a magazine dedicated to thought-provoking reads, speculative ideas, and fascinating discussions.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Community Section -->
    <section class="mb-4">
        <h2 class="mb-4">Community & Discussions</h2>
        <p>More information about community engagement, blog topics, and discussions coming soon...</p>
    </section>

    <!-- Collaboration Form Section -->
    <section class="mb-4">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Let's Collaborate</h2>
                <p class="card-text">Interested in working together? Fill out the form below:</p>
                
                <!-- Important Notice for Book Reviews - For Logged In Users -->
                {% if user.is_authenticated %}
                <div id="review-notice" class="alert alert-info mb-4" style="display: none;">
                    <h5 class="alert-heading"><i class="fas fa-info-circle"></i> Important Information for Book Reviews</h5>
                    <p><strong>You are currently logged in as {{ user.email }}</strong></p>
                    <p>Please ensure you use <strong>this same email address</strong> in the form below. This allows us to properly attribute your review to your account.</p>
                    <div class="input-group mb-3 mt-3">
                        <span class="input-group-text">Your account email</span>
                        <input type="text" class="form-control" value="{{ user.email }}" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="copyEmail()">Copy</button>
                    </div>
                </div>
                {% else %}
                <!-- Important Notice for Book Reviews - For Guests -->
                <div id="review-notice" class="alert alert-warning mb-4" style="display: none;">
                    <h5 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Account Required for Book Reviews</h5>
                    <p><strong>You must be registered and logged in to leave a book review.</strong></p>
                    <p>Please <a href="{% url 'account_login' %}" class="alert-link">log in</a> or <a href="{% url 'account_signup' %}" class="alert-link">create an account</a> before submitting your review.</p>
                    <p>After logging in, return to this page and use the <strong>same email address</strong> in the form that you used to create your account.</p>
                </div>
                {% endif %}
                
                <form method="post">
                    {% csrf_token %}
                    {% include 'about/collaboration_form.html' %}
                </form>
            </div>
        </div>
    </section>
</div>

{% block extra_js %}
<script src="{% static 'about/js/about.js' %}"></script>
<script>
    function copyEmail() {
        const emailInput = document.querySelector('#review-notice input');
        emailInput.select();
        document.execCommand('copy');
        
        // Show feedback
        const button = document.querySelector('#review-notice button');
        const originalText = button.textContent;
        button.textContent = 'Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');
        
        // Reset after 2 seconds
        setTimeout(() => {
            button.textContent = originalText;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }
</script>
{% endblock %}
{% endblock %}
