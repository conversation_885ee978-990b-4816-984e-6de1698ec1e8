from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import UserProfile
from .forms import UserProfileForm, PublicProfileForm
from checkout.models import Order
from blog.models import Comment


@login_required
def profile(request):
    """
    Display and handle updates to the user's private profile.

    Shows user's profile information, latest comments, and order history.
    Handles profile updates through POST requests.
    """
    profile = get_object_or_404(UserProfile, user=request.user)

    latest_comments = Comment.objects.filter(author=request.user)\
        .select_related('post')\
        .order_by('-created_on')[:3]

    if request.method == 'POST':
        form = UserProfileForm(
            request.POST, request.FILES, instance=profile
        )
        if form.is_valid():
            form.save()
            messages.success(request, 'Profile updated successfully')
            return redirect('profile')
        else:
            messages.error(
                request, 'Update failed. Please ensure the form is valid.'
            )
    else:
        form = UserProfileForm(instance=profile)

    try:
        orders = profile.orders.all()
    except AttributeError:
        orders = []

    template = 'profiles/profile.html'
    context = {
        'profile': profile,
        'form': form,
        'orders': orders,
        'latest_comments': latest_comments,
        'on_profile_page': True
    }

    return render(request, template, context)


def public_profile(request, user_id):
    """
    Display a user's public profile.

    Shows limited profile information and latest comments visible to other
    users. Redirects to private profile if user views
    their own public profile.
    """
    profile = get_object_or_404(UserProfile, user__id=user_id)

    if request.user.is_authenticated and request.user.id == user_id:
        return redirect('profile')

    form = PublicProfileForm(instance=profile)

    latest_comments = Comment.objects.filter(author=profile.user).select_related('post').order_by('-created_on')[:3]

    template = 'profiles/public_profile.html'
    context = {
        'profile': profile,
        'form': form,
        'latest_comments': latest_comments,
    }

    return render(request, template, context)


@login_required
def order_history(request, order_id):
    """
    Display details of a specific past order.

    Shows the order confirmation page with a message indicating
    this is a past order view.
    """
    order = get_object_or_404(Order, id=order_id)

    # Security check: ensure user can only view their own orders
    if order.user != request.user:
        messages.error(request, 'You do not have permission to view this order.')
        return redirect('profile')

    messages.info(
        request,
        f'This is a past confirmation for order number {order_id}.'
    )

    template = 'checkout/checkout_success.html'
    context = {
        'order': order,
        'from_profile': True,
    }

    return render(request, template, context)


@login_required
def my_library(request):
    """
    Display all digital content purchased by the user.

    Shows a library of e-books, audiobooks, and other digital content
    that the user has purchased or received through contest access requests.
    """
    # Get all paid orders and free book orders for the current user
    orders = Order.objects.filter(
        user=request.user,
        payment_status__in=['paid', 'free_book']
    )

    # Debug: Print order IDs
    print(f"Found {len(orders)} paid orders: {[o.id for o in orders]}")

    # Get all order items from these orders
    order_items = []
    for order in orders:
        items = order.orderitem_set.all()
        order_items.extend(items)

    # Debug: Print order items
    print(f"Found {len(order_items)} order items: {[(item.id, item.product.id, item.product.name) for item in order_items]}")

    # Organize content by type
    ebooks = []
    audiobooks = []
    other_content = []

    # Track processed product IDs to avoid duplicates
    processed_ebooks = set()
    processed_audiobooks = set()
    processed_other = set()

    # Process each product and add to appropriate lists based on category
    for item in order_items:
        product = item.product

        # Debug: Print product details
        print(f"Processing product: ID={product.id}, Name={product.name}, Category={product.category.name if product.category else 'None'}")

        # Add to ebooks if it has ebook category
        if product.category and product.category.name == 'ebook' and product.id not in processed_ebooks:
            ebooks.append(product)
            processed_ebooks.add(product.id)
            print(f"Added product {product.id} to ebooks")

        # Add to audiobooks if it has audiobook category
        if product.category and product.category.name == 'audiobook' and product.id not in processed_audiobooks:
            audiobooks.append(product)
            processed_audiobooks.add(product.id)
            print(f"Added product {product.id} to audiobooks")

        # Add to other content if it has attachments and isn't already categorized
        if (product.attachments.exists() and
                product.id not in processed_ebooks and
                product.id not in processed_audiobooks and
                product.id not in processed_other):
            other_content.append({
                'product': product,
                'attachments': product.attachments.all()
            })
            processed_other.add(product.id)
            print(f"Added product {product.id} to other_content")

    # Check for free access books from contest requests
    try:
        # Import here to avoid circular imports
        from contest.models import BookAccessRequest

        # Get all approved and granted access requests for this user
        free_access_requests = BookAccessRequest.objects.filter(
            user=request.user,
            status='approved',
            access_granted=True
        )

        print(f"Found {len(free_access_requests)} free access requests")

        # Add these products to the appropriate lists
        for request_obj in free_access_requests:
            product = request_obj.book_part

            # Check if this product is already in our lists
            if request_obj.format_type == 'ebook' and product.id not in processed_ebooks:
                ebooks.append(product)
                processed_ebooks.add(product.id)
                print(f"Added free access ebook {product.id} to library")

            elif request_obj.format_type == 'audiobook' and product.id not in processed_audiobooks:
                audiobooks.append(product)
                processed_audiobooks.add(product.id)
                print(f"Added free access audiobook {product.id} to library")
    except Exception as e:
        # Log the error but continue
        print(f"Error processing free access requests: {str(e)}")

    # Debug: Print final counts
    print(f"Final counts - Ebooks: {len(ebooks)}, Audiobooks: {len(audiobooks)}, Other: {len(other_content)}")

    template = 'profiles/my_library.html'
    context = {
        'ebooks': ebooks,
        'audiobooks': audiobooks,
        'other_content': other_content,
        'on_profile_page': True
    }

    return render(request, template, context)
