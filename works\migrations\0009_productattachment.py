# Generated by Django 5.1.5 on 2025-02-28 15:28

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('works', '0008_product_ebook_file'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductAttachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Name of the attachment (e.g., 'Audiobook', 'Bonus Chapter', 'Promotional Poster')", max_length=255)),
                ('description', models.TextField(blank=True, help_text='Optional description of the attachment')),
                ('attachment_type', models.CharField(choices=[('file', 'File Upload'), ('link', 'External Link')], default='file', max_length=10)),
                ('file', models.FileField(blank=True, help_text='Upload file attachment (PDF, MP3, etc.)', null=True, upload_to='product_attachments/')),
                ('link', models.URLField(blank=True, help_text='URL to external resource (e.g., audiobook streaming link)', max_length=1024, null=True)),
                ('is_promotional', models.BooleanField(default=False, help_text='Mark as promotional material that may be temporary')),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('updated_date', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='works.product')),
            ],
        ),
    ]
