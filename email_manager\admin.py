from django.contrib import admin
from django.utils.html import format_html
from django.urls import path
from django.template.response import TemplateResponse
from django.shortcuts import redirect
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count, Sum
from django.core.mail import send_mail
from django.conf import settings

from .models import EmailConfiguration, EmailCounter, QueuedEmail
from .utils import get_email_stats


class EmailConfigurationAdmin(admin.ModelAdmin):
    """Admin interface for email configuration"""
    fieldsets = (
        ('Email Verification', {
            'fields': ('require_email_verification',),
            'description': 'Control whether users need to verify their email addresses.'
        }),
        ('Rate Limiting', {
            'fields': ('hourly_email_limit', 'queue_batch_size', 'queue_batch_delay'),
            'description': 'Configure email sending rate limits and queue processing.'
        }),
        ('Email Types', {
            'fields': ('send_welcome_emails', 'send_newsletter_confirmation', 'combine_order_emails'),
            'description': 'Control which types of emails are sent.'
        }),
        ('Notifications', {
            'fields': ('notify_admin_on_limit', 'admin_email'),
            'description': 'Configure admin notifications for email limits.'
        }),
    )

    def has_add_permission(self, request):
        # Prevent adding new configurations (we use singleton pattern)
        return False

    def has_delete_permission(self, request, obj=None):
        # Prevent deleting the configuration
        return False


class EmailCounterAdmin(admin.ModelAdmin):
    """Admin interface for email counters"""
    list_display = ('date', 'hour', 'count', 'percent_of_limit')
    list_filter = ('date',)
    ordering = ('-date', 'hour')
    readonly_fields = ('date', 'hour', 'count')

    def percent_of_limit(self, obj):
        """Display percentage of hourly limit used"""
        config = EmailConfiguration.get_config()
        limit = config.hourly_email_limit
        percent = (obj.count / limit) * 100 if limit else 0

        # Color code based on percentage
        if percent < 50:
            color = 'green'
        elif percent < 80:
            color = 'orange'
        else:
            color = 'red'

        return format_html(
            '<span style="color: {};">{:.1f}%</span>',
            color, percent
        )

    percent_of_limit.short_description = 'Percent of Limit'

    def has_add_permission(self, request):
        # Prevent manually adding counters
        return False

    def has_change_permission(self, request, obj=None):
        # Prevent editing counters
        return False


class QueuedEmailAdmin(admin.ModelAdmin):
    """Admin interface for queued emails"""
    list_display = ('subject', 'recipient', 'priority', 'status', 'created_at', 'last_attempt')
    list_filter = ('status', 'priority', 'created_at')
    search_fields = ('subject', 'recipient', 'content_type', 'object_id')
    readonly_fields = ('created_at', 'last_attempt', 'error_message')
    actions = ['retry_failed_emails', 'send_now', 'cancel_emails']

    def retry_failed_emails(self, request, queryset):
        """Retry sending failed emails"""
        updated = queryset.filter(status='failed').update(
            status='pending',
            retry_count=0,
            error_message=None
        )
        self.message_user(
            request,
            f"{updated} emails marked for retry.",
            messages.SUCCESS
        )
    retry_failed_emails.short_description = "Retry selected failed emails"

    def send_now(self, request, queryset):
        """Send selected emails now, bypassing the queue"""
        from .utils import send_email_with_rate_limit

        success_count = 0
        fail_count = 0

        for email in queryset.filter(status='pending'):
            # Try to send the email immediately
            success, message = send_email_with_rate_limit(
                subject=email.subject,
                recipient=email.recipient,
                html_content=email.html_content,
                text_content=email.text_content,
                priority=email.priority,
                content_type=email.content_type,
                object_id=email.object_id,
                from_email=email.from_email,
                attachments=email.get_attachments() if email.has_attachments else None
            )

            if success:
                success_count += 1
                email.status = 'sent'
                email.last_attempt = timezone.now()
                email.save()
            else:
                fail_count += 1

        if success_count > 0:
            self.message_user(
                request,
                f"{success_count} emails sent successfully.",
                messages.SUCCESS
            )

        if fail_count > 0:
            self.message_user(
                request,
                f"{fail_count} emails could not be sent (hourly limit reached or error).",
                messages.WARNING
            )

    send_now.short_description = "Send selected emails now"

    def cancel_emails(self, request, queryset):
        """Cancel selected pending emails"""
        updated = queryset.filter(status='pending').delete()
        count = updated[0] if isinstance(updated, tuple) else updated

        self.message_user(
            request,
            f"{count} pending emails have been cancelled.",
            messages.SUCCESS
        )

    cancel_emails.short_description = "Cancel selected pending emails"

    def get_urls(self):
        """Add custom URLs for the admin interface"""
        urls = super().get_urls()
        custom_urls = [
            path('dashboard/', self.admin_site.admin_view(self.email_dashboard_view),
                 name='email_dashboard'),
            path('process_queue/', self.admin_site.admin_view(self.process_queue_view),
                 name='process_email_queue'),
        ]
        return custom_urls + urls

    def email_dashboard_view(self, request):
        """Custom view for email dashboard"""
        # Get email statistics
        stats = get_email_stats()

        # Get recent emails
        recent_emails = QueuedEmail.objects.all().order_by('-created_at')[:20]

        # Get daily counts
        today = timezone.now().date()
        daily_counts = EmailCounter.objects.filter(
            date__gte=today - timezone.timedelta(days=7)
        ).values('date').annotate(
            total=Sum('count')
        ).order_by('-date')

        # Get status counts
        status_counts = QueuedEmail.objects.values('status').annotate(
            count=Count('id')
        )

        context = {
            'title': 'Email Dashboard',
            'stats': stats,
            'recent_emails': recent_emails,
            'daily_counts': daily_counts,
            'status_counts': status_counts,
            'opts': self.model._meta,
        }

        return TemplateResponse(
            request,
            'admin/email_manager/email_dashboard.html',
            context
        )

    def process_queue_view(self, request):
        """View to manually trigger queue processing"""
        from django.core.management import call_command

        # Call the management command to process the queue
        try:
            call_command('process_email_queue', once=True)
            self.message_user(
                request,
                "Email queue processing has been triggered.",
                messages.SUCCESS
            )
        except Exception as e:
            self.message_user(
                request,
                f"Error processing queue: {str(e)}",
                messages.ERROR
            )

        return redirect('admin:email_manager_queuedemail_changelist')


# Register models with admin site
admin.site.register(EmailConfiguration, EmailConfigurationAdmin)
admin.site.register(EmailCounter, EmailCounterAdmin)
admin.site.register(QueuedEmail, QueuedEmailAdmin)
