.work-card {
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.work-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.work-image-container {
    height: 300px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.work-image-container img {
    width: auto;
    height: 100%;
    object-fit: cover;
}

.work-description {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    color: #6c757d;
}

.card-title a {
    transition: color 0.2s ease-in-out;
}

.card-title a:hover {
    color: #0d6efd !important;
}