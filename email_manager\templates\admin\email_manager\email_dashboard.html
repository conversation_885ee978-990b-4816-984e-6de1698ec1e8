{% extends "admin/base_site.html" %}
{% load i18n admin_urls static %}

{% block extrastyle %}
  {{ block.super }}
  <style>
    .email-stats {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    .stat-card {
      flex: 1;
      min-width: 200px;
      padding: 15px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      background-color: #fff;
    }
    .stat-card h3 {
      margin-top: 0;
      color: #666;
      font-size: 14px;
      text-transform: uppercase;
    }
    .stat-value {
      font-size: 24px;
      font-weight: bold;
      margin: 10px 0;
    }
    .progress-bar {
      height: 10px;
      background-color: #f0f0f0;
      border-radius: 5px;
      overflow: hidden;
      margin-top: 10px;
    }
    .progress-fill {
      height: 100%;
      background-color: #79aec8;
    }
    .progress-fill.warning {
      background-color: #ffcc00;
    }
    .progress-fill.danger {
      background-color: #ff5555;
    }
    .email-tables {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .email-table {
      flex: 1;
      min-width: 300px;
    }
    .dashboard-actions {
      margin: 20px 0;
    }
    .dashboard-actions a {
      margin-right: 10px;
    }
  </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
  <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
  &rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
  &rsaquo; <a href="{% url 'admin:email_manager_queuedemail_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
  &rsaquo; {% trans 'Email Dashboard' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
  <h1>Email Dashboard</h1>
  
  <div class="dashboard-actions">
    <a href="{% url 'admin:email_manager_emailconfiguration_change' 1 %}" class="button">Email Settings</a>
    <a href="{% url 'admin:email_manager_queuedemail_changelist' %}" class="button">View All Emails</a>
    <a href="{% url 'admin:process_email_queue' %}" class="button">Process Queue Now</a>
  </div>
  
  <div class="email-stats">
    <div class="stat-card">
      <h3>Current Hour</h3>
      <div class="stat-value">{{ stats.current_hour }} / {{ stats.limit }}</div>
      <div class="progress-bar">
        <div class="progress-fill {% if stats.percent_used > 80 %}danger{% elif stats.percent_used > 50 %}warning{% endif %}" style="width: {{ stats.percent_used }}%;"></div>
      </div>
      <div class="stat-description">{{ stats.percent_used|floatformat:1 }}% of hourly limit used</div>
    </div>
    
    <div class="stat-card">
      <h3>Today's Total</h3>
      <div class="stat-value">{{ stats.today_total }}</div>
      <div class="stat-description">Emails sent today</div>
    </div>
    
    <div class="stat-card">
      <h3>Pending Emails</h3>
      <div class="stat-value">{{ stats.pending_emails }}</div>
      <div class="stat-description">Emails waiting to be sent</div>
    </div>
    
    <div class="stat-card">
      <h3>Failed Emails</h3>
      <div class="stat-value">{{ stats.failed_emails }}</div>
      <div class="stat-description">Emails that failed to send</div>
    </div>
  </div>
  
  <div class="email-tables">
    <div class="email-table">
      <h2>Recent Emails</h2>
      {% if recent_emails %}
        <table>
          <thead>
            <tr>
              <th>Subject</th>
              <th>Recipient</th>
              <th>Status</th>
              <th>Created</th>
            </tr>
          </thead>
          <tbody>
            {% for email in recent_emails %}
              <tr>
                <td><a href="{% url 'admin:email_manager_queuedemail_change' email.id %}">{{ email.subject|truncatechars:40 }}</a></td>
                <td>{{ email.recipient }}</td>
                <td>
                  {% if email.status == 'pending' %}
                    <span style="color: orange;">Pending</span>
                  {% elif email.status == 'sent' %}
                    <span style="color: green;">Sent</span>
                  {% else %}
                    <span style="color: red;">Failed</span>
                  {% endif %}
                </td>
                <td>{{ email.created_at|date:"M d, H:i" }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      {% else %}
        <p>No emails have been sent yet.</p>
      {% endif %}
    </div>
    
    <div class="email-table">
      <h2>Daily Email Counts</h2>
      {% if daily_counts %}
        <table>
          <thead>
            <tr>
              <th>Date</th>
              <th>Total Emails</th>
            </tr>
          </thead>
          <tbody>
            {% for day in daily_counts %}
              <tr>
                <td>{{ day.date|date:"M d, Y" }}</td>
                <td>{{ day.total }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      {% else %}
        <p>No email statistics available yet.</p>
      {% endif %}
    </div>
  </div>
  
  <div class="email-tables">
    <div class="email-table">
      <h2>Email Status Summary</h2>
      {% if status_counts %}
        <table>
          <thead>
            <tr>
              <th>Status</th>
              <th>Count</th>
            </tr>
          </thead>
          <tbody>
            {% for status in status_counts %}
              <tr>
                <td>
                  {% if status.status == 'pending' %}
                    <span style="color: orange;">Pending</span>
                  {% elif status.status == 'sent' %}
                    <span style="color: green;">Sent</span>
                  {% else %}
                    <span style="color: red;">Failed</span>
                  {% endif %}
                </td>
                <td>{{ status.count }}</td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      {% else %}
        <p>No email status data available yet.</p>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
