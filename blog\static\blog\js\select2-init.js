document.addEventListener('DOMContentLoaded', function() {
    // Store Django's jQuery
    var djangoJQ = django.jQuery;
    
    // Load jQuery and restore Django's jQuery
    var script = document.createElement('script');
    script.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
    script.onload = function() {
        var $ = jQuery.noConflict();
        django.jQuery = djangoJQ;
        
        // Load Select2
        var select2Script = document.createElement('script');
        select2Script.src = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js';
        select2Script.onload = function() {
            initializeSelect2($);
        };
        document.head.appendChild(select2Script);
    };
    document.head.appendChild(script);
});

function initializeSelect2($) {
    // Get tag data from the window object
    var existingTags = (window.DJANGO_TAGS && window.DJANGO_TAGS.existing) || [];
    var currentTags = (window.DJANGO_TAGS && window.DJANGO_TAGS.current) || [];
    
    var $select = $('.django-select2').select2({
        theme: 'bootstrap5',
        width: '100%',
        tags: true,
        tokenSeparators: [','],
        minimumInputLength: 0, 
        dropdownCssClass: 'select2-dropdown-custom',
        data: existingTags.map(function(tag) {
            return { id: tag, text: tag };
        }),
        createTag: function(params) {
            var term = $.trim(params.term);
            if (term === '') {
                return null;
            }
            return {
                id: term,
                text: term,
                newTag: true
            };
        }
    }).val(currentTags).trigger('change');

    // Ensure keyboard events are properly handled
    $select.on('select2:open', function() {
        setTimeout(function() {
            $('.select2-search__field').focus();
            
            // Handle keyboard events
            $('.select2-search__field').on('keydown', function(e) {
                if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                    e.stopPropagation();
                    var $dropdown = $('.select2-results__options');
                    var $options = $dropdown.find('.select2-results__option[role="option"]');
                    var $highlighted = $dropdown.find('.select2-results__option--highlighted');
                    var index = $options.index($highlighted);

                    if (e.key === 'ArrowDown' && index < $options.length - 1) {
                        $options.removeClass('select2-results__option--highlighted');
                        $($options[index + 1]).addClass('select2-results__option--highlighted');
                    } else if (e.key === 'ArrowUp' && index > 0) {
                        $options.removeClass('select2-results__option--highlighted');
                        $($options[index - 1]).addClass('select2-results__option--highlighted');
                    }
                }
            });
        }, 0);
    });

    // Handle dynamic form additions
    $(document).on('formset:added', function() {
        $('.django-select2').select2({
            theme: 'bootstrap5',
            width: '100%',
            tags: true,
            tokenSeparators: [','],
            dropdownCssClass: 'select2-dropdown-custom',
            minimumInputLength: 0
        });
    });
}
