/**
 * TEMPUS View - Enhanced gallery viewing experience v1.1
 *
 * This script implements a lightbox-style gallery with a detail sidebar
 * for the Tempus Quest Illustration Contest.
 *
 * Version 1.1: Improved error handling and DOM interaction
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    let currentIndex = 0;
    let submissions = [];
    let isOpen = false;
    let isZoomed = false;
    let slideshowInterval = null;
    let isSlideshow = false;

    // Prevent right-click on images to protect content
    document.addEventListener('contextmenu', function(e) {
        const target = e.target;
        if (target.tagName === 'IMG' &&
            (target.closest('.submission-image-container') ||
             target.closest('.tempus-view-image-container'))) {
            e.preventDefault();
            alert('Image downloading is not permitted according to our terms and conditions.');
            return false;
        }
    }, false);

    // DOM elements
    const tempusViewBtn = document.getElementById('tempus-view-btn');
    const tempusViewContainer = document.getElementById('tempus-view-container');
    const tempusViewClose = document.getElementById('tempus-view-close');
    const tempusViewImage = document.getElementById('tempus-view-image');
    const tempusViewPrev = document.getElementById('tempus-view-prev');
    const tempusViewNext = document.getElementById('tempus-view-next');
    const tempusViewDetails = document.getElementById('tempus-view-details');
    const tempusViewTitle = document.getElementById('tempus-view-title');
    const tempusViewArtist = document.getElementById('tempus-view-artist');
    const tempusViewCategory = document.getElementById('tempus-view-category');
    const tempusViewDescription = document.getElementById('tempus-view-description');
    const tempusViewLikes = document.getElementById('tempus-view-likes');
    const tempusViewDate = document.getElementById('tempus-view-date');
    const tempusViewDetailsLink = document.getElementById('tempus-view-details-link');

    // Add slideshow button to the container
    const slideshowBtn = document.createElement('button');
    slideshowBtn.id = 'tempus-view-slideshow';
    slideshowBtn.className = 'tempus-view-control';
    slideshowBtn.innerHTML = '<i class="fas fa-play"></i>';
    slideshowBtn.title = 'Start Slideshow';
    if (tempusViewContainer) {
        tempusViewContainer.appendChild(slideshowBtn);
    }

    // Collect all submission data from the gallery
    function collectSubmissions() {
        console.log('Collecting submissions data...');
        submissions = [];

        // Get all submission cards
        const cards = document.querySelectorAll('.submission-card');
        console.log(`Found ${cards.length} submission cards`);

        // Process each card
        cards.forEach((card, index) => {
            try {
                // First, check if this card has an image we can use
                let imageUrl = null;
                let imageElement = null;

                // Try multiple selectors to find the image
                try {
                    imageElement = card.querySelector('.card-img-top');
                    if (!imageElement) {
                        imageElement = card.querySelector('.submission-image img');
                    }
                    if (!imageElement) {
                        imageElement = card.querySelector('img');
                    }

                    if (imageElement && imageElement.src) {
                        imageUrl = imageElement.src;
                    }
                } catch (e) {
                    console.warn('Error finding image element:', e);
                }

                // Skip if no image found
                if (!imageUrl) {
                    console.log(`Skipping card ${index} - no image found`);
                    return;
                }

                // Extract all other data with maximum safety
                const submissionData = {
                    index,
                    imageUrl,
                    title: 'Untitled Submission',
                    artist: 'Unknown Artist',
                    category: 'Uncategorized',
                    description: '',
                    likes: '0',
                    date: '',
                    detailsLink: '#'
                };

                // Try to get title
                try {
                    const titleElement = card.querySelector('.card-title');
                    if (titleElement && titleElement.textContent) {
                        submissionData.title = titleElement.textContent.trim();
                    }
                } catch (e) {
                    console.warn('Error extracting title:', e);
                }

                // Try to get artist and date
                try {
                    const textElements = card.querySelectorAll('.text-muted');
                    textElements.forEach(el => {
                        const text = el.textContent.trim();
                        if (text.includes('By:')) {
                            submissionData.artist = text.replace('By:', '').split('\n')[0].trim();

                            // Try to get date from the same element
                            const lines = text.split('\n').filter(line => line.trim() !== '');
                            if (lines.length > 1) {
                                // Last line is likely the date
                                submissionData.date = lines[lines.length - 1].trim();
                            }
                        }
                    });
                } catch (e) {
                    console.warn('Error extracting artist/date:', e);
                }

                // Try to get category
                try {
                    const badges = card.querySelectorAll('.badge');
                    badges.forEach(badge => {
                        if (badge.classList.contains('bg-secondary')) {
                            submissionData.category = badge.textContent.trim();
                        }
                    });
                } catch (e) {
                    console.warn('Error extracting category:', e);
                }

                // Try to get description
                try {
                    const descElement = card.querySelector('.card-text');
                    if (descElement && descElement.textContent) {
                        submissionData.description = descElement.textContent.trim();
                    }
                } catch (e) {
                    console.warn('Error extracting description:', e);
                }

                // Try to get likes count
                try {
                    const likesBadges = card.querySelectorAll('.badge');
                    likesBadges.forEach(badge => {
                        if (badge.classList.contains('bg-primary') &&
                            badge.classList.contains('rounded-pill')) {
                            submissionData.likes = badge.textContent.trim();
                        }
                    });
                } catch (e) {
                    console.warn('Error extracting likes:', e);
                }

                // Try to get details link
                try {
                    const links = card.querySelectorAll('a.btn');
                    links.forEach(link => {
                        if (link.classList.contains('btn-primary') &&
                            link.textContent.includes('View Details')) {
                            submissionData.detailsLink = link.getAttribute('href');
                        }
                    });
                } catch (e) {
                    console.warn('Error extracting details link:', e);
                }

                // Add to submissions array
                submissions.push(submissionData);
                console.log(`Added submission ${index}: ${submissionData.title}`);

            } catch (error) {
                console.error(`Error processing card ${index}:`, error);
            }
        });

        console.log(`Successfully collected ${submissions.length} submissions`);
        return submissions;
    }

    // Open the TEMPUS View
    function openTempusView(startIndex = 0) {
        console.log(`Opening TEMPUS View with startIndex: ${startIndex}`);

        // Always recollect submissions to ensure we have the latest data
        collectSubmissions();

        if (submissions.length === 0) {
            console.warn('No submissions found, showing alert');
            alert('No image submissions found in the current gallery view.');
            return;
        }

        // Validate the startIndex
        if (startIndex < 0 || startIndex >= submissions.length) {
            console.warn(`Invalid startIndex ${startIndex}, defaulting to 0`);
            startIndex = 0;
        }

        currentIndex = startIndex;
        console.log(`Setting current index to ${currentIndex}`);

        // Make sure the container exists
        if (!tempusViewContainer) {
            console.error('TEMPUS View container not found!');
            return;
        }

        // Update the view
        updateTempusView();

        // Show the container
        tempusViewContainer.classList.remove('d-none');
        document.body.classList.add('tempus-view-open');
        isOpen = true;

        // Add keyboard event listeners
        document.removeEventListener('keydown', handleKeyDown); // Remove any existing listener
        document.addEventListener('keydown', handleKeyDown);

        console.log('TEMPUS View opened successfully');
    }

    // Close the TEMPUS View
    function closeTempusView() {
        tempusViewContainer.classList.add('d-none');
        document.body.classList.remove('tempus-view-open');
        isOpen = false;

        // Stop slideshow if it's running
        if (isSlideshow) {
            isSlideshow = false;
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
            if (slideshowBtn) {
                slideshowBtn.innerHTML = '<i class="fas fa-play"></i>';
                slideshowBtn.title = 'Start Slideshow';
            }
        }

        // Remove keyboard event listeners
        document.removeEventListener('keydown', handleKeyDown);
    }

    // Update the TEMPUS View with the current submission
    function updateTempusView() {
        console.log(`Updating TEMPUS View with submission at index ${currentIndex}`);

        // Safety check
        if (!submissions || submissions.length === 0 || currentIndex < 0 || currentIndex >= submissions.length) {
            console.error('Invalid submission data or index');
            return;
        }

        try {
            const submission = submissions[currentIndex];

            // Reset zoom state when changing images
            isZoomed = false;
            if (tempusViewImage) {
                tempusViewImage.classList.remove('zoomed');

                // Update image with error handling
                if (submission.imageUrl) {
                    tempusViewImage.src = submission.imageUrl;
                    tempusViewImage.alt = submission.title || 'Submission Image';
                } else {
                    console.warn('No image URL for this submission');
                    tempusViewImage.src = '/static/contest/img/placeholder.jpg';
                    tempusViewImage.alt = 'Image not available';
                }
            }

            // Update details with null checks
            if (tempusViewTitle) tempusViewTitle.textContent = submission.title || 'Untitled';
            if (tempusViewArtist) tempusViewArtist.textContent = submission.artist || 'Unknown Artist';
            if (tempusViewCategory) tempusViewCategory.textContent = submission.category || 'Uncategorized';
            if (tempusViewDescription) tempusViewDescription.textContent = submission.description || '';
            if (tempusViewLikes) tempusViewLikes.textContent = submission.likes || '0';
            if (tempusViewDate) tempusViewDate.textContent = submission.date || '';
            if (tempusViewDetailsLink) tempusViewDetailsLink.href = submission.detailsLink || '#';

            // Update navigation state
            if (tempusViewPrev) tempusViewPrev.classList.toggle('disabled', currentIndex === 0);
            if (tempusViewNext) tempusViewNext.classList.toggle('disabled', currentIndex === submissions.length - 1);

            // Preload adjacent images for smoother navigation
            preloadAdjacentImages();

            console.log('TEMPUS View updated successfully');
        } catch (error) {
            console.error('Error updating TEMPUS View:', error);
        }
    }

    // Navigate to the previous submission
    function goToPrevious() {
        if (currentIndex > 0) {
            currentIndex--;
            updateTempusView();
        }
    }

    // Navigate to the next submission
    function goToNext() {
        if (currentIndex < submissions.length - 1) {
            currentIndex++;
            updateTempusView();
        }
    }

    // Toggle zoom on the current image
    function toggleZoom() {
        if (!isOpen) return;

        isZoomed = !isZoomed;
        tempusViewImage.classList.toggle('zoomed', isZoomed);
    }

    // Toggle slideshow mode
    function toggleSlideshow() {
        isSlideshow = !isSlideshow;

        if (isSlideshow) {
            slideshowBtn.innerHTML = '<i class="fas fa-pause"></i>';
            slideshowBtn.title = 'Pause Slideshow';

            // Start the slideshow interval
            slideshowInterval = setInterval(() => {
                if (currentIndex < submissions.length - 1) {
                    goToNext();
                } else {
                    // Stop at the end or loop back to the beginning
                    currentIndex = -1;
                    goToNext();
                }
            }, 3000); // Change slide every 3 seconds
        } else {
            slideshowBtn.innerHTML = '<i class="fas fa-play"></i>';
            slideshowBtn.title = 'Start Slideshow';

            // Clear the slideshow interval
            if (slideshowInterval) {
                clearInterval(slideshowInterval);
                slideshowInterval = null;
            }
        }
    }

    // Preload adjacent images for smoother navigation
    function preloadAdjacentImages() {
        if (submissions.length === 0) return;

        // Preload next image
        if (currentIndex < submissions.length - 1) {
            const nextImg = new Image();
            nextImg.src = submissions[currentIndex + 1].imageUrl;
        }

        // Preload previous image
        if (currentIndex > 0) {
            const prevImg = new Image();
            prevImg.src = submissions[currentIndex - 1].imageUrl;
        }
    }

    // Handle keyboard navigation
    function handleKeyDown(e) {
        if (!isOpen) return;

        switch (e.key) {
            case 'ArrowLeft':
                goToPrevious();
                break;
            case 'ArrowRight':
                goToNext();
                break;
            case 'Escape':
                closeTempusView();
                break;
            case ' ': // Spacebar
                toggleSlideshow();
                e.preventDefault(); // Prevent page scrolling
                break;
            case 'z':
            case 'Z':
                toggleZoom();
                break;
        }
    }

    // Event listeners
    if (tempusViewBtn) {
        tempusViewBtn.addEventListener('click', function(e) {
            e.preventDefault();
            openTempusView();
        });
    }

    if (tempusViewClose) {
        tempusViewClose.addEventListener('click', function(e) {
            e.preventDefault();
            closeTempusView();
        });
    }

    if (tempusViewPrev) {
        tempusViewPrev.addEventListener('click', function(e) {
            e.preventDefault();
            goToPrevious();
        });
    }

    if (tempusViewNext) {
        tempusViewNext.addEventListener('click', function(e) {
            e.preventDefault();
            goToNext();
        });
    }

    if (slideshowBtn) {
        slideshowBtn.addEventListener('click', function(e) {
            e.preventDefault();
            toggleSlideshow();
        });
    }

    if (tempusViewImage) {
        tempusViewImage.addEventListener('click', function(e) {
            e.preventDefault();
            toggleZoom();
        });
    }

    // Add touch swipe support for mobile devices
    let touchStartX = 0;
    let touchEndX = 0;

    if (tempusViewContainer) {
        tempusViewContainer.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        }, false);

        tempusViewContainer.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        }, false);
    }

    function handleSwipe() {
        const swipeThreshold = 50; // Minimum distance for a swipe

        if (touchEndX < touchStartX - swipeThreshold) {
            // Swipe left - go to next
            goToNext();
        }

        if (touchEndX > touchStartX + swipeThreshold) {
            // Swipe right - go to previous
            goToPrevious();
        }
    }

    // Function to set up event listeners for gallery items
    function setupGalleryEventListeners() {
        console.log('Setting up gallery event listeners');

        // Remove any existing event listeners by using event delegation
        document.removeEventListener('click', handleGalleryImageClick);
        document.addEventListener('click', handleGalleryImageClick);

        // Also set up direct listeners for better compatibility
        document.querySelectorAll('.submission-card').forEach((card, index) => {
            try {
                const imageContainer = card.querySelector('.submission-image');
                if (imageContainer) {
                    // Store the index as a data attribute for easier retrieval
                    imageContainer.setAttribute('data-submission-index', index);

                    // Remove existing listeners by cloning and replacing
                    const newImageContainer = imageContainer.cloneNode(true);
                    imageContainer.parentNode.replaceChild(newImageContainer, imageContainer);

                    // Add new listener
                    newImageContainer.addEventListener('click', function(e) {
                        // Only trigger if clicking on the image, not on badges or other elements
                        if (e.target.tagName === 'IMG') {
                            e.preventDefault();
                            const idx = parseInt(this.getAttribute('data-submission-index'), 10);
                            openTempusView(isNaN(idx) ? 0 : idx);
                        }
                    });
                }
            } catch (error) {
                console.error('Error setting up gallery item listener:', error);
            }
        });
    }

    // Event delegation handler for gallery images
    function handleGalleryImageClick(e) {
        // Check if the click was on an image inside a submission card
        if (e.target.tagName === 'IMG') {
            const imageContainer = e.target.closest('.submission-image');
            if (imageContainer) {
                e.preventDefault();

                // Recollect submissions to ensure we have the latest data
                collectSubmissions();

                // Find the index of this submission
                const card = imageContainer.closest('.submission-card');
                if (card) {
                    const allCards = Array.from(document.querySelectorAll('.submission-card'));
                    const index = allCards.indexOf(card);
                    if (index !== -1) {
                        openTempusView(index);
                    } else {
                        openTempusView(0);
                    }
                } else {
                    openTempusView(0);
                }
            }
        }
    }

    // Initial setup
    setupGalleryEventListeners();

    // Set up a MutationObserver to detect DOM changes and reattach listeners
    const observer = new MutationObserver(function(mutations) {
        let shouldReinit = false;

        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                // Check if any added nodes contain submission cards
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === 1) { // Element node
                        if (node.classList && node.classList.contains('submission-card') ||
                            node.querySelector && node.querySelector('.submission-card')) {
                            shouldReinit = true;
                            break;
                        }
                    }
                }
            }
        });

        if (shouldReinit) {
            console.log('DOM changed, reinitializing gallery event listeners');
            collectSubmissions();
            setupGalleryEventListeners();
        }
    });

    // Start observing the document
    observer.observe(document.body, { childList: true, subtree: true });
});
