from django.db import models
from django.utils import timezone
import datetime
import json

class EmailConfiguration(models.Model):
    """Global email configuration settings"""
    # Email verification settings
    require_email_verification = models.BooleanField(
        default=True,
        help_text="If enabled, users must verify their email address to fully access the site"
    )

    # Rate limiting settings
    hourly_email_limit = models.IntegerField(
        default=90,
        help_text="Maximum emails to send per hour (Dreamhost limit is 100)"
    )

    # Queue processing settings
    queue_batch_size = models.IntegerField(
        default=10,
        help_text="Number of emails to process in one batch"
    )
    queue_batch_delay = models.IntegerField(
        default=30,
        help_text="Seconds to wait between batches"
    )

    # Email types to enable/disable
    send_welcome_emails = models.BooleanField(
        default=True,
        help_text="Send welcome emails to new users"
    )
    send_newsletter_confirmation = models.BooleanField(
        default=False,
        help_text="Send confirmation emails for newsletter subscriptions"
    )
    combine_order_emails = models.BooleanField(
        default=True,
        help_text="Combine order confirmation and digital content emails"
    )

    # Notification settings
    notify_admin_on_limit = models.BooleanField(
        default=True,
        help_text="Notify admin when approaching email limits"
    )
    admin_email = models.EmailField(
        blank=True,
        null=True,
        help_text="Email to send admin notifications to"
    )

    # Singleton pattern - only one configuration instance
    class Meta:
        verbose_name = "Email Configuration"
        verbose_name_plural = "Email Configuration"

    def save(self, *args, **kwargs):
        """Ensure only one instance exists"""
        self.pk = 1
        super().save(*args, **kwargs)

    @classmethod
    def get_config(cls):
        """Get the configuration instance, creating it if it doesn't exist"""
        try:
            return cls.objects.get(pk=1)
        except cls.DoesNotExist:
            try:
                # Try to create a new configuration
                config = cls(pk=1)
                config.save()
                return config
            except Exception:
                # If we can't create it (e.g., during migrations), return a default instance
                # that's not saved to the database
                return cls(pk=1)

    def __str__(self):
        return "Email Configuration"


class EmailCounter(models.Model):
    """Track email sending rates to stay within Dreamhost limits"""
    date = models.DateField(default=timezone.now)
    hour = models.IntegerField()  # 0-23
    count = models.IntegerField(default=0)

    class Meta:
        unique_together = ('date', 'hour')
        verbose_name = "Email Counter"
        verbose_name_plural = "Email Counters"

    @classmethod
    def increment(cls):
        """Increment the counter for the current hour"""
        now = timezone.now()
        current_hour = now.hour
        current_date = now.date()

        counter, created = cls.objects.get_or_create(
            date=current_date,
            hour=current_hour,
            defaults={'count': 0}
        )
        counter.count += 1
        counter.save()
        return counter.count

    @classmethod
    def get_current_count(cls):
        """Get the email count for the current hour"""
        now = timezone.now()
        try:
            counter = cls.objects.get(date=now.date(), hour=now.hour)
            return counter.count
        except cls.DoesNotExist:
            return 0

    @classmethod
    def can_send_email(cls, limit=None):
        """Check if we can send more emails this hour"""
        try:
            if limit is None:
                # Get limit from configuration
                try:
                    config = EmailConfiguration.get_config()
                    limit = config.hourly_email_limit
                except Exception:
                    # Default limit if we can't get configuration
                    limit = 90

            current_count = cls.get_current_count()
            return current_count < limit
        except Exception:
            # If we can't check the counter, assume we can send
            # This ensures emails still work during migrations
            return True

    def __str__(self):
        return f"{self.date} {self.hour}:00 - {self.count} emails"


class QueuedEmail(models.Model):
    """Store emails to be sent later when rate limits allow"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('failed', 'Failed'),
    ]
    PRIORITY_CHOICES = [
        (1, 'Critical'),  # Account verification, order confirmations
        (2, 'Important'),  # Digital content delivery
        (3, 'Normal'),     # Book access approvals
        (4, 'Low'),        # Newsletters, marketing
    ]

    subject = models.CharField(max_length=255)
    recipient = models.EmailField()
    html_content = models.TextField()
    text_content = models.TextField()
    from_email = models.EmailField()

    # For attachments
    has_attachments = models.BooleanField(default=False)
    attachment_data = models.TextField(blank=True, null=True,
                                      help_text="JSON data for attachments")

    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=3)

    # For tracking failures
    error_message = models.TextField(blank=True, null=True)
    retry_count = models.IntegerField(default=0)
    last_attempt = models.DateTimeField(null=True, blank=True)

    # For tracking what the email is related to
    content_type = models.CharField(max_length=50, blank=True, null=True)  # e.g., 'order', 'verification'
    object_id = models.CharField(max_length=50, blank=True, null=True)     # e.g., order ID

    class Meta:
        ordering = ['priority', 'created_at']  # Process high priority first
        verbose_name = "Queued Email"
        verbose_name_plural = "Queued Emails"

    def __str__(self):
        return f"{self.subject} to {self.recipient} ({self.get_status_display()})"

    def get_attachments(self):
        """Get attachment data as Python objects"""
        if not self.has_attachments or not self.attachment_data:
            return []

        try:
            return json.loads(self.attachment_data)
        except:
            return []

    def add_attachment(self, file_path, file_name, mimetype='application/pdf'):
        """Add an attachment to the email"""
        attachments = self.get_attachments()

        # Add the new attachment
        attachments.append({
            'path': file_path,
            'filename': file_name,
            'mimetype': mimetype
        })

        # Update the attachment data
        self.has_attachments = True
        self.attachment_data = json.dumps(attachments)
        self.save(update_fields=['has_attachments', 'attachment_data'])

        return True
