{% extends "contest/base.html" %}
{% load static %}

{% block contest_title %}Tempus Quest Contests{% endblock %}

{% block contest_content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">Welcome to Tempus Quest!</h2>
                <p class="card-text">
                    Tempus Quest is a creative illustration contest where you can showcase your artistic talents
                    inspired by The Age of New Era book series. Create illustrations based on your favorite scenes
                    from the books and win exciting prizes!
                </p>
            </div>
        </div>
    </div>
</div>

{% if contests %}
    <div class="row">
        {% for contest in contests %}
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h3 class="card-title">{{ contest.name }}</h3>
                        <p class="card-text">{{ contest.description|truncatewords:30 }}</p>
                        <p class="card-text">
                            <small class="text-muted">
                                {{ contest.start_date|date:"F j, Y" }} - {{ contest.end_date|date:"F j, Y" }}
                            </small>
                        </p>
                    </div>
                    <div class="card-footer bg-transparent">
                        <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-primary">View Contest</a>
                        <a href="{% url 'contest:contest_terms' slug=contest.slug %}" class="btn btn-outline-secondary">Terms & Conditions</a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h3 class="card-title mb-0">No Active Contests</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <img src="{% static 'contest/img/coming_soon.svg' %}" alt="Coming Soon" class="img-fluid" style="max-height: 200px;">
                    </div>

                    <div class="alert alert-info">
                        <h4 class="alert-heading">Stay Tuned!</h4>
                        <p>There are no active contests at the moment, but we're planning exciting new opportunities for artists to showcase their talents!</p>
                    </div>

                    <h4>What is Tempus Quest?</h4>
                    <p>Tempus Quest is a creative illustration contest where artists can create artwork inspired by "The Age of New Era" book series. Participants can:</p>
                    <ul>
                        <li>Request access to book parts for inspiration</li>
                        <li>Submit original artwork in various categories</li>
                        <li>Win prizes and recognition for their creative interpretations</li>
                        <li>Connect with other artists and fans of the series</li>
                    </ul>

                    {% if upcoming_contests %}
                    <h4>Upcoming Contests</h4>
                    <div class="row mb-4">
                        {% for contest in upcoming_contests %}
                            <div class="col-md-6">
                                <div class="card h-100 border-primary">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="card-title mb-0">{{ contest.name }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">{{ contest.description|truncatewords:20 }}</p>
                                        <p><strong>Starts:</strong> {{ contest.start_date|date:"F j, Y" }}</p>
                                        <p><strong>Submissions Open:</strong> {{ contest.submissions_open_date|date:"F j, Y" }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    {% if past_contests %}
                    <h4>Past Contests</h4>
                    <div class="row mb-4">
                        {% for contest in past_contests %}
                            <div class="col-md-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ contest.name }}</h5>
                                        <p class="card-text small">{{ contest.description|truncatewords:10 }}</p>
                                        <p class="small text-muted">Ended: {{ contest.end_date|date:"F j, Y" }}</p>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <h4>Prepare for Future Contests</h4>
                    <p>While you wait for the next contest to begin, you can:</p>
                    <ul>
                        <li>Explore the <a href="{% url 'works' %}">books</a> to get familiar with the story</li>
                        <li>Practice your illustration skills</li>
                        <li>Follow us on social media for contest announcements</li>
                        <li>Sign up for our newsletter to be notified when new contests are launched</li>
                    </ul>

                    <div class="text-center mt-4">
                        <a href="{% url 'works' %}" class="btn btn-primary">Explore Books</a>
                        <a href="{% url 'blog' %}" class="btn btn-outline-secondary">Read Our Blog</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
