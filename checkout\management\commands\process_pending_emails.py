"""
Management command to process pending order emails.
"""
import logging
from django.core.management.base import BaseCommand
from checkout.models import Order
from checkout.combined_email import send_combined_order_email
from checkout.free_book_email import send_free_book_email

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Process pending order emails'

    def handle(self, *args, **options):
        # Get orders with pending emails
        pending_orders = Order.objects.filter(
            email_delivery_status='pending'
        ).exclude(
            payment_status='pending'
        )
        
        self.stdout.write(f"Found {pending_orders.count()} orders with pending emails")
        
        for order in pending_orders:
            self.stdout.write(f"Processing order {order.id} with payment status {order.payment_status}")
            
            if order.payment_status == 'paid':
                success = send_combined_order_email(order)
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f"Email sent for order {order.id}")
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f"Failed to send email for order {order.id}")
                    )
            elif order.payment_status == 'free_book':
                success = send_free_book_email(order)
                if success:
                    self.stdout.write(
                        self.style.SUCCESS(f"Free book email sent for order {order.id}")
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f"Failed to send free book email for order {order.id}")
                    )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f"Skipping order {order.id} with status {order.payment_status}"
                    )
                )
