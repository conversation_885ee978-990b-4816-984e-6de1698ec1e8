{% extends 'base.html' %}
{% load static %}

{% block title %}{{ contest.name }} - Leaderboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'contest/css/contest.css' %}?v={% now 'U' %}">
<style>
    /* Leaderboard specific styles */
    .leaderboard-header {
        position: relative;
        padding: 2rem 0;
        margin-bottom: 2rem;
        color: white;
    }

    .leaderboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.75);
        z-index: -1;
        border-radius: 0.5rem;
    }

    .leaderboard-header h1,
    .leaderboard-header p,
    .leaderboard-header .breadcrumb-item,
    .leaderboard-header .breadcrumb-item a,
    .leaderboard-header .breadcrumb-item.active,
    .leaderboard-header .breadcrumb-item + .breadcrumb-item::before {
        color: white !important;
    }

    .category-buttons {
        position: relative;
        padding: 1rem;
        margin-bottom: 2rem;
        border-radius: 0.5rem;
        background-color: rgba(0, 0, 0, 0.75);
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="leaderboard-header">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'home' %}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'contest:contest_list' %}">Contests</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'contest:contest_detail' slug=contest.slug %}">{{ contest.name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Leaderboard</li>
                    </ol>
                </nav>

                <div class="mb-4">
                    <h1 class="display-4">{{ contest.name }} - Leaderboard</h1>
                    <p class="lead">See the top-performing submissions in the contest!</p>
                </div>
            </div>

            <div class="category-buttons mb-4">
                <div class="btn-group">
                    <a href="{% url 'contest:leaderboard' slug=contest.slug %}" class="btn btn-outline-primary {% if not current_category %}active{% endif %}">All Categories</a>
                    {% for category in categories %}
                        <a href="{% url 'contest:leaderboard' slug=contest.slug %}?category={{ category.name }}" class="btn btn-outline-primary {% if current_category == category.name %}active{% endif %}">{{ category.name }}</a>
                    {% endfor %}
                </div>
            </div>

            <div class="row">
                <div class="col-md-4">
                    <div class="card mb-4 leaderboard-card">
                        <div class="card-header bg-primary text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-thumbs-up me-2"></i> Most Liked
                            </h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {% for submission in top_liked %}
                                    <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h5 class="mb-1">{{ submission.title }}</h5>
                                                <p class="mb-1 text-muted">by {{ submission.user.username }}</p>
                                                <small>{{ submission.category.name }}</small>
                                            </div>
                                            <span class="badge bg-primary rounded-pill">
                                                <i class="fas fa-thumbs-up"></i> {{ submission.like_count }}
                                            </span>
                                        </div>
                                    </a>
                                {% empty %}
                                    <div class="list-group-item">
                                        <p class="text-center mb-0">No submissions yet</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card mb-4 leaderboard-card">
                        <div class="card-header bg-success text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-star me-2"></i> Jury Favorites
                            </h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {% for submission in top_jury %}
                                    <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h5 class="mb-1">{{ submission.title }}</h5>
                                                <p class="mb-1 text-muted">by {{ submission.user.username }}</p>
                                                <small>{{ submission.category.name }}</small>
                                            </div>
                                            <span class="badge bg-success rounded-pill">
                                                <i class="fas fa-star"></i> {{ submission.jury_score }}
                                            </span>
                                        </div>
                                    </a>
                                {% empty %}
                                    <div class="list-group-item">
                                        <p class="text-center mb-0">No jury scores yet</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card mb-4 leaderboard-card">
                        <div class="card-header bg-info text-white">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-comment me-2"></i> Most Discussed
                            </h3>
                        </div>
                        <div class="card-body p-0">
                            <div class="list-group list-group-flush">
                                {% for submission in most_commented %}
                                    <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="list-group-item list-group-item-action">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h5 class="mb-1">{{ submission.title }}</h5>
                                                <p class="mb-1 text-muted">by {{ submission.user.username }}</p>
                                                <small>{{ submission.category.name }}</small>
                                            </div>
                                            <span class="badge bg-info rounded-pill">
                                                <i class="fas fa-comment"></i> {{ submission.comment_count }}
                                            </span>
                                        </div>
                                    </a>
                                {% empty %}
                                    <div class="list-group-item">
                                        <p class="text-center mb-0">No comments yet</p>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <a href="{% url 'contest:submission_gallery' slug=contest.slug %}" class="btn btn-primary">
                    <i class="fas fa-images"></i> View All Submissions
                </a>
                <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Contest
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'contest/js/contest.js' %}"></script>
{% endblock %}
