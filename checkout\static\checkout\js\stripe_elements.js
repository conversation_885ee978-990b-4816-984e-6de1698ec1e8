/**
 * Stripe payment integration for the checkout process using Payment Element.
 * 
 * This module handles:
 * - Stripe Payment Element initialization and styling
 * - Real-time payment form validation
 * - Payment form submission and processing
 * - Error handling and display
 * - Loading state management
 * - Postcode validation with custom regex
 * 
 * Dependencies:
 * - jQuery for DOM manipulation and animations
 * - Stripe.js library for payment processing
 * - Font Awesome for error icons
 */

document.addEventListener('DOMContentLoaded', function() {
    // Safely get elements and handle potential errors
    try {
        var stripePublicKeyElement = $('#id_stripe_public_key');
        var clientSecretElement = $('#id_client_secret');
        
        if (!stripePublicKeyElement.length || !clientSecretElement.length) {
            console.log('Stripe elements not found on this page');
            return;
        }
        
        var stripePublicKey = stripePublicKeyElement.text().slice(1, -1);
        var clientSecret = clientSecretElement.text().slice(1, -1);
        
        if (!stripePublicKey || !clientSecret) {
            console.log('Stripe keys not available');
            return;
        }
        
        // Initialize Stripe with error handling
        var stripe;
        try {
            stripe = Stripe(stripePublicKey);
            console.log('Stripe initialized successfully');
        } catch (error) {
            console.error('Error initializing Stripe:', error.message);
            handlePaymentError('Could not initialize payment system. Please try again later.');
            return;
        }
        
        // Create and mount the Payment Element
        const options = {
            clientSecret: clientSecret,
            appearance: {
                theme: 'stripe',
                variables: {
                    colorPrimary: '#007bff',
                    colorBackground: '#ffffff',
                    colorText: '#000000',
                    colorDanger: '#dc3545',
                    fontFamily: '"Helvetica Neue", Helvetica, sans-serif',
                    spacingUnit: '4px',
                    borderRadius: '4px'
                },
                rules: {
                    '.Label': {
                        color: '#000000'
                    }
                }
            }
        };

        // Initialize Elements
        const elements = stripe.elements(options);
        console.log('Stripe Elements initialized');
        
        // Create and mount the Payment Element
        const paymentElement = elements.create('payment', {
            layout: {
                type: 'tabs',
                defaultCollapsed: false
            }
        });
        
        paymentElement.mount('#payment-element');
        console.log('Payment Element mounted');
        
        // Listen for ready event
        paymentElement.on('ready', function(event) {
            console.log('Payment Element ready');
        });
        
        paymentElement.on('change', function(event) {
            if (event.error) {
                handlePaymentError(event.error.message);
            } else {
                // Clear any previous errors
                var errorDiv = document.getElementById('card-errors');
                if (errorDiv) {
                    errorDiv.textContent = '';
                }
            }
        });

        // Handle form submission
        var form = document.getElementById('payment-form');
        if (!form) {
            console.log('Payment form not found');
            return;
        }
        
        form.addEventListener('submit', function(ev) {
            ev.preventDefault();
            console.log('Form submission started');
            
            // Disable the submit button to prevent repeated clicks
            var submitButton = document.getElementById('submit-button');
            if (submitButton) {
                submitButton.disabled = true;
            }
            
            // Show loading overlay
            $('#loading-overlay').fadeToggle(100);
            
            // First, submit the form to create the order
            var formData = new FormData(form);
            
            // Get CSRF token from the form
            var csrfToken = $('input[name="csrfmiddlewaretoken"]').val();
            
            console.log('Creating order before processing payment');
            
            // Make an AJAX request to create the order
            $.ajax({
                url: '/checkout/create-order/',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(response) {
                    console.log('Order created successfully:', response);
                    
                    if (response.success && response.order_id) {
                        // Now proceed with payment
                        processPayment(response.order_id);
                    } else {
                        handlePaymentError(response.error || 'Failed to create order');
                        if (submitButton) {
                            submitButton.disabled = false;
                        }
                        $('#loading-overlay').fadeToggle(100);
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('Error creating order:', textStatus, errorThrown);
                    console.error('Response:', jqXHR.responseText);
                    
                    handlePaymentError('An error occurred while creating your order. Please try again.');
                    if (submitButton) {
                        submitButton.disabled = false;
                    }
                    $('#loading-overlay').fadeToggle(100);
                }
            });
            
            // Function to process payment after order is created
            function processPayment(orderId) {
                // Check if save-info was checked to save user's information for next time
                var saveInfo = false;
                var saveInfoCheckbox = document.getElementById('id-save-info');
                if (saveInfoCheckbox && saveInfoCheckbox.checked) {
                    saveInfo = true;
                }
                
                // Get form data to post to the server
                var postData = {
                    'csrfmiddlewaretoken': csrfToken,
                    'client_secret': clientSecret,
                    'save_info': saveInfo,
                    'order_id': orderId
                };
                var url = '/checkout/cache_checkout_data/';
                console.log('Posting to cache_checkout_data with order_id:', orderId);
                
                // Post data to the server before confirming payment
                $.post(url, postData)
                .done(function(response) {
                    console.log('cache_checkout_data response:', response);
                    // Use Stripe's confirmPayment method
                    stripe.confirmPayment({
                        elements,
                        confirmParams: {
                            // Return URL where customer should be redirected after payment
                            return_url: window.location.origin + '/checkout/payment-complete/?order_id=' + orderId,
                        }
                    })
                    .then(function(result) {
                        console.log('confirmPayment result:', result);
                        if (result.error) {
                            // Show error message
                            handlePaymentError(result.error.message);
                            
                            // Re-enable button and hide loading overlay
                            if (submitButton) {
                                submitButton.disabled = false;
                            }
                            $('#loading-overlay').fadeToggle(100);
                        }
                        // The payment is being processed or customer is being redirected
                        // No need to handle success case as we're using return_url
                    })
                    .catch(function(error) {
                        console.error('confirmPayment error:', error);
                        handlePaymentError('An error occurred while processing your payment. Please try again.');
                        if (submitButton) {
                            submitButton.disabled = false;
                        }
                        $('#loading-overlay').fadeToggle(100);
                    });
                })
                .fail(function(jqXHR, textStatus, errorThrown) {
                    console.error('cache_checkout_data error:', textStatus, errorThrown);
                    console.error('Response:', jqXHR.responseText);
                    handlePaymentError('An error occurred while preparing your payment. Please try again.');
                    if (submitButton) {
                        submitButton.disabled = false;
                    }
                    $('#loading-overlay').fadeToggle(100);
                });
            }
        });
    } catch (generalError) {
        console.error('Unexpected error in Stripe setup:', generalError);
        handlePaymentError('An unexpected error occurred. Please try again later.');
    }
});

function handlePaymentError(errorMessage) {
    console.error('Payment error:', errorMessage);
    var errorDiv = document.getElementById('card-errors');
    if (errorDiv) {
        var html = `
            <span class="icon" role="alert">
                <i class="fas fa-times"></i>
            </span>
            <span>${errorMessage}</span>
        `;
        $(errorDiv).html(html);
    } else {
        console.log('Error div not found for displaying:', errorMessage);
    }
    
    // Re-enable submit button if it exists
    var submitButton = document.getElementById('submit-button');
    if (submitButton) {
        submitButton.disabled = false;
    }
    
    // Hide loading overlay
    $('#loading-overlay').fadeToggle(100);
}

document.addEventListener('DOMContentLoaded', function() {
    const postcodeInput = document.getElementById('id_billing_postcode');
    if (postcodeInput) {
        postcodeInput.addEventListener('input', function(e) {
            const postcode = e.target.value;
            const postcodeRegex = /^[A-Za-z0-9-]{4,8}$/;
            const errorDiv = document.getElementById('postcode-error');
            const submitButton = document.getElementById('submit-button');
            
            if (!postcodeRegex.test(postcode)) {
                if (!errorDiv) {
                    const div = document.createElement('div');
                    div.id = 'postcode-error';
                    div.className = 'invalid-feedback d-block';
                    div.textContent = 'Postcode must be 4-8 characters and can contain letters, numbers, and hyphens';
                    e.target.classList.add('is-invalid');
                    e.target.parentNode.appendChild(div);
                }
                submitButton.disabled = true;
            } else {
                if (errorDiv) {
                    errorDiv.remove();
                }
                e.target.classList.remove('is-invalid');
                submitButton.disabled = false;
            }
        });
    }
});