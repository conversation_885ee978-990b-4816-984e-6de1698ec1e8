.StripeElement,
.form-control {
  box-sizing: border-box;
  height: 40px;
  padding: 10px 12px;
  border: 1px solid transparent;
  border-radius: 0px;
  background-color: white;
  box-shadow: 0 1px 3px 0 #e6ebf1;
  -webkit-transition: box-shadow 150ms ease;
  transition: box-shadow 150ms ease;
}

.StripeElement--focus,
.form-control:focus,
.form-control:active {
  box-shadow: 0 1px 3px 0 #cfd7df;
  color: #000 !important;
}

.StripeElement--webkit-autofill {
  background-color: #fefde5 !important;
}

.form-control::placeholder {
    color: #aab7c4;
}

.fieldset-label {
    position: relative;
    right: .5rem;
}

#payment-form .form-control {
    color: #000;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: #fff;
    padding: 12px;
}

/* Payment Element specific styling */
#payment-element {
    margin-bottom: 24px;
    color: #000;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
    border: 1px solid #ced4da;
    min-height: 250px; /* Ensure enough space for the payment form */
    height: auto;
    position: relative;
    z-index: 1; /* Ensure it doesn't leak under other elements */
}

/* Make sure the Payment Element is contained within its parent */
#payment-element iframe {
    width: 100% !important;
    max-width: 100% !important;
}

.form-control {
    color: #000 !important;
}

select.form-control {
    color: #000 !important;
}

.overlay {
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    position: fixed;
    background: rgba(0, 0, 0, 0.85);
    z-index: 9999;
    display: none;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    height: 100%;
}

/* Postcode help text */
#id_billing_postcode_helptext {
    color: #000;
    font-size: 12px;
}
/* Country list */
select:invalid {
    color: #aab7c4;
}
