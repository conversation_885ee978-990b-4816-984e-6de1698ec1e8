// Unregister Service Workers
(function() {
    if ('serviceWorker' in navigator) {
        console.log('Checking service worker registration status...');
        
        // Check if this is an auth or profile page
        const isAuthPage = window.location.pathname.includes('/accounts/') || 
                            window.location.pathname.includes('/profile/') ||
                            window.location.pathname.includes('/admin/');
        
        // Only unregister on auth pages
        if (isAuthPage) {
            console.log('Auth page detected, unregistering service workers for security...');
            // Unregister all service workers
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for (let registration of registrations) {
                    registration.unregister();
                    console.log('Service Worker unregistered');
                }
            }).catch(function(error) {
                console.error('Error unregistering service workers:', error);
            });
        }
    }
})();

// Service Worker Registration for Enhanced Audio Caching
(function() {
    // Check if service workers are supported
    if ('serviceWorker' in navigator) {
        console.log('Service Worker is supported in this browser.');
        
        // Wait until the page is fully loaded
        window.addEventListener('load', function() {
            // Don't register service worker on authentication pages
            if (window.location.pathname.includes('/accounts/') || 
                window.location.pathname.includes('/profile/') ||
                window.location.pathname.includes('/admin/')) {
                console.log('Skipping service worker registration on authentication page');
                return;
            }
            
            console.log('Attempting to register Enhanced Service Worker...');
            
            // Use the enhanced service worker
            const swPath = '/serviceworker.js';
            
            // Register the service worker
            navigator.serviceWorker.register(swPath)
                .then(function(registration) {
                    console.log('✅ Service Worker registered successfully!');
                    console.log('📋 Scope: ' + registration.scope);
                    
                    // Check if there's a "refresh" in the URL params (for debug purposes)
                    const urlParams = new URLSearchParams(window.location.search);
                    const forceRefresh = urlParams.has('refresh');
                    
                    // If there's a new service worker waiting, update immediately in development
                    if (registration.waiting && (isLocalEnvironment() || forceRefresh)) {
                        console.log('New service worker detected, updating immediately');
                        registration.waiting.postMessage({ command: 'skipWaiting' });
                    }
                    
                    // Listen for controlling service worker changes
                    navigator.serviceWorker.addEventListener('controllerchange', function() {
                        console.log('Service Worker controller changed!');
                        // Only reload if this is a normal refresh (not hard refresh)
                        if (!window.performance.getEntriesByType('navigation')[0].type.includes('reload')) {
                            console.log('Reloading page for new Service Worker...');
                            window.location.reload();
                        }
                    });
                    
                    // Listen for new service worker installations
                    registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        
                        if (newWorker) {
                            newWorker.addEventListener('statechange', function() {
                                console.log('Service Worker state changed:', newWorker.state);
                                
                                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                    // New service worker is ready, but waiting - show update prompt
                                    // or automatically update in development
                                    if (isLocalEnvironment()) {
                                        newWorker.postMessage({ command: 'skipWaiting' });
                                    } else {
                                        showServiceWorkerUpdatePrompt();
                                    }
                                }
                            });
                        }
                    });
                    
                    // Pre-cache important resources for the current page
                    if (navigator.serviceWorker.controller) {
                        preCacheCurrentPageResources();
                    }
                })
                .catch(function(error) {
                    console.error('❌ Service Worker registration failed: ', error);
                });
        });
        
        // Listen for F5 refresh - ensure proper handling
        if (window.performance && window.performance.getEntriesByType) {
            const navEntry = window.performance.getEntriesByType('navigation')[0];
            if (navEntry && navEntry.type === 'reload') {
                // This is a refresh, we might want to handle it differently
                console.log('Page refresh detected');
            }
        }
    } else {
        console.warn('Service Workers are not supported in this browser');
    }
})();

// Pre-cache resources for the current page
function preCacheCurrentPageResources() {
    // Don't pre-cache on auth pages
    if (window.location.pathname.includes('/accounts/') || 
        window.location.pathname.includes('/profile/') ||
        window.location.pathname.includes('/admin/')) {
        return;
    }
    
    // Resources to pre-cache based on the current page
    let resourcesToPrecache = [];
    
    // Add current page URL
    resourcesToPrecache.push(window.location.pathname);
    
    // If on a work page, add related resources - but only if we know the API exists
    if (window.location.pathname.includes('/works/')) {
        // Only try to pre-cache API endpoints if we're sure they exist
        // This prevents 404 errors when trying to cache non-existent endpoints
        
        // Check if the API endpoints exist before adding them to the cache
        const pathParts = window.location.pathname.split('/').filter(Boolean);
        
        // Make sure we're on a work detail page, not the audio player or other pages
        if (pathParts.length === 2 && pathParts[0] === 'works') {
            const workId = pathParts[1];
            
            // Only proceed if workId is a number
            if (workId && !isNaN(workId)) {
                // Use fetch with HEAD method to check if the API endpoints exist
                // without actually downloading the content
                const checkApiEndpoint = async (endpoint) => {
                    try {
                        const response = await fetch(endpoint, { method: 'HEAD' });
                        return response.ok;
                    } catch (error) {
                        console.warn(`API endpoint ${endpoint} is not available:`, error);
                        return false;
                    }
                };
                
                // Only add API endpoints to cache if they exist
                (async () => {
                    const workApiUrl = `/api/works/${workId}/`;
                    const chaptersApiUrl = `/api/works/${workId}/chapters/`;
                    
                    const workApiExists = await checkApiEndpoint(workApiUrl);
                    const chaptersApiExists = await checkApiEndpoint(chaptersApiUrl);
                    
                    let apiEndpointsToCache = [];
                    
                    if (workApiExists) {
                        apiEndpointsToCache.push(workApiUrl);
                    }
                    
                    if (chaptersApiExists) {
                        apiEndpointsToCache.push(chaptersApiUrl);
                    }
                    
                    if (apiEndpointsToCache.length > 0 && window.swManager) {
                        console.log('Pre-caching API endpoints:', apiEndpointsToCache);
                        window.swManager.preCacheResources(apiEndpointsToCache);
                    }
                })();
            }
        }
    }
    
    // If on library page, add library API - only if we know it exists
    if (window.location.pathname.includes('/library/')) {
        // Check if the library API endpoint exists before caching
        (async () => {
            try {
                const response = await fetch('/api/library/', { method: 'HEAD' });
                if (response.ok && window.swManager) {
                    console.log('Pre-caching library API endpoint');
                    window.swManager.preCacheResources(['/api/library/']);
                }
            } catch (error) {
                console.warn('Library API endpoint is not available:', error);
            }
        })();
    }
    
    // If we have resources to pre-cache and the service worker manager exists
    if (resourcesToPrecache.length > 0 && window.swManager) {
        console.log('Pre-caching resources for current page:', resourcesToPrecache);
        window.swManager.preCacheResources(resourcesToPrecache);
    }
}

// Authentication State Management
(function() {
    // Function to check if user is logged in
    function checkLoginState() {
        // Look for elements that indicate logged-in state
        const loggedInIndicators = [
            document.querySelector('#userDropdown'), // User dropdown in navbar
            document.querySelector('a[href="/accounts/logout/"]'), // Logout link
            document.querySelector('a[href*="profile"]') // Profile link
        ];
        
        const isLoggedIn = loggedInIndicators.some(el => el !== null);
        
        // If we're on a page that requires authentication but we're not logged in
        if (window.location.pathname.includes('/profile/') && !isLoggedIn) {
            console.log('⚠️ Authentication state mismatch detected. Reloading page...');
            window.location.reload();
        }
        
        return isLoggedIn;
    }
    
    // Check login state when page loads
    document.addEventListener('DOMContentLoaded', function() {
        const isLoggedIn = checkLoginState();
        console.log(`🔐 User login state: ${isLoggedIn ? 'Logged in' : 'Not logged in'}`);
        
        // If we're on a login/logout page, force a reload after navigation
        if (window.location.pathname.includes('/accounts/login/') || 
            window.location.pathname.includes('/accounts/logout/')) {
            
            // Store a flag in sessionStorage
            sessionStorage.setItem('auth_action_performed', 'true');
        }
        
        // Check if we just performed an auth action and are now on a different page
        if (sessionStorage.getItem('auth_action_performed') === 'true' && 
            !window.location.pathname.includes('/accounts/')) {
            
            // Clear the flag
            sessionStorage.removeItem('auth_action_performed');
            
            // Reload the page to ensure fresh state
            window.location.reload();
        }
    });
})();

// CSRF Token Handling
(function() {
    // Function to refresh CSRF token
    function refreshCSRFToken() {
        // Only run on pages with forms
        if (document.querySelector('form')) {
            try {
                fetch('/', { 
                    credentials: 'same-origin',
                    // Add a timeout to prevent long-running requests
                    signal: AbortSignal.timeout(5000) // 5 second timeout
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const newToken = doc.querySelector('input[name="csrfmiddlewaretoken"]');
                    
                    if (newToken) {
                        // Update all CSRF tokens in the page
                        document.querySelectorAll('input[name="csrfmiddlewaretoken"]').forEach(token => {
                            token.value = newToken.value;
                        });
                        console.log('CSRF tokens refreshed');
                    }
                })
                .catch(error => {
                    console.warn('CSRF token refresh skipped:', error);
                    // Don't treat this as a critical error, just a warning
                });
            } catch (error) {
                console.warn('CSRF token refresh attempt failed:', error);
                // Catch any synchronous errors (like AbortSignal not being supported)
            }
        }
    }
    
    // Refresh CSRF token when page becomes visible again
    // but only if the page has been hidden for more than 5 minutes
    let lastHiddenTime = 0;
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'hidden') {
            lastHiddenTime = Date.now();
        } else if (document.visibilityState === 'visible') {
            // Only refresh if the page was hidden for more than 5 minutes
            if (Date.now() - lastHiddenTime > 5 * 60 * 1000) {
                refreshCSRFToken();
            }
        }
    });
    
    // Refresh CSRF token after login/logout operations
    if (window.location.pathname.includes('/accounts/') || 
        window.location.pathname.includes('/profile/')) {
        refreshCSRFToken();
    }
})();

// Mobile Menu Overlay
document.addEventListener('DOMContentLoaded', function() {
    const overlay = document.querySelector('.mobile-menu-overlay');
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (!overlay || !navbarToggler || !navbarCollapse) {
        console.warn('Mobile menu overlay elements not found');
        return;
    }
    
    // Function to show overlay
    function showOverlay() {
        overlay.classList.add('show');
        console.log('Overlay shown');
    }
    
    // Function to hide overlay
    function hideOverlay() {
        overlay.classList.remove('show');
        console.log('Overlay hidden');
    }
    
    // Toggle overlay when navbar toggler is clicked
    navbarToggler.addEventListener('click', function() {
        console.log('Navbar toggler clicked');
        if (navbarCollapse.classList.contains('show')) {
            hideOverlay();
        } else {
            showOverlay();
        }
    });
    
    // Hide overlay when clicking on it
    overlay.addEventListener('click', function() {
        console.log('Overlay clicked');
        if (navbarCollapse.classList.contains('show')) {
            const bsCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
            if (bsCollapse) {
                bsCollapse.hide();
            } else {
                navbarCollapse.classList.remove('show');
            }
            hideOverlay();
        }
    });
    
    // Listen for Bootstrap collapse events
    navbarCollapse.addEventListener('shown.bs.collapse', function() {
        console.log('Navbar shown');
        showOverlay();
    });
    
    navbarCollapse.addEventListener('hidden.bs.collapse', function() {
        console.log('Navbar hidden');
        hideOverlay();
    });
});

// Helper function to check if we're in a local environment
function isLocalEnvironment() {
    // Implement the logic to determine if the environment is local
    // This is a placeholder and should be replaced with the actual implementation
    return false;
}

// Utility function to force reload the page
window.forceReload = function() {
    // Clear any cache in the page
    window.location.reload(true);
};

// Utility function to update the service worker
window.updateServiceWorker = function() {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistration().then(function(registration) {
            if (registration && registration.waiting) {
                // Send a message to the service worker to skip waiting
                registration.waiting.postMessage({ command: 'skipWaiting' });
            }
        });
    }
};

// Add a utility function to check if an API endpoint exists
async function checkApiEndpointExists(url) {
    try {
        const response = await fetch(url, { method: 'HEAD' });
        return response.ok;
    } catch (error) {
        console.warn(`API endpoint ${url} is not available:`, error);
        return false;
    }
}

// Initialize service worker when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // ... existing code ...
}); 