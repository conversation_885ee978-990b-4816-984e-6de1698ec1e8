from django.core.management.base import BaseCommand
from blog.models import Post


class Command(BaseCommand):
    help = 'Test blog post image cropping functionality'

    def handle(self, *args, **options):
        posts = Post.objects.filter(post_featured_image__isnull=False)[:3]
        
        if not posts:
            self.stdout.write(
                self.style.WARNING('No posts with featured images found')
            )
            return
        
        for post in posts:
            self.stdout.write(f"\n--- Testing Post: {post.post_title} ---")
            self.stdout.write(f"Original image: {post.post_featured_image.url}")
            
            # Check crop data
            self.stdout.write(f"Featured cropping: {post.featured_cropping}")
            self.stdout.write(f"Card cropping: {post.card_cropping}")
            self.stdout.write(f"Detail cropping: {post.detail_cropping}")
            
            # Test URL generation
            try:
                featured_url = post.get_featured_image_url('featured')
                card_url = post.get_featured_image_url('card')
                detail_url = post.get_featured_image_url('detail')
                
                self.stdout.write(f"Featured URL: {featured_url}")
                self.stdout.write(f"Card URL: {card_url}")
                self.stdout.write(f"Detail URL: {detail_url}")
                
                # Check if URLs are different (indicating cropping is working)
                original_url = post.post_featured_image.url
                if featured_url != original_url:
                    self.stdout.write(
                        self.style.SUCCESS('✓ Cropping appears to be working!')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING('⚠ URLs are the same - cropping may not be applied')
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error generating URLs: {e}')
                )
