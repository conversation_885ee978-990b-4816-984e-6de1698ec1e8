# Generated by Django 5.1.5 on 2025-02-07 15:13

import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('blog', '0003_alter_comment_active'),
        ('works', '0003_alter_review_comment_alter_review_rating'),
    ]

    operations = [
        migrations.CreateModel(
            name='CarouselItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('subtitle', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('image', models.ImageField(upload_to='carousel/')),
                ('alt_text', models.CharField(blank=True, help_text='Alternative text for accessibility', max_length=255)),
                ('style', models.CharField(choices=[('product', 'Product Style'), ('blog', 'Blog Style'), ('news', 'News Style'), ('external', 'External Style')], default='external', max_length=10)),
                ('external_link', models.URLField(blank=True, null=True)),
                ('open_in_new_tab', models.BooleanField(default=False)),
                ('start_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('end_date', models.DateTimeField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('order', models.IntegerField(default=0, help_text='Order of appearance in carousel')),
                ('cta_text', models.CharField(blank=True, help_text='Call to Action button text', max_length=50, null=True)),
                ('cta_style', models.CharField(blank=True, choices=[('primary', 'Primary Button'), ('secondary', 'Secondary Button'), ('link', 'Link Style')], default='primary', max_length=20, null=True)),
                ('blog_post', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='blog.post')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='works.product')),
            ],
            options={
                'verbose_name': 'Carousel Item',
                'verbose_name_plural': 'Carousel Items',
                'ordering': ['order', '-start_date'],
            },
        ),
    ]
