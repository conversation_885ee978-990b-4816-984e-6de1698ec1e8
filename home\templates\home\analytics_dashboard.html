{% extends "base.html" %}
{% load static %}

{% block title %}Audio Analytics Dashboard{% endblock %}

{% block extra_css %}
<style>
    .analytics-container {
        padding: 20px;
        max-width: 1200px;
        margin: 0 auto;
        color: #333;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 20px;
        text-align: center;
    }
    
    .stat-card h3 {
        margin-top: 0;
        color: #666;
        font-size: 14px;
        text-transform: uppercase;
    }
    
    .stat-card .value {
        font-size: 32px;
        font-weight: bold;
        color: #333;
        margin: 10px 0;
    }
    
    .stat-card .unit {
        font-size: 14px;
        color: #999;
    }
    
    .chart-container {
        background: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 30px;
        height: 400px;
    }
    
    .chart-container h2 {
        margin-top: 0;
        color: #333;
        font-size: 18px;
        margin-bottom: 20px;
    }
    
    table.data-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    table.data-table th,
    table.data-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }
    
    table.data-table th {
        background: #f5f5f5;
        font-weight: bold;
    }
    
    .filter-form {
        margin-bottom: 20px;
        display: flex;
        align-items: center;
    }
    
    .filter-form label {
        margin-right: 10px;
    }
    
    .filter-form select {
        padding: 5px 10px;
        border-radius: 3px;
        border: 1px solid #ddd;
        margin-right: 10px;
    }
    
    .filter-form button {
        padding: 5px 15px;
        background: #79aec8;
        color: white;
        border: none;
        border-radius: 3px;
        cursor: pointer;
    }
    
    .filter-form button:hover {
        background: #417690;
    }
    
    canvas {
        max-width: 100%;
        height: 300px;
    }
    
    .tab-container {
        margin-bottom: 20px;
    }
    
    .tab-buttons {
        display: flex;
        border-bottom: 1px solid #ddd;
        margin-bottom: 20px;
    }
    
    .tab-button {
        padding: 10px 20px;
        background: none;
        border: none;
        border-bottom: 3px solid transparent;
        cursor: pointer;
        font-weight: bold;
        color: #666;
    }
    
    .tab-button.active {
        border-bottom-color: #79aec8;
        color: #333;
    }
    
    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .warning-message {
        background-color: #fff3cd;
        color: #856404;
        padding: 1rem;
        margin-bottom: 1rem;
        border-radius: 5px;
        border: 1px solid #ffeeba;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="analytics-container">
    <h1>Audio Analytics Dashboard</h1>
    
    {% if not completed_field_exists %}
    <div class="warning-message">
        <strong>Warning:</strong> Database migration required! The 'completed' and 'progress_percentage' fields are missing from the database.
        Run <code>python manage.py migrate</code> to apply the missing migration. Some stats will be unavailable until the migration is applied.
    </div>
    {% endif %}
    
    <form class="filter-form" method="get">
        <label for="days">Time period:</label>
        <select name="days" id="days">
            <option value="7" {% if days == 7 %}selected{% endif %}>Last 7 days</option>
            <option value="30" {% if days == 30 %}selected{% endif %}>Last 30 days</option>
            <option value="90" {% if days == 90 %}selected{% endif %}>Last 90 days</option>
            <option value="365" {% if days == 365 %}selected{% endif %}>Last year</option>
        </select>
        <button type="submit">Apply</button>
    </form>
    
    <div class="stats-grid">
        <div class="stat-card">
            <h3>Total Sessions</h3>
            <div class="value">{{ total_sessions }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Unique Users</h3>
            <div class="value">{{ total_users }}</div>
        </div>
        
        <div class="stat-card">
            <h3>Total Listening Time</h3>
            <div class="value">{{ total_duration|floatformat:0 }}</div>
            <div class="unit">minutes</div>
        </div>
        
        <div class="stat-card">
            <h3>Avg. Session Duration</h3>
            <div class="value">{{ avg_session_duration|floatformat:1 }}</div>
            <div class="unit">minutes</div>
        </div>
        
        <div class="stat-card">
            <h3>Completed Sessions</h3>
            <div class="value">{{ completed_sessions }}</div>
            <div class="unit">{{ completed_percentage|floatformat:1 }}% of total</div>
            {% if not completed_field_exists %}<div class="unit">(migration needed)</div>{% endif %}
        </div>
        
        <div class="stat-card">
            <h3>Avg. Completion Rate</h3>
            <div class="value">{{ avg_progress|floatformat:1 }}</div>
            <div class="unit">percent</div>
            {% if not completed_field_exists %}<div class="unit">(migration needed)</div>{% endif %}
        </div>
    </div>
    
    <div class="tab-container">
        <div class="tab-buttons">
            <button type="button" class="tab-button active" data-tab="charts">Charts</button>
            <button type="button" class="tab-button" data-tab="works">Most Active Works</button>
            <button type="button" class="tab-button" data-tab="users">Most Active Users</button>
        </div>
        
        <div id="charts-tab" class="tab-content active">
            <div class="chart-container">
                <h2>Sessions by Day</h2>
                <canvas id="sessionsChart"></canvas>
            </div>
            
            <div class="chart-container">
                <h2>Events by Type</h2>
                <canvas id="eventsChart"></canvas>
            </div>
        </div>
        
        <div id="works-tab" class="tab-content">
            <div class="chart-container" style="height: auto;">
                <h2>Most Active Works</h2>
                {% if not completed_field_exists %}
                <div class="warning-message">
                    Completion rate data unavailable until database migration is applied.
                </div>
                {% endif %}
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Work ID</th>
                            <th>Sessions</th>
                            <th>Total Duration (min)</th>
                            <th>Avg. Completion Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for work in most_active_works %}
                        <tr>
                            <td>{{ work.work_id }}</td>
                            <td>{{ work.count }}</td>
                            <td>{{ work.total_duration|floatformat:1 }}</td>
                            <td>{{ work.completion_rate|floatformat:1 }}%</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4">No data available</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <div id="users-tab" class="tab-content">
            <div class="chart-container" style="height: auto;">
                <h2>Most Active Users</h2>
                {% if not completed_field_exists %}
                <div class="warning-message">
                    Completion rate data unavailable until database migration is applied.
                </div>
                {% endif %}
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>User</th>
                            <th>Sessions</th>
                            <th>Total Duration (min)</th>
                            <th>Avg. Completion Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in most_active_users %}
                        <tr>
                            <td>
                                {% if user.user__username %}
                                    {{ user.user__username }}
                                {% else %}
                                    Anonymous ({{ user.anonymous_user_id }})
                                {% endif %}
                            </td>
                            <td>{{ user.count }}</td>
                            <td>{{ user.total_duration|floatformat:1 }}</td>
                            <td>{{ user.completion_rate|floatformat:1 }}%</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="4">No data available</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab functionality
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');
        
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                // Remove active class from all buttons and contents
                tabButtons.forEach(btn => btn.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Add active class to clicked button and corresponding content
                button.classList.add('active');
                const tabId = button.getAttribute('data-tab');
                document.getElementById(`${tabId}-tab`).classList.add('active');
            });
        });
        
        // Sessions by day chart
        const sessionsLabels = [
            {% for item in sessions_by_day %}
                '{{ item.day|date:"M d" }}',
            {% endfor %}
        ];
        
        const sessionsData = [
            {% for item in sessions_by_day %}
                {{ item.count }},
            {% endfor %}
        ];
        
        const sessionsCtx = document.getElementById('sessionsChart').getContext('2d');
        new Chart(sessionsCtx, {
            type: 'line',
            data: {
                labels: sessionsLabels,
                datasets: [{
                    label: 'Number of Sessions',
                    data: sessionsData,
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    borderColor: 'rgba(75, 192, 192, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });
        
        // Events by type chart
        const eventsLabels = [
            {% for item in events_by_type %}
                '{{ item.event_type }}',
            {% endfor %}
        ];
        
        const eventsData = [
            {% for item in events_by_type %}
                {{ item.count }},
            {% endfor %}
        ];
        
        const eventsCtx = document.getElementById('eventsChart').getContext('2d');
        new Chart(eventsCtx, {
            type: 'bar',
            data: {
                labels: eventsLabels,
                datasets: [{
                    label: 'Number of Events',
                    data: eventsData,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });
    });
</script>
{% endblock %} 