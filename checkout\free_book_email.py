import logging
import os
import time
from django.conf import settings
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags

from .models import Order, OrderItem
from email_manager.utils import send_email_with_rate_limit

logger = logging.getLogger(__name__)

def send_free_book_email(order):
    """
    Send a free book email to a customer.
    This function is designed to be called directly or from a background thread.

    Args:
        order: The Order object containing the free book

    Returns:
        bool: True if email was sent successfully, False otherwise
    """
    try:
        # Get order items with digital content
        order_items = OrderItem.objects.filter(order=order)
        digital_items = [item for item in order_items if item.product.has_digital_content()]

        if not digital_items:
            logger.warning(f"No digital content found for order {order.id}")
            order.email_delivery_status = 'failed'
            order.save(update_fields=['email_delivery_status'])
            return False

        # Prepare email context
        context = {
            'order': order,
            'order_items': order_items,
            'digital_items': digital_items,
            'has_digital_content': bool(digital_items),
            'contact_email': settings.CONTACT_DISPLAY_EMAIL,
            'SITE_URL': settings.SITE_URL,
        }

        # Render email templates
        html_message = render_to_string('checkout/emails/free_book_email.html', context)
        text_message = strip_tags(html_message)

        # Create email
        subject = 'Your Free Book from Tempus Author Platform'
        from_email = settings.DEFAULT_FROM_EMAIL
        to_email = order.email

        # Use rate-limited email sending
        email_sent = send_email_with_rate_limit(
            subject=subject,
            recipient=to_email,
            html_content=html_message,
            text_content=text_message,
            priority=1,  # High priority
            content_type='free_book',
            object_id=order.id
        )

        # Attach ebook files if present
        for item in digital_items:
            if item.product.ebook_file:
                try:
                    # Try different methods to get the file path
                    try:
                        # Method 1: Direct path access
                        file_path = item.product.ebook_file.path
                    except (ValueError, AttributeError) as e:
                        logger.warning(f"Could not get direct path: {str(e)}")

                        # Method 2: Construct path from MEDIA_ROOT and name
                        file_path = os.path.join(settings.MEDIA_ROOT, str(item.product.ebook_file))

                        # Verify file exists
                        if not os.path.exists(file_path):
                            logger.warning(f"File does not exist at constructed path: {file_path}")

                            # Method 3: Try to get the URL and add a note in the email
                            # We'll add a note in the email that files can be downloaded from the library
                            continue

                    file_name = os.path.basename(str(item.product.ebook_file))

                    # Add attachment to the email in the queue
                    from email_manager.models import QueuedEmail
                    queued_email = QueuedEmail.objects.filter(
                        content_type='free_book',
                        object_id=order.id
                    ).first()

                    if queued_email:
                        queued_email.add_attachment(file_path, file_name)
                        logger.info(f"Attached ebook file to queued email: {file_name}")
                except Exception as e:
                    logger.error(f"Error attaching ebook file: {str(e)}")

        # Update order status
        if email_sent:
            order.email_delivery_status = 'sent'
            order.save(update_fields=['email_delivery_status'])
            logger.info(f"Free book email sent for order {order.id}")
            return True
        else:
            logger.warning(f"Free book email queued for order {order.id}")
            return False

    except Exception as e:
        logger.error(f"Error sending free book email for order {order.id}: {str(e)}")
        order.email_delivery_status = 'failed'
        order.save(update_fields=['email_delivery_status'])
        return False
