{% extends 'base.html' %}
{% load static %}
{% load blog_extras %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'blog/css/blog.css' %}">
<link rel="stylesheet" href="{% static 'blog/css/post_details.css' %}">
<link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700;900&display=swap" rel="stylesheet">
{% endblock %}

{% block extrahead %}
<link rel="canonical" href="{{ request.build_absolute_uri }}">
{% endblock %}

{% block opengraph %}
<!-- Open Graph / Facebook -->
<meta property="og:type" content="article">
<meta property="og:url" content="{{ request.build_absolute_uri }}">
<meta property="og:title" content="{{ post.post_title }} | Tempus Author Platform">
<meta property="og:description" content="{{ post.post_excerpt|default:post.post_content|truncatechars:200|striptags }}">
<meta property="og:image" content="{% if post.post_featured_image %}{{ MEDIA_URL_WITH_HOST }}{{ post.post_featured_image.name }}{% else %}{{ STATIC_URL_WITH_HOST }}images/noimage.png{% endif %}">
<meta property="og:site_name" content="Tempus Author Platform">
<meta property="og:logo" content="{{ STATIC_URL_WITH_HOST }}images/logo.png">
<meta property="article:published_time" content="{{ post.post_created_on|date:'c' }}">
<meta property="article:author" content="{{ post.post_author }}">
{% if post.post_categories.all %}
<meta property="article:section" content="{{ post.post_categories.first }}">
{% endif %}

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="{{ request.build_absolute_uri }}">
<meta name="twitter:title" content="{{ post.post_title }} | Tempus Author Platform">
<meta name="twitter:description" content="{{ post.post_excerpt|default:post.post_content|truncatechars:200|striptags }}">
<meta name="twitter:image" content="{% if post.post_featured_image %}{{ MEDIA_URL_WITH_HOST }}{{ post.post_featured_image.name }}{% else %}{{ STATIC_URL_WITH_HOST }}images/noimage.png{% endif %}">
{% endblock %}

{% block content %}
<div class="tempus-gradus-blog">
    <!-- Masthead -->
    <div class="masthead text-center py-5">
        <h1 class="tempus-title">TEMPVS GRADVS</h1>
        <div class="masthead-divider">
            <span class="divider-line"></span>
            <span class="divider-icon"><i class="fas fa-hourglass"></i></span>
            <span class="divider-line"></span>
        </div>
        <p class="masthead-subtitle">CHRONICLES OF THE NEW ERA</p>
    </div>

<article class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-lg-8 bg-black bg-opacity-75 p-3 rounded mb-5">
            <!-- Post Header -->
            <header class="mb-4">
                <h1 class="display-4 mb-3">{{ post.post_title }}</h1>
                <div class="meta mb-4">
                    <span class="me-3">
                        <i class="fas fa-user me-2"></i>{{ post.post_author }}
                    </span>
                    <span class="me-3">
                        <i class="fas fa-calendar-alt me-2"></i>{{ post.post_created_on|date:"F j, Y" }}
                    </span>
                </div>
            </header>

            <!-- Featured Image -->
            {% if post.post_featured_image %}
            <div class="featured-image mb-4">
                <img src="{{ post|get_featured_image_url:'detail' }}" alt="{{ post.post_title }}" class="img-fluid rounded">
            </div>
            {% endif %}

            <!-- Post Content -->
            <div class="post-content">
                {{ post.post_content|safe }}
            </div>

            <!-- Navigation -->
            <div class="mt-5 pt-3 border-top">
                <a href="{% url 'blog' %}" class="btn btn-outline-primary" aria-label="Return to blog listing">
                    <i class="fas fa-arrow-left me-2" aria-hidden="true"></i>Back to Blog
                </a>
                <!-- Copy Link Button -->
                <button type="button" class="btn btn-outline-secondary" id="copyLink" aria-label="Copy Link">
                    <i class="fas fa-link"></i>
                </button>
                <!-- Share Button (only visible on mobile devices) -->
                <button type="button" class="btn btn-outline-secondary d-md-none" id="shareButton" aria-label="Share">
                    <i class="fas fa-share"></i>
                </button>
            </div>
            <input type="hidden" name="redirect_url" value="{{ request.path }}">
            
            <!-- Toast Notification -->
            <div class="toast-container position-fixed top-0 end-0 p-3">
                <div id="copyLinkToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-check-circle me-2"></i>Link copied to clipboard
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            </div>

            <!-- Related Posts Section -->
            <div class="related-posts mt-5">
                <h3>Related Posts</h3>
                <div class="related-posts-slider">
                    <div class="row flex-nowrap overflow-auto pb-3">
                        {% for related_post in related_posts %}
                        <div class="col-md-4 col-sm-6 flex-shrink-0">
                            <div class="card blog-card h-100 mt-2">
                                {% if related_post.post_featured_image %}
                                <img src="{{ related_post|get_featured_image_url:'card' }}" class="card-img-top" alt="{{ related_post.post_title }}" style="height: 200px; object-fit: cover;">
                                {% else %}
                                <img src="{% static 'blog/images/default-blog-image.png' %}" class="card-img-top" alt="Default post image" style="height: 200px; object-fit: cover;">
                                {% endif %}
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <a href="{% url 'post_detail' related_post.post_slug %}" class="text-decoration-none text-dark">
                                            {{ related_post.post_title }}
                                        </a>
                                    </h5>
                                    <p class="card-text">{{ related_post.post_excerpt|striptags|safe|truncatechars:100 }}</p>
                                </div>
                                <div class="card-footer bg-transparent border-0">
                                    <a href="{% url 'post_detail' related_post.post_slug %}" class="btn btn-primary">Read More</a>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="col-12">
                            <p>No related posts found.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Tags Section -->
            {% if post.tags.all %}
            <div class="tags-section mt-4">
                <h4>Tags</h4>
                <div class="tags">
                    {% for tag in post.tags.all %}
                    <a href="{% url 'blog' %}?tag={{ tag.slug }}" class="badge bg-secondary text-decoration-none me-1">
                        <i class="fas fa-tag"></i> {{ tag.name }}
                    </a>
                    {% endfor %}
                </div>
            </div>
            {% endif %}

            <!-- Category Section -->
            {% if post.category %}
            <div class="category-section mt-3">
                <h4>Category</h4>
                <a href="{% url 'blog' %}?category={{ post.category.slug }}" class="badge bg-primary text-decoration-none">
                    <i class="fas fa-folder"></i> {{ post.category.name }}
                </a>
            </div>
            {% endif %}

            <!-- Comments Section -->
            <div class="comments-section mt-5">
                <h3>Comments</h3>
                
                {% if user.is_authenticated %}
                    <div class="comment-form mb-4">
                        <form method="post">
                            {% csrf_token %}
                            {{ form.as_p }}
                            <button type="submit" class="btn btn-primary">Post Comment</button>
                        </form>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        Please <a href="{% url 'account_login' %}?next={{ request.path }}" aria-label="Login to leave a comment">login</a> to leave a comment.
                    </div>
                {% endif %}

                <!-- Display Comments -->
                {% if comments %}
                {% for comment in comments %}
                    <div class="comment mb-3 p-3 border rounded">
                        <div class="comment-meta small">
                                <a href="{% url 'public_profile' comment.author.id %}" aria-label="View {{ comment.author }}'s profile">
                                {% if comment.author.profile.profile_image %}
                                    <img src="{{ comment.author.profile.profile_image.url }}" alt="Profile Image" class="rounded-circle nav-profile-image">
                                {% else %}
                                    <img src="{{ MEDIA_URL }}profile_images/default.jpg" alt="Default Profile Image" class="rounded-circle nav-profile-image">
                                {% endif %}
                            <strong>{{ comment.author }}</strong></a> - 
                            {{ comment.created_on|date:"F j, Y" }}
                            
                            {% if user == comment.author %}
                                <div class="float-end">
                                    <a href="{% url 'comment_edit' post.post_slug comment.id %}" 
                                       class="btn btn-sm btn-outline-secondary"
                                       aria-label="Edit your comment">
                                        <i class="fas fa-edit" aria-hidden="true"></i> Edit
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-danger" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#deleteModal{{ comment.id }}">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                        <div class="comment-content mt-2">
                            {{ comment.content }}
                        </div>
                    </div>
                
                    <!-- Delete Modal for each comment -->
                    {% if user == comment.author or user.is_staff %}
                    <div class="modal fade" id="deleteModal{{ comment.id }}" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title text-dark">Confirm Delete</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body text-dark">
                                    Are you sure you want to delete this comment?
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <form action="{% url 'comment_delete' post.post_slug comment.id %}" method="POST" style="display: inline;">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-danger">Delete</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                {% endfor %}
                
                {% else %}
                    <p>No comments yet. Be the first to comment!</p>
                {% endif %}
            </div>
        </div>
    </div>
</article>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'blog/js/post_details_alert.js' %}"></script>
{% endblock %}
