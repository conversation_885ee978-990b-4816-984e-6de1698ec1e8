from django.contrib import admin
from .models import CollaborationRequest
from django.contrib import messages


@admin.register(CollaborationRequest)
class CollaborationRequestAdmin(admin.ModelAdmin):
    """
    Admin configuration for managing collaboration requests.
    """

    list_display = (
        'name', 'email', 'collaboration_type', 'status',
        'is_read', 'created_at'
    )
    list_filter = ('collaboration_type', 'status', 'is_read', 'created_at')
    search_fields = ('name', 'email', 'subject', 'message')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'created_at'
    ordering = ('-created_at',)

    fieldsets = (
        ('Contact Information', {
            'fields': ('name', 'email')
        }),
        ('Request Details', {
            'fields': ('subject', 'collaboration_type', 'message')
        }),
        ('Status Information', {
            'fields': ('status', 'is_read')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['mark_as_read', 'mark_as_unread', 'create_review_from_request']

    def mark_as_read(self, request, queryset):
        """
        Marks selected collaboration requests as read.
        """
        queryset.update(is_read=True, status='READ')
    mark_as_read.short_description = "Mark selected requests as read"

    def mark_as_unread(self, request, queryset):
        """
        Marks selected collaboration requests as unread.
        """
        queryset.update(is_read=False, status='NEW')
    mark_as_unread.short_description = "Mark selected requests as unread"
    
    def create_review_from_request(self, request, queryset):
        """
        Creates a review from a book review collaboration request.
        
        This action extracts the book ID and rating from the message
        and creates a review for the specified product.
        """
        # Only process one request at a time
        if queryset.count() != 1:
            self.message_user(
                request, 
                "Please select exactly one book review request.", 
                level=messages.ERROR
            )
            return
            
        collab_request = queryset.first()
        
        # Check if this is a book review request
        if collab_request.collaboration_type != 'REVIEW':
            self.message_user(
                request, 
                "This action only works with Book Review requests.", 
                level=messages.ERROR
            )
            return
            
        # Parse the message to extract book ID and rating
        message = collab_request.message
        book_id = None
        rating = None
        
        # Extract book ID from the message
        if "Book ID:" in message:
            book_id_parts = message.split("Book ID:", 1)[1].split("\n", 1)
            if book_id_parts:
                try:
                    book_id = int(book_id_parts[0].strip())
                except ValueError:
                    book_id = None
                
        # Extract rating from the message
        if "Rating:" in message:
            rating_parts = message.split("Rating:", 1)[1].split("/", 1)
            if rating_parts:
                try:
                    rating = int(rating_parts[0].strip())
                except ValueError:
                    rating = None
        
        # Get the review text
        review_text = ""
        if "Review:" in message:
            review_parts = message.split("Review:", 1)
            if len(review_parts) > 1:
                review_text = review_parts[1].strip()
        
        if not book_id:
            self.message_user(
                request, 
                "Could not extract book ID from the message.", 
                level=messages.ERROR
            )
            return
            
        # Find the product by ID
        from works.models import Product
        try:
            # Try to find the product by ID
            product = Product.objects.filter(id=book_id).first()
            
            if not product:
                self.message_user(
                    request, 
                    f"No product found with ID {book_id}.", 
                    level=messages.ERROR
                )
                return
                
            # Find the user based on the email
            from django.contrib.auth.models import User
            user = User.objects.filter(email=collab_request.email).first()
            
            if not user:
                error_msg = (
                    f"No user found with email '{collab_request.email}'. "
                    "The user needs to register first."
                )
                self.message_user(request, error_msg, level=messages.ERROR)
                return
                
            # Create the review
            from works.models import Review
            # Create the review and store it in a variable (not used but keeps it explicit)
            Review.objects.create(
                product=product,
                user=user,
                rating=rating or 3,  # Default to 3 if rating couldn't be parsed
                comment=review_text,
                approved=True  # Auto-approve since you're creating it manually
            )
            
            # Mark the collaboration request as completed
            collab_request.status = 'COMPLETED'
            collab_request.save()
            
            success_msg = (
                f"Successfully created review for '{product.name}' "
                f"by {user.username}."
            )
            self.message_user(request, success_msg, level=messages.SUCCESS)
            
        except Exception as e:
            self.message_user(
                request, 
                f"Error creating review: {str(e)}", 
                level=messages.ERROR
            )
            
    create_review_from_request.short_description = "Create review from selected request"
