{% extends "base.html" %}
{% load static %}
{% load custom_filters %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'home/css/home.css' %}">
{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            {% if search_term %}
                <h2 class="text-center mb-4">Search results for "{{ search_term }}"</h2>
                {% if works or blog_posts %}
                     <p class="text-center">Found {{ total_results }} result{{ total_results|pluralize }} <br>
                        ({{ works|length }} in works, {{ blog_posts|length }} in blog posts)</p>
                {% else %}
                    <p class="text-center">No results found. Try a different search term.</p>
                {% endif %}
            {% else %}
                <h2 class="text-center mb-4">All Works</h2>
            {% endif %}
        </div>
    </div>

    {% if works %}
    <h3 class="mt-4">Works</h3>
        <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-4 g-4">
            {% for work in works %}
                <div class="col">
                    <div class="image-container card work-search-card-img h-100">
                        {% if work.image %}
                            <img src="{{ work.image.url }}" class="card-img-top img-fluid" alt="{{ work.name }}">
                        {% else %}
                            <img src="{% static 'images/noimage.png' %}" class="card-img-top img-fluid" alt="{{ work.name }}">
                        {% endif %}
                        
                        <div class="card-body">
                            <h5 class="card-title">{{ work.name|highlight_yellow:search_term|striptags|safe }}</h5>
                            <p class="card-text">{{ work.description|smart_truncate:search_term|truncatewords:20|highlight_yellow:search_term|striptags|safe }}</p>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="h5 mb-0">${{ work.price }}</span>
                                <span class="badge bg-primary">{{ work.category.friendly_name }}</span>
                            </div>
                        </div>
                        
                        <div class="card-footer bg-white border-top-0">
                            <a href="{% url 'work_detail' work.id %}" 
                               class="btn btn-outline-primary w-100"
                               aria-label="View details of {{ work.name }}">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}
    {% if blog_posts %}
        <h3 class="mt-4">Blog Posts</h3>
        <div class="row">
            {% for post in blog_posts %}
                <div class="col-12 col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 rounded">
                        <div class="image-wrapper bg-black rounded">
                        {% if post.post_featured_image %}
                        <div class="bg-black">
                            <img src="{{ post.post_featured_image.url }}" class="card-img-top img-fluid" alt="{{ post.post_title }}">
                        </div>
                        {% else %}
                            <img src="{% static 'images/noimage.png' %}" class="card-img-top img-fluid" alt="{{ pospt.post_title }}">
                        {% endif %}
                        </div>
                        <div class="card-body">
                            <h5 class="card-title">{{ post.post_title|highlight_yellow:search_term|striptags|safe }}</h5>
                            <p class="card-text">{{ post.post_content|smart_truncate:search_term|striptags|safe|truncatewords:30|highlight_yellow:search_term }}</p>
                            <a href="{% url 'post_detail' post.post_slug %}" 
                               class="btn btn-primary mt-auto"
                               aria-label="Read more about {{ post.post_title }}">
                                Read More
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}    
</div>
{% endblock %}
