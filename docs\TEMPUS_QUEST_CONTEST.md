# TempusQuest Illustration Contest Application

## Overview

The TempusQuest Illustration Contest is a feature-rich application integrated with the Tempus Author Platform. It allows artists to participate in illustration contests by submitting artwork inspired by the Tempus novel, with various features for contest management, user submissions, voting, and book access.

## Key Features

### Contest Management
- **Contest Creation**: Admin can create contests with start/end dates, submission periods, and results announcement dates
- **Contest Toggling**: Admin can enable/disable submissions regardless of dates
- **Categories**: Contests can have multiple categories for submissions
- **Jury Scoring**: Staff members can score submissions to determine winners

### User Submissions
- **Artwork Upload**: Users can upload illustrations with titles, descriptions, and category selection
- **Book References**: Users can specify which part/chapter of the book inspired their artwork
- **Book Reviews**: Submissions include book reviews that are also posted to the book's page
- **Submission Management**: Users can edit or delete their submissions

### Gallery & Viewing
- **Gallery View**: Public gallery of approved submissions with filtering options
- **TEMPUS View**: Enhanced slideshow view with zoom, fullscreen, and navigation features
- **Submission Details**: Detailed view of individual submissions with comments and likes
- **Navigation**: Previous/next navigation between submissions

### Voting System
- **Like System**: Users can like submissions (limited to 1000 points)
- **Eligibility**: Users who give at least 3 likes become eligible for grand prizes
- **Leaderboard**: Display of top submissions by likes

### Book Access System
- **Access Requests**: Contest participants can request free or paid access to book parts
- **Free Access Workflow**:
  1. User submits free access request with reason and category
  2. Admin approves request and grants access
  3. System creates a $0 order record
  4. User receives email notification with access details
  5. Book appears in user's library
- **Paid Access Workflow**:
  1. User selects paid access option
  2. User is redirected to cart/checkout
  3. After payment, book is available in user's library

### Social Features
- **Comments**: Users can comment on submissions
- **Social Sharing**: Share buttons for submissions with #tempusquest hashtag
- **Contest Sharing**: Copy/share links for the contest itself

### Contest Guide
- **Book Fragments**: Featured book fragments as inspiration for artists
- **Visual Moments**: Descriptions pointing to visually rich moments in the novel

## Technical Implementation

### Models
- **Contest**: Main contest information, dates, rules, etc.
- **Category**: Contest categories
- **Submission**: User artwork submissions with metadata
- **Like**: User likes on submissions
- **JuryScore**: Staff scoring of submissions
- **Comment**: User comments on submissions
- **BookAccessRequest**: Requests for book access with approval workflow

### Integration Points
- **Works App**: Integration with existing products/books
- **Checkout App**: Integration for paid book access
- **Profiles App**: Integration for user library access
- **Review System**: Integration for book reviews

### Key Workflows

#### Book Access Request Workflow
1. User submits request (free or paid)
2. For free access:
   - Admin approves request and grants access
   - System sends email notification
   - System creates order record for library access
3. For paid access:
   - User is redirected to cart
   - Standard checkout process applies

#### Submission Workflow
1. User creates submission with artwork and book review
2. Admin approves submission
3. Submission appears in gallery
4. Other users can like and comment
5. Jury can score submissions
6. Winners determined by jury scores and/or likes

## Admin Features
- **Submission Approval**: Review and approve user submissions
- **Book Access Management**: Approve/reject book access requests
- **Jury Dashboard**: Score submissions and view statistics
- **Contest Configuration**: Set dates, rules, and toggle submission acceptance

## User Experience Enhancements
- **Responsive Design**: Different layouts for desktop and mobile
- **Image Protection**: Prevent right-click downloads of artwork
- **Zoom Functionality**: Detailed viewing of artwork with zoom and pan
- **Professional UI**: Clean, consistent interface with proper alignment and spacing

## Future Enhancements
- Enhanced analytics for contest participation
- Additional social media integration
- More interactive gallery features
- Advanced filtering and search options

## Technical Notes
- The contest app is designed to be modular and integrates with the existing Tempus Author Platform
- Free access requests create $0 order records to leverage the existing library system
- Signal handlers manage email notifications for access approvals
- Custom admin actions streamline the approval process

## Conclusion
The TempusQuest Illustration Contest application provides a comprehensive platform for running art contests related to the Tempus novel, with features for submissions, voting, book access, and community engagement. It integrates seamlessly with the existing Tempus Author Platform while providing specialized features for contest management.
