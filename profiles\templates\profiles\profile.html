{% extends "base.html" %}
{% load static %}
{% load crispy_forms_tags %}
{% load profile_tags %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'profiles/css/profile.css' %}">
{% endblock %}

{% block content %}
<div id="profile-top"></div>
<div class="overlay"></div>
<div class="container">
    <div class="row">
        <div class="col">
            <hr>
            <h2 class="logo-font mb-4 text-center">My Profile</h2>
            <hr>
        </div>
    </div>    
    <div class="row">
        <div class="col-12 col-lg-4">
            <div class="text-center mb-4">
                <h4 class="mb-3">Profile Picture</h4>
                    <div class="profile-image-container">
                        <label for="id_profile_image">
                            {% if user.profile.profile_image %}
                                <img src="{{ user.profile.profile_image.url }}" alt="Profile Image" class="rounded-circle profile-image">
                            {% else %}
                                <img src="{{ MEDIA_URL }}profile_images/default.jpg" alt="Default Profile Image" class="rounded-circle profile-image">
                            {% endif %}
                        </label>
                    </div>
                <p class="mt-2">User: {{ user.username }}</p>
                <small>Click image to change your profile picture</small>
            </div>
            
            <div class="text-center mb-4">
                <a href="{% url 'my_library' %}" class="btn btn-primary">
                    <i class="fas fa-book-reader me-2"></i>My Library
                </a>
                <p class="small mt-2">Access your e-books, audiobooks, and other digital content</p>
            </div>
        </div>
        <div class="col-12 col-lg-4">
            <h4 class="mb-3">Profile Information</h4>
            <div class="profile-form-container">
                <form action="{% url 'profile' %}" method="POST" id="profile-update-form" enctype="multipart/form-data">
                    {% csrf_token %}
                    {{ form|crispy|replace_currently_text }}
                    <button class="btn btn-primary rounded-0 mt-3">
                        Update Profile
                    </button>
                </form>
            </div>
        </div>     
        <div class="col-12 col-lg-4">
            <p>Order History</p>
            <div class="order-history table-responsive">
                <div class="order-history table-responsive">
                    {% if orders %}
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Order </th>
                                <th>Order Date</th>
                                <th>Items</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in orders %}
                            <tr>
                                <td>
                                    <a href="{% url 'order_history' order.id %}" 
                                       aria-label="View details of order {{ order.id }}"
                                       title="{{ order.id }}">
                                        {{ order.id }}
                                    </a>
                                </td>
                                <td>{{ order.order_date }}</td>
                                <td>
                                    <ul class="list-unstyled">
                                        {% for item in order.orderitem_set.all %}
                                        <li class="small">{{ item.product.name }}</li>
                                        {% endfor %}
                                    </ul>
                                </td>
                                <td>{{ order.total_amount }}</td>
                            </tr>
                            {% empty %}
                                <tr>
                                    <td colspan="4">No order history found.</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% else %}
                    <p>No order history found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
        <!-- Recent Comments Section -->
        <div class="row mt-4">
            <div class="col-12">
                <h4>Recent Comments</h4>
                {% if latest_comments %}
                    <div class="comments-section">
                        {% for comment in latest_comments %}
                            <div class="comment-card mb-3 p-3 border rounded">
                                <div class="comment-content">
                                    <p class="mb-1">{{ comment.content|truncatewords:50 }}</p>
                                    <div class="comment-meta small">
                                        on post: <a href="{% url 'post_detail' comment.post.post_slug %}"
                                                  aria-label="View post: {{ comment.post.post_title }}">
                                            {{ comment.post.post_title }}</a>
                                        <br>
                                        {{ comment.created_on|date:"F j, Y" }}
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p>No comments yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
