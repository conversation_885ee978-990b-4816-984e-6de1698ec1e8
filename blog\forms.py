from django import forms
from .models import Comment, Post, PostImage
from utils.image_validation import validate_image
from django.conf import settings
import logging

logger = logging.getLogger(__name__)


class CommentForm(forms.ModelForm):
    """
    Form for creating and editing blog post comments.
    """

    class Meta:
        """
        Meta class for CommentForm.
        """
        model = Comment
        fields = ('content',)
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Write your comment here...'
            })
        }


class PostForm(forms.ModelForm):
    """
    Form for creating and editing blog posts.
    Includes image validation.
    """
    post_featured_image = forms.ImageField(
        validators=[validate_image],
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/jpeg,image/png,image/webp'
        })
    )

    class Meta:
        model = Post
        fields = ['post_title', 'post_content', 'post_featured_image', 'post_status', 'post_excerpt', 'category', 'tags']
        widgets = {
            'post_content': forms.Textarea(attrs={'class': 'form-control'}),
            'post_excerpt': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3
            })
        }


class PostImageForm(forms.ModelForm):
    """
    Form for blog post content images.
    Includes image validation and standardization.
    """
    image = forms.ImageField(
        validators=[validate_image],
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/jpeg,image/png,image/webp'
        })
    )
    
    def clean_alt_text(self):
        """
        Validate alt text for accessibility compliance.
        """
        alt_text = self.cleaned_data.get('alt_text', '').strip()

        if not alt_text:
            raise forms.ValidationError(
                'Alt text is required for accessibility. '
                'Describe what the image shows for screen reader users.'
            )

        if len(alt_text) < 3:
            raise forms.ValidationError(
                'Alt text should be at least 3 characters long and descriptive.'
            )

        # Check for non-descriptive alt text
        non_descriptive = ['image', 'photo', 'picture', 'img', 'pic']
        if alt_text.lower().strip() in non_descriptive:
            raise forms.ValidationError(
                'Please provide a more descriptive alt text. '
                'Describe what the image shows, not just that it is an image.'
            )

        return alt_text

    class Meta:
        model = PostImage
        fields = ['image', 'alt_text', 'caption', 'width', 'alignment']
        widgets = {
            'alt_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Required: Describe the image for screen readers',
                'aria-describedby': 'alt_text_help',
                'required': True
            }),
            'caption': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Optional: Caption to display below the image'
            }),
            'width': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '200',
                'max': '1200'
            })
        }

    def clean_image(self):
        """
        Validate and standardize uploaded images.
        """
        image = self.cleaned_data.get('image')
        if image:
            # Check file size
            if image.size > settings.MAX_UPLOAD_SIZE:
                raise forms.ValidationError(
                    f'Image too large. Max size is {settings.MAX_UPLOAD_SIZE/1024/1024}MB.'
                )
                
            # Log information about the image
            logger.info(f'Processing image upload: {image.name}, {image.size} bytes')
                
        return image
