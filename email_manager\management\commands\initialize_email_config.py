"""
Management command to initialize the email configuration.
Run this after migrations to ensure a default configuration exists.
"""
from django.core.management.base import BaseCommand
from email_manager.models import EmailConfiguration

class Command(BaseCommand):
    help = 'Initialize the email configuration with default values'

    def handle(self, *args, **options):
        # Get or create the configuration
        config, created = EmailConfiguration.objects.get_or_create(pk=1)
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created default email configuration'))
        else:
            self.stdout.write(self.style.SUCCESS('Email configuration already exists'))
