{% extends "account/base_manage_email.html" %}
{% load i18n %}
{% load allauth %}
{% block head_title %}
    {% trans "Email Address" %}
{% endblock head_title %}
{% block inner_content %}
    {% element h1 %}
        {% trans "Email Address" %}
    {% endelement %}
    {% if not emailaddresses %}
        {% include "account/snippets/warn_no_email.html" %}
    {% endif %}
    {% url 'account_email' as action_url %}
    {% element form method="post" action=action_url %}
        {% slot body %}
            {% csrf_token %}
            {% if current_emailaddress %}
                {% element field id="current_email" disabled=True type="email" value=current_emailaddress.email %}
                    {% slot label %}
                        {% translate "Current email" %}:
                    {% endslot %}
                {% endelement %}
            {% endif %}
            {% if new_emailaddress %}
                {% element field id="new_email" value=new_emailaddress.email disabled=True type="email" %}
                    {% slot label %}
                        {% if not current_emailaddress %}
                            {% translate "Current email" %}:
                        {% else %}
                            {% translate "Changing to" %}:
                        {% endif %}
                    {% endslot %}
                    {% slot help_text %}
                        {% blocktranslate %}Your email address is still pending verification.{% endblocktranslate %}
                        {% element button form="pending-email" type="submit" name="action_send" tags="minor,secondary" %}
                            {% trans 'Re-send Verification' %}
                        {% endelement %}
                        {% if current_emailaddress %}
                            {% element button form="pending-email" type="submit" name="action_remove" tags="danger,minor" %}
                                {% trans 'Cancel Change' %}
                            {% endelement %}
                        {% endif %}
                    {% endslot %}
                {% endelement %}
            {% endif %}
            {% element field id=form.email.auto_id name="email" value=form.email.value errors=form.email.errors type="email" %}
                {% slot label %}
                    {% translate "Change to" %}:
                {% endslot %}
            {% endelement %}
        {% endslot %}
        {% slot actions %}
            {% element button name="action_add" type="submit" %}
                {% trans "Change Email" %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
    {% if new_emailaddress %}
        <form style="display: none"
              id="pending-email"
              method="post"
              action="{% url 'account_email' %}">
            {% csrf_token %}
            <input type="hidden" name="email" value="{{ new_emailaddress.email }}">
        </form>
    {% endif %}
{% endblock inner_content %}
