{% load static %}

<!doctype html>
<html lang="en">
  <head>

    <!-- Website Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="Discover THE AGE OF NEW ERA - a visionary blend of speculative fiction and science exploring global progress.
    Join the TEMPUS platform for books, audiobooks, and a community shaping humanity's future.">
    <meta name="keywords" content="speculative fiction, world development, <PERSON>, visionary sci-fi community,
    science fiction social change, futuristic audiobook, global technological unity, <PERSON>, TEMPUS engine, societal evolution">
    <meta name="author" content="Valleyberg Publishing, Rafal Zygula, Author of THE AGE OF NEW ERA">
    <meta name="theme-color" content="#333333">

    {% block opengraph %}
    <!-- Default Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:title" content="Tempus Author Platform">
    <meta property="og:description" content="Discover THE AGE OF NEW ERA - a visionary blend of speculative fiction and science exploring global progress. Join the TEMPUS platform for books, audiobooks, and a community shaping humanity's future.">
    <meta property="og:image" content="{{ STATIC_URL_WITH_HOST }}images/og-default.jpg">
    <meta property="og:site_name" content="Tempus Author Platform">
    <meta property="og:logo" content="{{ STATIC_URL_WITH_HOST }}images/logo.png">

    <!-- Default Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="{{ request.build_absolute_uri }}">
    <meta name="twitter:title" content="Tempus Author Platform">
    <meta name="twitter:description" content="Discover THE AGE OF NEW ERA - a visionary blend of speculative fiction and science exploring global progress. Join the TEMPUS platform for books, audiobooks, and a community shaping humanity's future.">
    <meta name="twitter:image" content="{{ STATIC_URL_WITH_HOST }}images/og-default.jpg">
    {% endblock %}

    <!-- Google Fonts CSS -->
    <link rel="preconnect" href="https://fonts.gstatic.com">
    <link rel="preconnect" href="https://fonts.googleapis.com">

    <link href="https://fonts.googleapis.com/css2?family=Libre+Caslon+Text:ital,wght@0,400;0,700;1,400" rel="stylesheet">

    <!-- Font Awesome CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">

    <!-- Bootstrap core CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-QWTKZyjpPEjISv5WaRU9OFeRpok6YctnYmDr5pNlyT2bRjXh0JMhjY6hW+ALEwIH" crossorigin="anonymous">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <!-- Favicons -->
    <link rel="icon" type="image/png" href="{% static 'favicon/favicon-96x96.png' %}" sizes="96x96">
    <link rel="icon" type="image/svg+xml" href="{% static 'favicon/favicon.svg' %}">
    <link rel="shortcut icon" href="{% static 'favicon/favicon.ico' %}">
    <link rel="apple-touch-icon" sizes="180x180" href="{% static 'favicon/apple-touch-icon.png' %}">
    <meta name="apple-mobile-web-app-title" content="TEMPUS A.P.">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Web App Manifest -->
    <link rel="manifest" href="/manifest.json">

    {% block extra_css %}
    {% endblock %}

    <!-- Carousel CSS -->
    <link rel="stylesheet" href="{% static 'home/css/carousel.css' %}">

    <!-- jquery -->
    <script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>

    <!-- Bootstrap JavaScript Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-YvpcrYf0tY3lHB60NNkmXc5s9fDVZLESaAA55NDzOxhy9GkcIdslK1eN7N6jIeHz" crossorigin="anonymous"></script>

    <!-- Stripe JS -->
    <script src="https://js.stripe.com/v3/" data-cookiepartitioned="true"></script>
    <meta name="permissions-policy" content="payment=*">

    <!-- Main JavaScript -->
    <script src="{% static 'js/main.js' %}" defer></script>

    <!-- Service Worker Manager -->
    <script src="{% static 'js/sw_manager.js' %}" defer></script>

    {% block extra_js %}
    {% endblock %}

    {% block extrahead %}
    {% endblock %}

    <title>Tempus Author Platform</title>
  </head>

  <body class="d-flex flex-column min-vh-100">
    <!-- Skip Navigation Links -->
    <a class="skip-link visually-hidden-focusable" href="#main-content">Skip to main content</a>
    <a class="skip-link visually-hidden-focusable" href="#navbarNav">Skip to navigation</a>

    <!-- Navigation -->
    <header class="container-fluid fixed-top" role="banner">
        <nav class="navbar navbar-expand-lg navbar-light bg-light" role="navigation" aria-label="Main navigation">
            <div class="container">
                <a class="navbar-brand" href="{% url 'home' %}" aria-label="Go to Tempus Author Platform home page">Tempus A.P.</a>

                <div class="d-flex align-items-center">
                    <!-- Bookcart Icon - visible only on mobile -->
                    <div class="d-lg-none me-2">
                        <a href="{% url 'view_bookcart' %}" class="nav-link position-relative" aria-label="View your library items">
                            <i class="fas fa-book-reader fs-5" aria-hidden="true"></i>
                            {% if product_count %}
                                <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary" style="transform: translate(-50%, -25%);">
                                    {{ product_count }}
                                    <span class="visually-hidden">items in library</span>
                                </span>
                            {% endif %}
                        </a>
                    </div>

                    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                        <span class="navbar-toggler-icon"></span>
                    </button>
                </div>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/' %}active{% endif %}" aria-current="page" href="{% url 'home' %}" aria-label="Home page">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/works/' %}active{% endif %}" aria-current="page" href="{% url 'works' %}" aria-label="Browse works">Works</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/blog/' %}active{% endif %}" aria-current="page" href="{% url 'blog' %}" aria-label="Read our blog">Blog</a>
                        </li>
                        <!-- TEMPUS QUEST Framework -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if '/contest/' in request.path %}active{% endif %}" href="#" id="contestDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" aria-label="Tempus Quest contest menu">
                                Tempus Quest
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="contestDropdown">
                                <li><a class="dropdown-item" href="{% url 'contest:contest_list' %}">Contest Home</a></li>
                                <li><a class="dropdown-item" href="{% url 'contest:submission_gallery' slug='tempus-quest-illustration-contest' %}">Gallery</a></li>
                                <li><a class="dropdown-item" href="{% url 'contest:leaderboard' slug='tempus-quest-illustration-contest' %}">Leaderboard</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'contest:submission_create' slug='tempus-quest-illustration-contest' %}">Submit Entry</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if '/newsletter/subscribe/' in request.path %}active{% endif %}" aria-current="page" href="{% url 'newsletter:subscribe' %}" aria-label="Subscribe to our newsletter">Newsletter</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.path == '/about/' %}active{% endif %}" aria-current="page" href="{% url 'about' %}" aria-label="About us">About</a>
                        </li>
                    </ul>
                <!-- Search Form -->
                <form class="d-flex mx-2" role="search" aria-label="Site search" method="GET" action="{% url 'search' %}">
                    <div class="input-group">
                        <input class="form-control" type="search" name="q" placeholder="Search..." aria-label="Search">
                        <button class="btn btn-outline-secondary" type="submit" aria-label="Submit search">
                            <i class="fas fa-search" aria-hidden="true"></i>
                        </button>
                    </div>
                </form>

                <!-- User Dropdown Menu -->

                    <ul class="navbar-nav align-items-center">
                        {% if user.is_authenticated %}
                            <li class="nav-item dropdown d-flex align-items-center">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false" aria-label="Your account menu">
                                    {% if user.profile.profile_image %}
                                        <img src="{{ user.profile.profile_image.url }}" alt="Profile Image" class="rounded-circle nav-profile-image">
                                    {% else %}
                                        <img src="{{ MEDIA_URL }}profile_images/default.jpg" alt="Default Profile Image" class="rounded-circle nav-profile-image">
                                    {% endif %}
                                    {{ user.username }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                    <li><a class="dropdown-item" href="{% url 'profile' %}#profile-top" aria-label="Go to account settings"><i class="fas fa-user-cog" aria-hidden="true"></i> Account Settings</a></li>
                                    <li><a class="dropdown-item" href="{% url 'my_library' %}" aria-label="Go to your digital content library"><i class="fas fa-book-reader" aria-hidden="true"></i> My Library</a></li>
                                    <li><hr class="dropdown-divider" aria-hidden="true"></li>
                                    <li><a class="dropdown-item" href="{% url 'account_logout' %}" aria-label="Log out of account" id="logout-link"><i class="fas fa-sign-out-alt" aria-hidden="true"></i> Logout</a></li>
                                </ul>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'account_signup' %}" aria-label="Create new account">Register</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'account_login' %}" aria-label="Sign in to your account">Login</a>
                            </li>
                        {% endif %}
                        <li class="nav-item d-none d-lg-block">
                            <a class="nav-link position-relative" href="{% url 'view_bookcart' %}" aria-label="View your library items">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-book-reader" aria-hidden="true"></i>
                                    <!-- Bookcart Badge -->
                                    {% if product_count %}
                                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-primary">
                                            {{ product_count }}
                                            <span class="visually-hidden">items in library</span>
                                        </span>
                                    {% endif %}
                                </div>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay"></div>

    <!-- Messages container -->
    {% if messages %}
    <div class="container text-center col-md-6" role="region" aria-label="Status messages" aria-live="polite">
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message|safe }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Content blocks -->
    {% block page_header %}
    {% endblock %}

    <main class="text-white" id="main-content" role="main">
    {% block content %}
    {% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-5 mt-auto" role="contentinfo">
        <div class="footer container">
            <div class="row">
                <!-- About Column -->
                <div class="col-md-4 mb-3">
                    <h5>Tempus Author Platform</h5>
                    <p class="text-muted">Your gateway to extraordinary literary works and unique author experiences.</p>
                </div>

                <!-- Quick Links Column -->
                <div class="col-md-4 mb-3">
                    <h5 id="quick-links-heading">Quick Links</h5>
                    <ul class="list-unstyled" aria-labelledby="quick-links-heading">
                        <li><a href="{% url 'home' %}" class="text-decoration-none text-muted"
                            aria-label="Go to home page">Home</a></li>
                        <li><a href="{% url 'works' %}" class="text-decoration-none text-muted"
                            aria-label="View all works">Works</a></li>
                        <li><a href="{% url 'blog' %}" class="text-decoration-none text-muted"
                            aria-label="Read our blog">Blog</a></li>
                        <li><a href="{% url 'contest:contest_list' %}" class="text-decoration-none text-muted"
                            aria-label="Participate in Tempus Quest contest">Tempus Quest</a></li>
                        <li><a href="{% url 'newsletter:subscribe' %}" class="text-decoration-none text-muted"
                            aria-label="Subscribe to our newsletter">Newsletter</a></li>
                        <li><a href="{% url 'about' %}" class="text-decoration-none text-muted"
                            aria-label="Learn about us">About</a></li>
                    </ul>
                </div>

                <!-- Contact & Social Column -->
                <div class="col-md-4 mb-3">
                    <h5>Connect With Us</h5>
                    <div class="social-links mb-3">
                        <a href="https://www.facebook.com/theageofnewera/" class="text-decoration-none text-muted me-2" target="_blank"
                            rel="noopener noreferrer" aria-label="Visit our Facebook page">
                            <i class="fab fa-facebook-f" aria-hidden="true"></i>
                        </a>
                        <a href="https://www.instagram.com/theageofnewera/" class="text-decoration-none text-muted me-2" target="_blank"
                            rel="noopener noreferrer" aria-label="Visit our Instagram page">
                            <i class="fab fa-instagram" aria-hidden="true"></i>
                        </a>
                        <a href="https://linktr.ee/TheAgeofNewEra" class="text-decoration-none text-muted me-2" target="_blank"
                            rel="noopener noreferrer" aria-label="Visit our LinkTree page">
                            <i class="fab fa-pagelines" aria-hidden="true"></i>
                        </a>
                    </div>
                    <p class="text-muted">
                        <i class="fas fa-envelope me-2" aria-hidden="true"></i>
                        <span><EMAIL></span><br>
                    </p>
                </div>
            </div>

            <!-- Copyright -->
            <div class="row mt-3">
                <div class="col-12">
                    <hr>
                    <p class="text-center text-muted mb-0">
                        &copy; 2025 Tempus Author Platform. All rights reserved.
                    <br>
                    <small>
                        build by valleyberg &copy;
                    </small></p>
                </div>
            </div>
        </div>
        <!-- Back to Top Button -->
        <button type="button" class="btn text-white rounded-circle position-fixed bottom-0 end-0 translate-middle d-none shadow-sm" id="back-to-top" aria-label="Back to top">
            <i class="fas fa-arrow-up"></i>
        </button>
        <script src="{% static 'js/backtotop.js' %}"></script>
        <script src="{% static 'js/navbar.js' %}"></script>
        <script src="{% static 'js/navbar-dim.js' %}"></script>

        <!-- Authentication Helper -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Special handling for logout link
                const logoutLink = document.getElementById('logout-link');
                if (logoutLink) {
                    logoutLink.addEventListener('click', function(event) {
                        // Prevent the default link behavior
                        event.preventDefault();

                        // Set a flag to indicate logout
                        sessionStorage.setItem('logout_requested', 'true');

                        // Unregister service workers before logout
                        if ('serviceWorker' in navigator) {
                            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                                // If there are service workers, unregister them
                                if (registrations.length > 0) {
                                    console.log('Unregistering service workers before logout');

                                    // Unregister all service workers
                                    const unregisterPromises = registrations.map(registration => registration.unregister());

                                    // After unregistering, proceed with logout
                                    Promise.all(unregisterPromises).then(function() {
                                        window.location.href = logoutLink.href;
                                    }).catch(function() {
                                        window.location.href = logoutLink.href;
                                    });
                                } else {
                                    // No service workers to unregister, proceed with logout
                                    window.location.href = logoutLink.href;
                                }
                            }).catch(function() {
                                // If error checking registrations, still proceed with logout
                                window.location.href = logoutLink.href;
                            });
                        } else {
                            // If service workers aren't supported, just proceed with logout
                            window.location.href = logoutLink.href;
                        }
                    });
                }

                // Check if we're returning from logout
                if (sessionStorage.getItem('logout_requested') === 'true' &&
                    !window.location.pathname.includes('/accounts/logout/')) {
                    // Clear the flag
                    sessionStorage.removeItem('logout_requested');

                    // Force reload to ensure fresh state
                    window.location.reload(true);
                }

                // Force reload on login pages to ensure fresh state
                if (window.location.pathname.includes('/accounts/login/')) {
                    // Add event listeners to login forms
                    document.querySelectorAll('form').forEach(function(form) {
                        form.addEventListener('submit', function() {
                            // Set a flag to indicate login
                            sessionStorage.setItem('login_requested', 'true');
                        });
                    });
                }

                // Check if we're returning from login
                if (sessionStorage.getItem('login_requested') === 'true' &&
                    !window.location.pathname.includes('/accounts/login/')) {
                    // Clear the flag
                    sessionStorage.removeItem('login_requested');

                    // Force reload to ensure fresh state
                    window.location.reload(true);
                }
            });
        </script>
    </footer>

    {% if user.is_authenticated %}
    <script>
        // Any authenticated user-specific JavaScript can go here
    </script>
    {% endif %}

    <!-- Audio Analytics Script - Only load on audio player page -->
    {% if '/works/audio-player/' in request.path %}
    <script src="{% static 'works/js/audio_analytics.js' %}"></script>
    {% endif %}

    {% block postloadjs %}
    {% endblock %}
  </body>
</html>