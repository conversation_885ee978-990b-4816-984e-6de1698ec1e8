{% extends "contest/base.html" %}
{% load static %}

{% block contest_title %}{{ contest.name }}{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
    // Function to copy contest link to clipboard (desktop version)
    function copyContestLink() {
        const shareUrl = document.getElementById('contest-share-url');
        shareUrl.select();
        shareUrl.setSelectionRange(0, 99999); // For mobile devices

        navigator.clipboard.writeText(shareUrl.value)
            .then(() => {
                // Change button text/icon temporarily to show success
                const copyBtn = document.getElementById('contest-copy-link-btn');
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.classList.remove('btn-outline-secondary');
                copyBtn.classList.add('btn-success');

                // Show alert
                alert('Contest link copied to clipboard! Share it to spread the word about TempusQuest!');

                // Reset button after 2 seconds
                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.classList.remove('btn-success');
                    copyBtn.classList.add('btn-outline-secondary');
                }, 2000);
            })
            .catch(err => {
                console.error('Could not copy text: ', err);
                alert('Could not copy link. Please try again or copy it manually.');
            });
    }

    // Function to copy contest link to clipboard (mobile version)
    function copyContestLinkMobile() {
        const shareUrl = document.getElementById('contest-share-url-mobile').value;

        navigator.clipboard.writeText(shareUrl)
            .then(() => {
                // Change button text/icon temporarily to show success
                const copyBtn = document.getElementById('contest-copy-link-btn-mobile');
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.classList.remove('btn-outline-secondary');
                copyBtn.classList.add('btn-success');

                // Show alert
                alert('Contest link copied to clipboard!');

                // Reset button after 2 seconds
                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.classList.remove('btn-success');
                    copyBtn.classList.add('btn-outline-secondary');
                }, 2000);
            })
            .catch(err => {
                console.error('Could not copy text: ', err);
                alert('Could not copy link. Please try again or copy it manually.');
            });
    }

    // Function for mobile share using Web Share API
    function mobileShareContest() {
        // Check if Web Share API is supported
        if (navigator.share) {
            navigator.share({
                title: '{{ contest.name }} - TempusQuest Contest',
                text: 'Check out the TempusQuest illustration contest! #TempusQuest',
                url: document.getElementById('contest-share-url-mobile').value
            })
            .then(() => console.log('Successfully shared'))
            .catch(error => console.log('Error sharing:', error));
        } else {
            // Fallback for browsers that don't support the Web Share API
            alert('Web Share API not supported on this browser. Please use the social sharing buttons below.');
        }
    }
</script>
{% endblock %}

{% block contest_content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">{{ contest.name }}</h2>
                <p class="card-text">{{ contest.description }}</p>
                <div class="alert alert-info mt-2">
                    <strong><i class="fas fa-birthday-cake me-2"></i> Special Occasion:</strong> This contest celebrates the 21st birthday of the TEMPUS story idea! Read more in our <a href="{% url 'blog' %}?category=welcome" target="_blank">TEMPUS GRADUS blog</a> and check out <a href="{% url 'post_detail' 'the-genesis-of-a-vision-20-years-of-crafting-the-age-of-new-era' %}" target="_blank">The Genesis of a Vision: 20 Years of Crafting The Age of New Era</a>.
                </div>

                <!-- Share Contest Section -->
                <div class="share-contest mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <h5 class="mb-0 me-2"><i class="fas fa-share-alt text-purple" style="color: #6f42c1;"></i> Share This Contest</h5>
                        <span class="small text-muted">Help spread the word about #TempusQuest!</span>
                    </div>

                    <!-- Desktop Layout (visible on screens >= 992px) -->
                    <div class="d-none d-lg-flex align-items-center">
                        <div class="input-group me-2" style="max-width: 450px;">
                            <input type="text" id="contest-share-url" class="form-control" value="{{ request.build_absolute_uri }}" readonly>
                            <button class="btn btn-outline-secondary" type="button" id="contest-copy-link-btn" onclick="copyContestLink()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>

                        <!-- Social Share Buttons -->
                        <div class="d-flex">
                            <!-- Facebook -->
                            <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri|urlencode }}"
                               target="_blank" class="btn btn-outline-primary mx-1" title="Share on Facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>

                            <!-- Twitter -->
                            <a href="https://twitter.com/intent/tweet?text=Check out the TempusQuest illustration contest! %23TempusQuest&url={{ request.build_absolute_uri|urlencode }}"
                               target="_blank" class="btn btn-outline-info mx-1" title="Share on Twitter">
                                <i class="fab fa-twitter"></i>
                            </a>

                            <!-- WhatsApp -->
                            <a href="https://api.whatsapp.com/send?text=Check out the TempusQuest illustration contest! %23TempusQuest {{ request.build_absolute_uri|urlencode }}"
                               target="_blank" class="btn btn-outline-success mx-1" title="Share on WhatsApp">
                                <i class="fab fa-whatsapp"></i>
                            </a>

                            <!-- Email -->
                            <a href="mailto:?subject=TempusQuest Illustration Contest&body=Check out this amazing illustration contest: {{ request.build_absolute_uri|urlencode }}"
                               class="btn btn-outline-secondary mx-1" title="Share via Email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>

                    <!-- Mobile Layout (visible on screens < 992px) -->
                    <div class="d-lg-none">
                        <div class="d-flex">
                            <!-- Mobile Share Button (uses Web Share API) -->
                            <button id="contest-mobile-share-btn" class="btn btn-purple flex-grow-1 me-2" style="background-color: #6f42c1; color: white;" onclick="mobileShareContest()">
                                <i class="fas fa-share-alt me-2"></i> Share Contest
                            </button>

                            <!-- Copy Link Button (simplified for mobile) -->
                            <button class="btn btn-outline-secondary" type="button" id="contest-copy-link-btn-mobile" onclick="copyContestLinkMobile()">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>

                        <!-- Hidden input for mobile copy functionality -->
                        <input type="hidden" id="contest-share-url-mobile" value="{{ request.build_absolute_uri }}">
                    </div>
                </div>

                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <div>
                        <span class="badge bg-primary">Contest Start: {{ contest.start_date|date:"F j, Y" }}</span>
                        {% if contest.submissions_open_date %}
                            <span class="badge bg-success">Submissions Open: {{ contest.submissions_open_date|date:"F j, Y" }}</span>
                        {% endif %}
                        <span class="badge bg-danger">Submissions End: {{ contest.end_date|date:"F j, Y" }}</span>
                        {% if contest.results_date %}
                            <span class="badge bg-warning text-dark">Results Announced: {{ contest.results_date|date:"F j, Y" }}</span>
                        {% endif %}

                        {% with submission_status=contest.are_submissions_allowed %}
                            {% if submission_status.0 %}
                                <span class="badge bg-success mt-2 d-block">{{ submission_status.1 }}</span>
                            {% else %}
                                <span class="badge bg-warning text-dark mt-2 d-block">{{ submission_status.1 }}</span>
                            {% endif %}
                        {% endwith %}
                    </div>
                    <div class="mt-2 mt-md-2 ">
                        <a href="{% url 'contest:contest_guide' slug=contest.slug %}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-lightbulb"></i> Inspiration Guide
                        </a>
                        <a href="{% url 'contest:contest_terms' slug=contest.slug %}" class="btn btn-outline-secondary">
                            <i class="fas fa-gavel"></i> Terms & Conditions
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">How to Enter</h3>
            </div>
            <div class="card-body">
                <ol>
                    <li>Register for a free account at tempus.valleyberg.com</li>
                    <li>Request access to exactly one part of The Age of New Era (Part I, II, or III) in e-book or audiobook format.
                        <div class="mt-2">
                            <a href="{% url 'contest:book_access_request' slug=contest.slug %}" class="btn btn-sm btn-outline-success">
                                <i class="fas fa-book me-1"></i> Request Book Access
                            </a>
                        </div>
                    </li>
                    <li>Choose what you like most - the 3 parts are very different, so be sure to read the genre description. <a href="{% url 'works' %}" target="_blank">Browse our books</a> to learn more about each part before deciding.</li>
                    <li>Read your selected part (you can use fragments from our <a href="{% url 'contest:contest_guide' slug=contest.slug %}">Inspiration Guide</a> or find your favourite moment).</li>
                    <li>Create your artwork inspired by the book fragment you chose.</li>
                    <li>Include a small signature or watermark in one corner of the image.</li>
                    <li>Submit your artwork via the Contest Submit Entry Form, tagging the correct category.</li>
                    <li>Submit a book review of the book you requested via the Contest Submit Entry Form:</li>
                        <ul>
                            <li>At least 48 words</li>
                            <li>Include the fragment you're illustrating in quotation marks or short description</li>
                            <li>Select the chapter number and title where you found your fragment</li>
                            <li>Explain why you chose this fragment and how it inspired your artwork</li>
                        </ul>
                    </li>
                    <li>Engage: “Like” at least 3 other entries to qualify for the grand prize.</li>
                    <li>Follow us on Facebook or Instagram for news and live updates (optional but recommended).</li>
                    <li>Optional Buy-In: You may get the book for $1 to support the project and unlock your chosen part immediately—but if you prefer, you can request free access through our <a href="{% url 'contest:book_access_request' slug=contest.slug %}">Book Access Request Form</a>.</li>
                    <li>Good Luck!</li>
                </ol>
                <div class="text-center mt-3">
                    {% with submission_status=contest.are_submissions_allowed %}
                        {% if submission_status.0 %}
                            <a href="{% url 'contest:submission_create' slug=contest.slug %}" class="btn btn-success">Submit Your Entry</a>
                        {% else %}
                            <button class="btn btn-secondary" disabled>{{ submission_status.1 }}</button>
                            <p class="text-muted mt-2 small">You can still prepare your artwork and request book access while waiting for submissions to open.</p>
                        {% endif %}
                    {% endwith %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h3 class="card-title mb-0">Categories</h3>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for stat in category_stats %}
                        <a href="{% url 'contest:submission_gallery' slug=contest.slug %}?category={{ stat.category.name }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            {{ stat.category.name }}
                            <span class="badge bg-primary rounded-pill">{{ stat.submission_count }}</span>
                        </a>
                    {% empty %}
                        <div class="list-group-item">No categories available</div>
                    {% endfor %}
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'contest:submission_gallery' slug=contest.slug %}" class="btn btn-info">
                        <i class="fas fa-images"></i> View All Submissions
                    </a>
                    <a href="{% url 'contest:leaderboard' slug=contest.slug %}" class="btn btn-warning">
                        <i class="fas fa-trophy"></i> View Leaderboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

{% if user.is_authenticated %}
    <div class="row">
        <div class="col-12">
            <div class="card  mb-4">
                <div class="card-header bg-secondary text-white">
                    <h3 class="card-title mb-0">Your Submissions</h3>
                </div>
                <div class="card-body">
                    {% if user_submissions %}
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>Your Submissions ({{ user_submissions|length }})</h4>
                            {% with submission_status=contest.are_submissions_allowed %}
                                {% if submission_status.0 %}
                                    <a href="{% url 'contest:submission_create' slug=contest.slug %}" class="btn btn-success">
                                        <i class="fas fa-plus"></i> Submit Another Entry
                                    </a>
                                {% else %}
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-clock"></i> {{ submission_status.1 }}
                                    </button>
                                {% endif %}
                            {% endwith %}
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Category</th>
                                        <th>Book Part</th>
                                        <th>Submitted</th>
                                        <th>Status</th>
                                        <th>Likes</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for submission in user_submissions %}
                                        <tr>
                                            <td>{{ submission.title }}</td>
                                            <td>{{ submission.category.name }}</td>
                                            <td>{{ submission.book_part.name }}</td>
                                            <td>{{ submission.submitted_at|date:"M d, Y" }}</td>
                                            <td>
                                                {% if submission.is_approved %}
                                                    <span class="badge bg-success">Approved</span>
                                                {% else %}
                                                    <span class="badge bg-warning text-dark">Pending</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ submission.total_likes }}</td>
                                            <td>
                                                <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-sm btn-primary">View</a>
                                                <a href="{% url 'contest:submission_edit' slug=submission.slug %}" class="btn btn-sm btn-secondary">Edit</a>
                                                <a href="{% url 'contest:submission_delete' slug=submission.slug %}" class="btn btn-sm btn-danger">Delete</a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <p>You haven't submitted any entries yet.</p>
                            <p>You can submit multiple entries in different categories to increase your chances of winning!</p>
                            {% with submission_status=contest.are_submissions_allowed %}
                                {% if submission_status.0 %}
                                    <a href="{% url 'contest:submission_create' slug=contest.slug %}" class="btn btn-success">
                                        <i class="fas fa-plus"></i> Submit Your First Entry
                                    </a>
                                {% else %}
                                    <button class="btn btn-secondary" disabled>
                                        <i class="fas fa-clock"></i> {{ submission_status.1 }}
                                    </button>
                                    <p class="text-muted mt-2">You can still prepare your artwork and request book access while waiting for submissions to open.</p>
                                {% endif %}
                            {% endwith %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}
