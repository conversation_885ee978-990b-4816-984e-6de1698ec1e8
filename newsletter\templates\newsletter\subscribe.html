{% extends "base.html" %}
{% load static %}

{% block title %}Subscribe to Our Newsletter{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body">
                    <h2 class="card-title text-center mb-4">Subscribe to Our Newsletter</h2>
                    
                    {% if messages %}
                    <div class="messages">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }}">
                            {{ message }}
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        {% for field in form %}
                        <div class="mb-3">
                            <label for="{{ field.id_for_label }}" class="form-label">
                                {{ field.label }}
                            </label>
                            {{ field }}
                            {% if field.errors %}
                            <div class="invalid-feedback d-block">
                                {{ field.errors.0 }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">Subscribe</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
