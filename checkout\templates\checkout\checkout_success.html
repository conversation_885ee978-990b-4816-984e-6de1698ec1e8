{% extends "base.html" %}
{% load static %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'checkout/css/stripe.css' %}">
{% endblock %}

{% block content %}
<div class="overlay"></div>
<div class="container bg-black bg-opacity-75 p-3 rounded mb-4">
    <div class="row">
        <div class="col">
            <hr>
            <h2 class="logo-font mb-4">Thank You</h2>
            <hr>
            {% if from_profile %}
                <p>Order Details:</p>
            {% else %}
                <p>Your order information is below. A confirmation email will be sent to {{ order.email }}.</p>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-lg-7">
            <div class="order-confirmation-wrapper p-2 border">
                <div class="row">
                    <div class="col">
                        <small class="">Order Info:</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-4">
                        <p class="mb-0 font-weight-bold">Order Number</p>
                    </div>
                    <div class="col-12 col-md-8 text-md-right">
                        <p class="mb-0">{{ order.id }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-4">
                        <p class="mb-0 font-weight-bold">Order Date</p>
                    </div>
                    <div class="col-12 col-md-8 text-md-right">
                        <p class="mb-0">{{ order.order_date }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col">
                        <small class="">Order Details:</small>
                    </div>
                </div>

                {% for item in order.orderitem_set.all %}
                <div class="row">
                    <div class="col-12 col-md-4">
                        <p class="small mb-0 font-weight-bold">
                            <a href="{% url 'work_detail' item.product.id %}" class="text-decoration-none" aria-label="View details of {{ item.product.name }}">
                                {{ item.product.name }}
                            </a>
                        </p>
                    </div>
                    <div class="col-12 col-md-8 text-md-right">
                        <p class="small mb-0">${{ item.product.price }}</p>
                    </div>
                </div>
                {% endfor %}

                <div class="row">
                    <div class="col">
                        <small class="">Billing Info:</small>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-4">
                        <p class="mb-0 font-weight-bold">Full Name</p>
                    </div>
                    <div class="col-12 col-md-8 text-md-right">
                        <p class="mb-0">{{ order.full_name }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-4">
                        <p class="mb-0 font-weight-bold">Address</p>
                    </div>
                    <div class="col-12 col-md-8 text-md-right">
                        <p class="mb-0">{{ order.billing_address1 }}</p>
                        {% if order.billing_address2 %}
                            <p class="mb-0">{{ order.billing_address2 }}</p>
                        {% endif %}
                        <p class="mb-0">{{ order.billing_city }}</p>
                        <p class="mb-0">{{ order.billing_postcode }}, {{ order.billing_country }}</p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-md-4">
                        <p class="mb-0 font-weight-bold">Total</p>
                    </div>
                    <div class="col-12 col-md-8 text-md-right">
                        <p class="mb-0">${{ order.total_amount }}</p>
                    </div>
                </div>
                
                <!-- Tax notice -->
                <div class="row mt-2">
                    <div class="col-12 text-md-right">
                        <p class="small text-muted">Final price (taxes may apply in some locations)</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12 col-lg-7 text-right mt-3">
            {% if from_profile %}
                <a href="{% url 'profile' %}" class="btn btn-white rounded-0 my-2" aria-label="Return to your profile page">
                    <span class="icon mr-2">
                        <i class="fas fa-angle-left text-white" aria-hidden="true"></i>
                    </span>
                    <span class="text-uppercase text-white">Back to Profile</span>
                </a>
            {% else %}
                <a href="{% url 'works' %}" class="btn btn-white rounded-0 my-2" aria-label="Browse more works and check latest deals">
                    <span class="icon mr-2">
                        <i class="fas fa-gifts text-white" aria-hidden="true"></i>
                    </span>
                    <span class="text-uppercase text-white">Now check out the latest deals!</span>
                </a>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
