// Contest App JavaScript

// Main initialization function that can be called multiple times
function initializeContestApp() {
    console.log('Initializing contest app...');

    // Star Rating System
    initializeStarRating();

    // Book Part Selection Enhancement
    initializeBookPartFilter();

    // Initialize file upload preview
    initializeFileUploadPreview();

    // Initialize form validation
    initializeFormValidation();

    // Initialize chapter selection
    initializeChapterSelection();
}

// Initialize on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded, initializing app...');

    // Load chapters data
    loadChaptersData();

    // Initialize all components
    initializeContestApp();

    // Also set up a MutationObserver to handle dynamic content loading
    setupMutationObserver();
});

// Load chapters data
// Use var instead of let to avoid redeclaration issues when script is loaded multiple times
var chaptersData = chaptersData || null;

function loadChaptersData() {
    // Skip the fetch and use hardcoded data directly
    console.log('Using hardcoded chapters data');
    chaptersData = getHardcodedChaptersData();
    initializeChapterSelection();
}

// Initialize the star rating system
function initializeStarRating() {
    console.log('Initializing star rating system from contest.js...');

    // Note: The actual star rating functionality is now handled by inline JavaScript
    // in the submission_form.html template for better reliability

    // This function is kept for compatibility with existing code
}

// Helper function to highlight stars on hover
function highlightStars(stars, rating) {
    stars.forEach(star => {
        const starRating = parseInt(star.getAttribute('data-rating'));
        if (starRating <= rating) {
            star.classList.remove('far');
            star.classList.add('fas');
        } else {
            star.classList.remove('fas');
            star.classList.add('far');
        }
    });
}

// Helper function to update stars permanently
function updateStars(stars, rating) {
    stars.forEach(star => {
        const starRating = parseInt(star.getAttribute('data-rating'));
        if (starRating <= rating) {
            star.classList.remove('far');
            star.classList.add('fas');
        } else {
            star.classList.remove('fas');
            star.classList.add('far');
        }
    });
}

// Initialize the book part filter
function initializeBookPartFilter() {
    // Function kept for compatibility but functionality removed
    console.log('Book part filter initialization skipped as requested');
}

// Initialize the chapter selection based on book part
function initializeChapterSelection() {
    console.log('Initializing chapter selection');
    const bookPartSelect = document.getElementById('id_book_part');
    const chapterSelect = document.getElementById('id_chapter_reference');

    if (!bookPartSelect) {
        console.log('Book part select element not found');
        return;
    }

    if (!chapterSelect) {
        console.log('Chapter reference select element not found');
        return;
    }

    // Set initial state if editing an existing submission
    if (bookPartSelect.value) {
        console.log('Initial book part value:', bookPartSelect.value);

        // Enable chapter select
        chapterSelect.disabled = false;

        // If there's a pre-selected chapter, select it
        const currentChapter = chapterSelect.getAttribute('data-current');
        if (currentChapter) {
            let found = false;
            for (let i = 0; i < chapterSelect.options.length; i++) {
                if (chapterSelect.options[i].text === currentChapter) {
                    chapterSelect.selectedIndex = i;
                    found = true;
                    break;
                }
            }

            if (!found) {
                // If we didn't find an exact match, add the current value as an option
                const option = document.createElement('option');
                option.value = currentChapter;
                option.text = currentChapter;
                option.selected = true;
                chapterSelect.add(option);
            }
        }
    }

    // Remove existing event listeners by cloning and replacing
    const newBookPartSelect = bookPartSelect.cloneNode(true);
    bookPartSelect.parentNode.replaceChild(newBookPartSelect, bookPartSelect);

    // Add change event to book part select
    newBookPartSelect.addEventListener('change', function() {
        console.log('Book part changed to:', this.value);

        if (this.value) {
            chapterSelect.disabled = false;
        } else {
            // Clear chapters if no book part selected
            chapterSelect.innerHTML = '<option value="">Please select a book part first</option>';
            chapterSelect.disabled = true;
        }
    });
}

// Initialize file upload preview
function initializeFileUploadPreview() {
    const fileInput = document.querySelector('input[type="file"]');
    if (!fileInput) return;

    // Remove existing event listeners by cloning and replacing
    const newFileInput = fileInput.cloneNode(true);
    fileInput.parentNode.replaceChild(newFileInput, fileInput);

    newFileInput.addEventListener('change', function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // Check if there's an existing preview
                let preview = document.querySelector('.file-preview');

                // If no preview exists, create one
                if (!preview) {
                    preview = document.createElement('img');
                    preview.classList.add('file-preview');
                    newFileInput.parentNode.appendChild(preview);
                }

                // Set the preview source
                preview.src = e.target.result;

                // Show the preview
                preview.style.display = 'block';
            };

            // Check if the file is an image
            if (file.type.match('image.*')) {
                reader.readAsDataURL(file);
            } else {
                // If not an image, show a placeholder
                let preview = document.querySelector('.file-preview');
                if (preview) {
                    preview.style.display = 'none';
                }

                // Show file type info
                let fileInfo = document.querySelector('.file-info');
                if (!fileInfo) {
                    fileInfo = document.createElement('div');
                    fileInfo.classList.add('file-info', 'alert', 'alert-info', 'mt-2');
                    newFileInput.parentNode.appendChild(fileInfo);
                }

                fileInfo.textContent = `Selected file: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)`;
                fileInfo.style.display = 'block';
            }
        }
    });
}

// Initialize form validation
function initializeFormValidation() {
    // Submission form validation
    const submissionForm = document.querySelector('.submission-form');
    if (!submissionForm) return;

    // Remove existing event listeners by cloning and replacing
    const newSubmissionForm = submissionForm.cloneNode(true);
    submissionForm.parentNode.replaceChild(newSubmissionForm, submissionForm);

    newSubmissionForm.addEventListener('submit', function(e) {
        const reviewText = document.getElementById('id_review_text');
        if (reviewText && reviewText.value) {
            const wordCount = reviewText.value.trim().split(/\s+/).length;
            if (wordCount < 48 || wordCount > 200) {
                e.preventDefault();
                alert('Your review must be between 48 and 200 words. Current word count: ' + wordCount);
                reviewText.focus();
            }
        }
    });

    // Word counter for review text
    const reviewTextarea = document.getElementById('id_review_text');
    if (!reviewTextarea) return;

    // Check if word counter already exists
    let wordCounter = reviewTextarea.parentNode.querySelector('.word-counter');
    if (!wordCounter) {
        // Create word counter element
        wordCounter = document.createElement('div');
        wordCounter.classList.add('word-counter', 'text-muted', 'small', 'mt-1');
        reviewTextarea.parentNode.appendChild(wordCounter);
    }

    // Remove existing event listeners by cloning and replacing
    const newReviewTextarea = reviewTextarea.cloneNode(true);
    reviewTextarea.parentNode.replaceChild(newReviewTextarea, reviewTextarea);

    // Update word count on input
    function updateWordCount() {
        const text = newReviewTextarea.value.trim();
        const wordCount = text ? text.split(/\s+/).length : 0;
        wordCounter.textContent = `Word count: ${wordCount} (required: 48-200 words)`;

        // Add visual feedback
        if (wordCount < 48 || wordCount > 200) {
            wordCounter.classList.add('text-danger');
            wordCounter.classList.remove('text-success');
        } else {
            wordCounter.classList.add('text-success');
            wordCounter.classList.remove('text-danger');
        }
    }

    newReviewTextarea.addEventListener('input', updateWordCount);

    // Initial count
    updateWordCount();

    // Jury score range input
    const scoreRange = document.querySelector('input[type="range"]');
    if (scoreRange) {
        const scoreValue = document.querySelector('.score-value');
        if (scoreValue) {
            // Remove existing event listeners by cloning and replacing
            const newScoreRange = scoreRange.cloneNode(true);
            scoreRange.parentNode.replaceChild(newScoreRange, scoreRange);

            newScoreRange.addEventListener('input', function() {
                scoreValue.textContent = this.value;
            });
        }
    }
}

// Set up a MutationObserver to detect DOM changes
function setupMutationObserver() {
    // Create an observer instance
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            // If nodes were added, check if they contain our rating elements
            if (mutation.addedNodes.length > 0) {
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === 1) { // Element node
                        if (node.querySelector('.rating-container') ||
                            node.classList && node.classList.contains('rating-container')) {
                            console.log('Rating container added to DOM, reinitializing');
                            initializeStarRating();
                        }
                    }
                }
            }
        });
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
    console.log('MutationObserver set up to watch for DOM changes');
}

// Fallback function to provide hardcoded chapters data
function getHardcodedChaptersData() {
    return {
        "title": "The Age of New Era",
        "author": "Rafal Zygula",
        "parts": [
            {
                "part_number": 1,
                "title": "The Origin",
                "chapters": [
                    { "number": 1, "title": "Straight Street Ground" },
                    { "number": 2, "title": "Deep Loopia" },
                    { "number": 3, "title": "Tigers and Cages" },
                    { "number": 4, "title": "Sunday Shade" },
                    { "number": 5, "title": "Impact Choice" },
                    { "number": 6, "title": "Conscientious Consequence" },
                    { "number": 7, "title": "Enlightening Storm" },
                    { "number": 8, "title": "Journey" },
                    { "number": 9, "title": "Rerouted Trip" },
                    { "number": 10, "title": "Dragon Dance" },
                    { "number": 11, "title": "Simpler Times" },
                    { "number": 12, "title": "The Enlivening Happening" },
                    { "number": 13, "title": "The Seal" },
                    { "number": 14, "title": "Change" },
                    { "number": 15, "title": "The Spirit Drive" },
                    { "number": 16, "title": "Initial Intention" },
                    { "number": 17, "title": "Big Escape" }
                ]
            },
            {
                "part_number": 2,
                "title": "The Scrutiny",
                "chapters": [
                    { "number": 18, "title": "Questioning Knowledge" },
                    { "number": 19, "title": "Contemplation Blizzard" },
                    { "number": 20, "title": "Baby Steps" },
                    { "number": 21, "title": "Veracity" },
                    { "number": 22, "title": "Information Dumping" },
                    { "number": 23, "title": "Conscious Insights" },
                    { "number": 24, "title": "Misleading Myths" },
                    { "number": 25, "title": "Streams of Consciousness" },
                    { "number": 26, "title": "Humanity Composition" },
                    { "number": 27, "title": "Universal Wisdom" }
                ]
            },
            {
                "part_number": 3,
                "title": "The Tempus",
                "chapters": [
                    { "number": 28, "title": "The Reverse Day" },
                    { "number": 29, "title": "Vision Layers" },
                    { "number": 30, "title": "Test Flight" },
                    { "number": 31, "title": "The G" },
                    { "number": 32, "title": "Whale's Awakening" },
                    { "number": 33, "title": "Core Issues" },
                    { "number": 34, "title": "Early Preparations" },
                    { "number": 35, "title": "Responsibility Whip" },
                    { "number": 36, "title": "Red Destination" },
                    { "number": 37, "title": "Modification Exploration" },
                    { "number": 38, "title": "The Message" },
                    { "number": 39, "title": "Kind Rebellion" },
                    { "number": 40, "title": "Enigmatic Feedback" },
                    { "number": 41, "title": "Arduous Task" },
                    { "number": 42, "title": "The TUP" },
                    { "number": 43, "title": "New Systems" },
                    { "number": 44, "title": "Camping Time" },
                    { "number": 45, "title": "Thoughtful Advancement" },
                    { "number": 46, "title": "Balanced Progress" },
                    { "number": 47, "title": "The Expedition" },
                    { "number": 48, "title": "Edgy Middle" },
                    { "number": 49, "title": "Dashing Tempus" }
                ]
            }
        ]
    };
}

// Call initializeContestApp when the page is loaded via Turbolinks or similar
if (typeof window.addEventListener === 'function') {
    // For Turbolinks 5+
    document.addEventListener('turbolinks:load', function() {
        console.log('Turbolinks load event detected, initializing app...');
        initializeContestApp();
    });

    // For other SPA frameworks
    window.addEventListener('load', function() {
        console.log('Window load event detected, initializing app...');
        initializeContestApp();
    });
}
