(function($) {
  $(document).ready(function() {
    // Function to handle image preview standardization
    function handleImagePreview() {
      $('.field-image input[type="file"]').on('change', function() {
        const input = this;
        const imgPreview = $('<img class="preview-img" style="max-height: 150px; margin-top: 10px; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">');
        
        // Remove any existing preview
        $(input).closest('.form-row').find('.preview-img').remove();
        
        if (input.files && input.files[0]) {
          const reader = new FileReader();
          
          reader.onload = function(e) {
            imgPreview.attr('src', e.target.result);
            $(input).after(imgPreview);
            
            // Add a loading indicator
            const loadingMsg = $('<span class="loading-msg" style="display: block; margin-top: 5px; color: #007bff;">Processing image...</span>');
            $(input).after(loadingMsg);
            
            // Remove the loading message after the image loads
            imgPreview.on('load', function() {
              loadingMsg.remove();
            });
          };
          
          reader.readAsDataURL(input.files[0]);
        }
      });
    }
    
    // Initialize image preview on page load
    handleImagePreview();
    
    // Initialize on formset add
    $(document).on('formset:added', function(event, $row, formsetName) {
      if (formsetName.includes('postimage')) {
        handleImagePreview();
      }
    });
    
    // Helper function to set default values for alt text from filename
    $('.field-image input[type="file"]').on('change', function() {
      const input = this;
      const fileName = input.files[0]?.name || '';
      
      if (fileName) {
        // Extract name without extension
        const baseName = fileName.split('.').slice(0, -1).join('.');
        let altText = baseName
          .replace(/[_-]/g, ' ')  // Replace underscores and hyphens with spaces
          .replace(/\s+/g, ' ')   // Replace multiple spaces with a single space
          .trim();                // Trim leading/trailing spaces

        // Improve alt text with better formatting
        altText = altText
          .toLowerCase()
          .split(' ')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');

        // Add descriptive prefix if not already descriptive
        if (altText && !altText.match(/^(image|photo|picture|illustration|diagram|chart|graph)/i)) {
          altText = 'Image: ' + altText;
        }

        // Look for the alt_text field in the same row
        const altTextField = $(input).closest('tr').find('.field-alt_text input');

        // Only set if the field is empty
        if (altTextField.length > 0 && !altTextField.val()) {
          altTextField.val(altText);
          // Highlight briefly to show it was auto-filled
          altTextField.css('background-color', '#e8f4ff');
          setTimeout(() => {
            altTextField.css('background-color', '');
          }, 1000);

          // Add a note about improving the alt text
          const helpText = altTextField.closest('.form-row').find('.help');
          if (helpText.length === 0) {
            altTextField.after('<div class="help" style="font-size: 0.8em; color: #666; margin-top: 5px;">Auto-generated alt text. Please review and improve for better accessibility.</div>');
          }
        }
      }
    });
  });
})(django.jQuery); 