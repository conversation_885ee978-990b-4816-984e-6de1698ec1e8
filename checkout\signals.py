import logging
import os
import socket
import time
import threading
from django.conf import settings
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.exceptions import ObjectDoesNotExist

from .models import Order
from .combined_email import send_combined_order_email
from .free_book_email import send_free_book_email
from email_manager.utils import should_combine_order_emails

# Set up logger
logger = logging.getLogger(__name__)

# Dictionary to track email sending status
email_sending_tracker = {}


@receiver(post_save, sender=Order)
def send_order_email_on_payment_confirmation(sender, instance, created, **kwargs):
    """
    Send order confirmation and digital content in a single email when an
    order is marked as paid or as a free book. Uses the appropriate email function
    based on the payment status and configuration.
    """
    order_id = instance.id

    # Skip if email is already being processed
    if email_sending_tracker.get(order_id):
        logger.info(f"Email already being processed for order {order_id}")
        return

    # Skip if email has already been sent or failed
    if instance.email_delivery_status in ['sent', 'failed']:
        logger.info(
            f"Skipping email for order {order_id} - "
            f"status already: {instance.email_delivery_status}"
        )
        return

    # Skip if this is a free contest book access order (we don't want to send emails for these)
    # But we DO want to send emails for paid contest book purchases
    if hasattr(instance, '_contest_book_access') and instance._contest_book_access and instance.payment_status == 'free_book':
        logger.info(f"Order {order_id} is a free contest book access order, skipping email")
        return

    logger.info(
        f"Signal triggered for order {order_id} "
        f"with payment status: {instance.payment_status}"
    )

    # Process all orders regardless of webhook flag
    # This ensures emails are sent for direct purchases too
    logger.info(f"Processing email for order {order_id} with payment status {instance.payment_status}")

    # Process based on payment status
    if instance.payment_status == 'paid' or instance.payment_status == 'free_book':
        # Mark this order as being processed
        email_sending_tracker[order_id] = True

        logger.info(f"Starting order email delivery for {order_id}")

        # For free books, use the free book email
        if instance.payment_status == 'free_book':
            # Launch free book email sending in a background thread
            email_thread = threading.Thread(
                target=send_free_book_email,
                args=(instance,)
            )
            email_thread.daemon = True
            email_thread.start()

            logger.info(f"Free book email process started for order {order_id}")
        # For paid orders, check if we should use combined emails
        elif should_combine_order_emails():
            # Launch combined email sending in a background thread
            email_thread = threading.Thread(
                target=send_combined_order_email,
                args=(instance,)
            )
            email_thread.daemon = True
            email_thread.start()

            logger.info(f"Combined email process started for order {order_id}")
        else:
            # Fall back to original separate emails
            email_thread = threading.Thread(
                target=send_digital_content_email,
                args=(order_id,)
            )
            email_thread.daemon = True
            email_thread.start()

            logger.info(f"Separate email process started for order {order_id}")


def send_digital_content_email(order_id):
    """Send digital content email in a background thread."""
    try:
        # Retrieve the order
        try:
            instance = Order.objects.get(id=order_id)

            # Double-check email hasn't been sent while we were starting up
            if instance.email_delivery_status in ['sent', 'failed']:
                logger.info(f"Email already processed for order {order_id}")
                return

        except ObjectDoesNotExist:
            logger.error(f"Order {order_id} not found")
            email_sending_tracker.pop(order_id, None)
            return

        # Get all items in the order
        order_items = instance.orderitem_set.all()

        digital_items = []
        for item in order_items:
            if item.product.ebook_file or item.product.audiobook_link:
                digital_items.append(item)
                logger.info(f"Found digital content in {item.product.name}")

        # If there is digital content, send an email
        if digital_items:
            # Prepare email content
            context = {
                'order': instance,
                'digital_items': digital_items,
                'contact_email': settings.DEFAULT_FROM_EMAIL,
                'SITE_URL': settings.SITE_URL,
            }

            # Render email templates
            html_content = render_to_string(
                'checkout/emails/ebook_delivery_email.html',
                context
            )
            text_content = strip_tags(html_content)

            # Create email
            subject = (
                f'Your digital content purchase from '
                f'{settings.CONTACT_DISPLAY_NAME}'
            )
            from_email = settings.DEFAULT_FROM_EMAIL
            to_email = instance.email

            # Create email message
            email = EmailMultiAlternatives(
                subject,
                text_content,
                from_email,
                [to_email]
            )

            email.attach_alternative(html_content, "text/html")

            # Attach ebook files
            attachments_successful = []
            attachments_failed = []

            for item in digital_items:
                if item.product.ebook_file:
                    file_path = os.path.join(
                        settings.MEDIA_ROOT,
                        str(item.product.ebook_file)
                    )

                    try:
                        file_name = os.path.basename(file_path)
                        with open(file_path, 'rb') as f:
                            file_content = f.read()
                            email.attach(
                                file_name,
                                file_content,
                                'application/pdf'
                            )
                            attachments_successful.append(file_name)
                    except FileNotFoundError:
                        logger.error(f"Ebook file not found: {file_path}")
                        attachments_failed.append(file_path)
                    except Exception as e:
                        logger.error(
                            f"Error attaching file {file_path}: {str(e)}"
                        )
                        attachments_failed.append(file_path)

            # Send email with retry logic
            max_retries = 3
            retry_count = 0
            delivery_success = False

            while retry_count < max_retries and not delivery_success:
                try:
                    logger.info(f"Sending email (attempt {retry_count + 1})")
                    email.send(fail_silently=False)
                    delivery_success = True
                    logger.info("Email sent successfully")

                    # Update order status directly in the database
                    Order.objects.filter(id=order_id).update(
                        email_delivery_status='sent'
                    )

                    # Log attachment results
                    if attachments_successful:
                        logger.info(f"Files: {', '.join(attachments_successful)}")
                    if attachments_failed:
                        logger.warning(f"Failed: {', '.join(attachments_failed)}")

                except (socket.error, Exception) as e:
                    retry_count += 1
                    logger.error(f"Email sending failed: {str(e)}")

                    if retry_count >= max_retries:
                        logger.error("Max retries reached, marking as failed")
                        Order.objects.filter(id=order_id).update(
                            email_delivery_status='failed'
                        )
                    else:
                        time.sleep(2)  # Wait before retry
        else:
            logger.info("No digital content found, skipping email")

    except Exception as e:
        logger.error(f"Critical error in email process: {str(e)}")
    finally:
        # Always clean up the tracker
        email_sending_tracker.pop(order_id, None)
