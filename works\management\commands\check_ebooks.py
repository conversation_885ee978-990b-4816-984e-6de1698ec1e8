from django.core.management.base import BaseCommand
from works.models import Product
from checkout.models import Order, OrderItem


class Command(BaseCommand):
    help = 'Check products with ebook files and orders with ebook products'

    def handle(self, *args, **options):
        # Check products with ebook files
        self.stdout.write(self.style.SUCCESS('Checking products with ebook files...'))
        products_with_ebooks = Product.objects.exclude(ebook_file='')
        
        if not products_with_ebooks:
            self.stdout.write(self.style.WARNING('No products found with ebook files!'))
        else:
            self.stdout.write(self.style.SUCCESS(f'Found {products_with_ebooks.count()} products with ebook files:'))
            for product in products_with_ebooks:
                self.stdout.write(f'  - {product.name}: {product.ebook_file}')
        
        # Check recent orders
        self.stdout.write('\n' + self.style.SUCCESS('Checking recent orders...'))
        recent_orders = Order.objects.all().order_by('-order_date')[:10]
        
        if not recent_orders:
            self.stdout.write(self.style.WARNING('No recent orders found!'))
        else:
            self.stdout.write(self.style.SUCCESS(f'Found {recent_orders.count()} recent orders:'))
            for order in recent_orders:
                self.stdout.write(f'  - Order {order.id}: Status={order.payment_status}, Email={order.email}')
                
                # Check if order has ebook products
                order_items = OrderItem.objects.filter(order=order)
                ebook_items = [item for item in order_items if item.product.ebook_file]
                
                if ebook_items:
                    self.stdout.write(self.style.SUCCESS(f'    Order contains {len(ebook_items)} ebook products:'))
                    for item in ebook_items:
                        self.stdout.write(f'      - {item.product.name}: {item.product.ebook_file}')
                else:
                    self.stdout.write(self.style.WARNING('    Order does not contain any ebook products'))
