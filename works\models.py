from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse


class Category(models.Model):
    """
    Model representing product categories.
    """
    class Meta:
        verbose_name_plural = 'Categories'

    name = models.Char<PERSON>ield(max_length=255)
    friendly_name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)

    def __str__(self):
        """
        String representation of the Category model.
        """
        return self.friendly_name or self.name

    def get_friendly_name(self):
        """
        Get the user-friendly name for the category.
        """
        return self.friendly_name


class Genre(models.Model):
    """
    Model representing genres.
    """
    name = models.Char<PERSON>ield(max_length=100)

    def __str__(self):
        """
        String representation of the Genre model.
        """
        return self.name


class File_format(models.Model):
    """
    Model representing file formats.
    """
    name = models.CharField(max_length=100)

    def __str__(self):
        """
        String representation of the File_format model.
        """
        return self.name


class Language(models.Model):
    """
    Model representing languages.
    """
    name = models.Char<PERSON>ield(max_length=50)

    def __str__(self):
        """
        String representation of the Language model.
        """
        return self.name


class Product(models.Model):
    """
    Model representing products/works available for purchase.
    """
    BOOK_PART_CHOICES = [
        ('full', 'Full Book'),
        ('part1', 'Part 1: The Origin'),
        ('part2', 'Part 2: The Scrutiny'),
        ('part3', 'Part 3: The Tempus'),
        ('other', 'Other')
    ]

    category = models.ForeignKey(
        'Category', null=True, blank=True, on_delete=models.SET_NULL
    )
    genre = models.ManyToManyField('Genre', blank=True)
    file_format = models.ManyToManyField('File_format', blank=True)
    language = models.ForeignKey(
        'Language', null=True, blank=True, on_delete=models.SET_NULL
    )
    name = models.CharField(max_length=255)
    description = models.TextField()
    price = models.DecimalField(max_digits=9, decimal_places=2)
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)
    image = models.ImageField(upload_to='products/', null=True, blank=True)
    image_url = models.URLField(max_length=1024, null=True, blank=True)
    ebook_file = models.FileField(
        upload_to='ebooks/',
        null=True,
        blank=True,
        help_text="Upload the ebook file (PDF) that customers will receive after purchase"
    )
    audiobook_link = models.URLField(
        max_length=1024,
        null=True,
        blank=True,
        help_text="Link to the audiobook (MP3) that customers will access after purchase"
    )
    audiobook_sample_link = models.URLField(
        max_length=1024,
        null=True,
        blank=True,
        help_text="Link to the audiobook sample JSON that customers can preview before purchase"
    )

    # New field to identify which book part this product represents
    book_part = models.CharField(
        max_length=10,
        choices=BOOK_PART_CHOICES,
        default='other',
        help_text="Which part of the book this product represents"
    )

    # Related products field to link different formats of the same book part
    related_products = models.ManyToManyField(
        'self',
        blank=True,
        symmetrical=True,
        help_text="Other formats of the same book part (e.g., audiobook version of an ebook)"
    )

    # New fields for work details
    pages = models.PositiveIntegerField(
        help_text="Number of pages in the work (for ebooks)",
        null=True,
        blank=True
    )

    duration = models.PositiveIntegerField(
        help_text="Duration in minutes (for audiobooks)",
        null=True,
        blank=True
    )

    excerpt = models.TextField(
        blank=True, help_text="A sample excerpt from the work"
    )

    @property
    def is_digital(self):
        """Check if the product is digital (has ebook or audiobook)."""
        return bool(self.ebook_file or self.audiobook_link)

    def has_digital_content(self):
        """
        Check if the product has digital content (ebook or audiobook).
        This method is used by the free book email system.
        """
        return bool(self.ebook_file or self.audiobook_link)

    def reading_time(self):
        """
        Calculate the estimated reading time based on the number of pages.
        Assuming an average reading speed of 2 minutes per page.
        """
        if not self.pages:
            return 0
        return round(self.pages * 1.8)

    def get_related_products(self):
        """
        Get all products that are related to this one (same book part, different formats).
        Includes self in the result.
        """
        # Start with directly related products
        related = list(self.related_products.all())

        # Add self to the list
        related.append(self)

        # If book_part is set, also find products with the same book_part
        if self.book_part and self.book_part != 'other':
            same_part_products = Product.objects.filter(book_part=self.book_part).exclude(id=self.id)
            for product in same_part_products:
                if product not in related:
                    related.append(product)

        return related

    def get_all_reviews(self):
        """
        Get all approved reviews for this product and its related products.
        """
        from django.db.models import Q

        # Get all related products
        related_products = self.get_related_products()
        related_ids = [p.id for p in related_products]

        # Get all approved reviews for these products
        from .models import Review
        reviews = Review.objects.filter(
            product_id__in=related_ids,
            approved=True
        ).order_by('-created_date')

        return reviews

    def average_rating(self):
        """
        Calculate the average rating from approved reviews of this product
        and its related products.
        """
        reviews = self.get_all_reviews()
        if reviews:
            return sum(review.rating for review in reviews) / len(reviews)
        return 0

    def total_reviews_count(self):
        """
        Count all approved reviews for this product and its related products.
        """
        return self.get_all_reviews().count()

    def __str__(self):
        """
        String representation of the Product model.
        """
        return self.name

    def get_absolute_url(self):
        """
        Get the URL for the product detail view.
        """
        return reverse('work_detail', kwargs={'work_id': self.id})


class Review(models.Model):
    """
    Model representing user reviews for products.

    Each user can only submit one review per product.
    Reviews must be approved before being displayed.
    """
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name='reviews'
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    rating = models.IntegerField(
        choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5')],
        verbose_name="Rating (required)"
    )
    comment = models.TextField(
        blank=True, null=True, verbose_name="Review (optional)",
        help_text="Share your thought about this story (optional)."
    )
    created_date = models.DateTimeField(auto_now_add=True)
    approved = models.BooleanField(default=False)

    class Meta:
        ordering = ['-created_date']
        unique_together = ('product', 'user')

    def __str__(self):
        """
        String representation of the Review model.
        """
        return f'{self.user.username} review for {self.product.name}'


class ProductAttachment(models.Model):
    """
    Model representing attachments for products.

    This allows for flexible addition of different types of files or links
    to products, such as PDF files, audiobook links, promotional materials, etc.
    """
    ATTACHMENT_TYPE_CHOICES = [
        ('file', 'File Upload'),
        ('link', 'External Link'),
    ]

    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='attachments'
    )
    name = models.CharField(
        max_length=255,
        help_text="Name of the attachment (e.g., 'Audiobook', 'Bonus Chapter', 'Promotional Poster')"
    )
    description = models.TextField(
        blank=True,
        help_text="Optional description of the attachment"
    )
    attachment_type = models.CharField(
        max_length=10,
        choices=ATTACHMENT_TYPE_CHOICES,
        default='file'
    )
    file = models.FileField(
        upload_to='product_attachments/',
        null=True,
        blank=True,
        help_text="Upload file attachment (PDF, MP3, etc.)"
    )
    link = models.URLField(
        max_length=1024,
        null=True,
        blank=True,
        help_text="URL to external resource (e.g., audiobook streaming link)"
    )
    is_promotional = models.BooleanField(
        default=False,
        help_text="Mark as promotional material that may be temporary"
    )
    created_date = models.DateTimeField(auto_now_add=True)
    updated_date = models.DateTimeField(auto_now=True)

    def __str__(self):
        """
        String representation of the ProductAttachment model.
        """
        return f"{self.name} - {self.product.name}"

    def clean(self):
        """
        Validate that either file or link is provided based on attachment_type.
        """
        from django.core.exceptions import ValidationError

        if self.attachment_type == 'file' and not self.file:
            raise ValidationError("File must be provided for 'File Upload' attachment type.")

        if self.attachment_type == 'link' and not self.link:
            raise ValidationError("URL must be provided for 'External Link' attachment type.")
