from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponseForbidden
from django.db.models import Sum, Count, Q
from django.utils import timezone
from django.utils.safestring import mark_safe
from django.core.paginator import Paginator
from works.models import Review, Product


from .models import Contest, Category, Submission, Like, JuryScore, Comment, BookAccessRequest
from .forms import SubmissionForm, JuryScoreForm, CommentForm


def contest_list(request):
    """
    Redirect to the active contest or show a message if no active contest.
    """
    active_contest = Contest.objects.filter(is_active=True).order_by('-start_date').first()
    if active_contest:
        return redirect('contest:contest_detail', slug=active_contest.slug)

    # If no active contest, show a message with past contests for context
    past_contests = Contest.objects.filter(is_active=False).order_by('-end_date')[:3]
    upcoming_contests = Contest.objects.filter(
        is_active=False,
        start_date__gt=timezone.now().date()
    ).order_by('start_date')[:2]

    context = {
        'contests': [],
        'past_contests': past_contests,
        'upcoming_contests': upcoming_contests,
        'show_contest_info': True
    }
    return render(request, 'contest/contest_list.html', context)


def contest_detail(request, slug):
    """
    Display details of a specific contest.
    """
    contest = get_object_or_404(Contest, slug=slug)
    categories = contest.categories.all()

    # Get submission counts by category
    category_stats = []
    for category in categories:
        submission_count = Submission.objects.filter(
            contest=contest,
            category=category,
            is_approved=True
        ).count()
        category_stats.append({
            'category': category,
            'submission_count': submission_count
        })

    # Check if user has made submissions
    user_submissions = []
    if request.user.is_authenticated:
        user_submissions = Submission.objects.filter(
            contest=contest,
            user=request.user
        ).order_by('-submitted_at')

    context = {
        'contest': contest,
        'categories': categories,
        'category_stats': category_stats,
        'user_submissions': user_submissions,
    }
    return render(request, 'contest/contest_detail.html', context)


def contest_terms(request, slug):
    """
    Display the terms and conditions for a contest.
    """
    contest = get_object_or_404(Contest, slug=slug)
    return render(request, 'contest/contest_terms.html', {'contest': contest})


def contest_guide(request, slug):
    """
    Display the inspiration guide for a contest.
    """
    contest = get_object_or_404(Contest, slug=slug)
    return render(request, 'contest/contest_guide.html', {'contest': contest})


@login_required
def book_access_request(request, slug):
    """
    Handle book access requests for contest participants.
    Each participant can only request access to one book part.
    """
    contest = get_object_or_404(Contest, slug=slug)

    # Check if user already has any book access request for this contest
    existing_requests = BookAccessRequest.objects.filter(
        user=request.user,
        contest=contest
    )

    # If user already has an approved or granted request, redirect with a friendly message
    existing_granted_request = existing_requests.filter(
        Q(status='approved') | Q(access_granted=True)
    ).first()

    if existing_granted_request:
        book_part = existing_granted_request.book_part
        format_type = existing_granted_request.get_format_type_display()

        # Add alert-persistent class to prevent auto-dismissal
        messages.info(
            request,
            f"""
            <div class="alert-content alert-persistent">
                <h4>You already have access to a book part!</h4>
                <p>You've already been granted access to <strong>{book_part.name}</strong> ({format_type}).</p>
                <p>For the Tempus Quest Illustration Contest, each participant can request access to one book part only.</p>
                <p>You can find your book in <a href="/profile/my-library/" class="alert-link">your library</a>.</p>
            </div>
            """
        )
        return redirect('contest:contest_detail', slug=contest.slug)

    # If user has a pending request, show a friendly message
    existing_pending_request = existing_requests.filter(status='pending').first()
    if existing_pending_request:
        book_part = existing_pending_request.book_part
        format_type = existing_pending_request.get_format_type_display()

        # Add alert-persistent class to prevent auto-dismissal
        messages.info(
            request,
            f"""
            <div class="alert-content alert-persistent">
                <h4>You already have a pending request!</h4>
                <p>You've already requested access to <strong>{book_part.name}</strong> ({format_type}).</p>
                <p>Your request is currently being processed. We'll notify you by email once it's approved.</p>
                <p>For the Tempus Quest Illustration Contest, each participant can request access to one book part only.</p>
                <p>If this error persists, please contact us.</p>
            </div>
            """
        )
        return redirect('contest:contest_detail', slug=contest.slug)

    # Get book parts from the WORKS app - exclude full book
    ebook_parts = Product.objects.filter(
        Q(category__name__icontains='ebook') &
        ~Q(category__name__icontains='bundle')
    ).order_by('name')

    # Filter out full books - using both book_part field and name
    ebook_parts = ebook_parts.exclude(
        Q(book_part='full') |
        Q(name__icontains='whole') |
        Q(name__icontains='full book') |
        Q(name__icontains='complete')
    )

    audiobook_parts = Product.objects.filter(
        Q(category__name__icontains='audiobook') &
        ~Q(category__name__icontains='bundle')
    ).order_by('name')

    # Filter out full books - using both book_part field and name
    audiobook_parts = audiobook_parts.exclude(
        Q(book_part='full') |
        Q(name__icontains='whole') |
        Q(name__icontains='full book') |
        Q(name__icontains='complete')
    )

    # Get contest categories
    categories = Category.objects.filter(contest=contest).order_by('name')

    if request.method == 'POST':
        # Process the form submission
        format_type = request.POST.get('format')
        book_part_id = request.POST.get('book_part')
        access_method = request.POST.get('access_method')
        reason = request.POST.get('reason', '')
        category_id = request.POST.get('category')

        # Get the selected book part and category
        book_part = get_object_or_404(Product, id=book_part_id)
        category = get_object_or_404(Category, id=category_id)

        # For now, just show a success message
        # In a real implementation, you would:
        # 1. Save the request to a database
        # 2. Send notification emails
        # 3. Handle payment for paid access
        # 4. Grant access to the book part

        # Check if user already has a request for this book part
        existing_request = BookAccessRequest.objects.filter(
            user=request.user,
            book_part=book_part
        ).first()

        if existing_request:
            if existing_request.access_granted:
                # Add alert-persistent class to prevent auto-dismissal
                messages.info(
                    request,
                    mark_safe(f"""
                    <div class="alert-content alert-persistent">
                        <h4>You already have access!</h4>
                        <p>You already have access to <strong>{book_part.name}</strong>.</p>
                        <p>You can access it from <a href="/profile/my-library/" class="alert-link">your library</a>.</p>
                    </div>
                    """)
                )
                return redirect('contest:contest_detail', slug=contest.slug)
            elif existing_request.status == 'pending' and existing_request.access_type == access_method:
                # Add alert-persistent class to prevent auto-dismissal
                messages.info(
                    request,
                    mark_safe(f"""
                    <div class="alert-content alert-persistent">
                        <h4>Pending Request</h4>
                        <p>You already have a pending request for <strong>{book_part.name}</strong>.</p>
                        <p>We'll process it as soon as possible.</p>
                    </div>
                    """)
                )
                return redirect('contest:contest_detail', slug=contest.slug)
            elif existing_request.status == 'rejected' and existing_request.access_type == access_method:
                # Update the existing request
                existing_request.status = 'pending'
                existing_request.reason = reason
                existing_request.save()

                messages.success(
                    request,
                    f"Your request for '{book_part.name}' has been resubmitted. "
                    f"We'll process it shortly."
                )
                return redirect('contest:contest_detail', slug=contest.slug)
            else:
                # If the access type is different (free vs paid), update the existing request
                existing_request.access_type = access_method
                existing_request.status = 'pending'
                existing_request.reason = reason
                existing_request.save()

                if access_method == 'free':
                    messages.success(
                        request,
                        f"Your request for free access to '{book_part.name}' has been updated. "
                        f"We'll process it shortly."
                    )
                    return redirect('contest:contest_detail', slug=contest.slug)
                else:  # paid access
                    # For paid access, redirect to add to bookcart
                    return redirect('add_to_bookcart', work_id=book_part_id)

        if access_method == 'free':
            # For free access, create and automatically approve the book access request
            from checkout.models import Order, OrderItem

            # Create the book access request with approved status
            book_request = BookAccessRequest.objects.create(
                contest=contest,
                user=request.user,
                book_part=book_part,
                category=category,
                format_type=format_type,
                access_type='free',
                reason=reason,
                status='approved',  # Auto-approve
                access_granted=True  # Auto-grant access
            )

            # Create a free order to provide access to the book part
            # Check if an order already exists for this user and book part
            existing_order = OrderItem.objects.filter(
                order__user=request.user,
                product=book_part
            ).exists()

            if not existing_order:
                # Create a new order for this free book access
                # Try to create the order with the correct field name (total_amount or total)
                try:
                    # Get user profile data safely
                    profile_data = {}
                    if hasattr(request.user, 'profile'):
                        profile = request.user.profile
                        # Safely get profile fields
                        for field in ['phone_number', 'address1', 'address2', 'city', 'postcode', 'country']:
                            profile_data[field] = getattr(profile, field, '') if hasattr(profile, field) else ''

                    # Create order with safe profile data
                    order = Order.objects.create(
                        user=request.user,
                        email=request.user.email,
                        total_amount=0.00,  # Free access
                        payment_status='free_book',  # Mark as free book
                        email_delivery_status='sent',  # Mark as sent so no email is triggered
                        full_name=request.user.get_full_name() or request.user.username,
                        phone_number=profile_data.get('phone_number', ''),
                        billing_address1=profile_data.get('address1', ''),
                        billing_address2=profile_data.get('address2', ''),
                        billing_city=profile_data.get('city', ''),
                        billing_postcode=profile_data.get('postcode', ''),
                        billing_country=profile_data.get('country', 'US')
                    )

                    # Mark this as a contest book access order
                    order._contest_book_access = True

                except TypeError:
                    # If total_amount doesn't work, try with total
                    order = Order.objects.create(
                        user=request.user,
                        email=request.user.email,
                        total=0.00,  # Free access
                        payment_status='free_book',  # Mark as free book
                        email_delivery_status='sent',  # Mark as sent so no email is triggered
                        full_name=request.user.get_full_name() or request.user.username,
                        phone_number=profile_data.get('phone_number', ''),
                        billing_address1=profile_data.get('address1', ''),
                        billing_address2=profile_data.get('address2', ''),
                        billing_city=profile_data.get('city', ''),
                        billing_postcode=profile_data.get('postcode', ''),
                        billing_country=profile_data.get('country', 'US')
                    )

                    # Mark this as a contest book access order
                    order._contest_book_access = True

                # Create order item
                OrderItem.objects.create(
                    order=order,
                    product=book_part,
                    price=0.00  # Free access
                )

            # Use mark_safe to properly render HTML in the message
            from django.utils.safestring import mark_safe

            # Add alert-persistent class to prevent auto-dismissal
            messages.success(
                request,
                mark_safe(f"""
                <div class="alert-content alert-persistent">
                    <h4><i class="fas fa-check-circle"></i> Access Granted!</h4>
                    <p>You now have free access to <strong>{book_part.name}</strong>.</p>
                    <p>Your book is now available in <a href="/profile/my-library/" class="alert-link">your library</a>.</p>
                    <p>To find 'My Library" click your username in the top-right corner of your screen. You can start your journey right away!</p>
                </div>
                """)
            )
            return redirect('contest:contest_detail', slug=contest.slug)
        else:  # paid access
            # Create a record for tracking purposes
            access_request = BookAccessRequest.objects.create(
                contest=contest,
                user=request.user,
                book_part=book_part,
                category=category,
                format_type=format_type,
                access_type='paid',
                status='pending'
            )

            # For paid access, redirect to add to bookcart
            # Add the product to cart and redirect to checkout
            # Store a flag in the session to indicate this is a contest book purchase
            request.session['contest_book_purchase'] = {
                'contest_id': contest.id,
                'book_part_id': book_part_id,
                'access_request_id': access_request.id
            }
            return redirect('add_to_bookcart', work_id=book_part_id)

    context = {
        'contest': contest,
        'ebook_parts': ebook_parts,
        'audiobook_parts': audiobook_parts,
        'categories': categories,
    }

    return render(request, 'contest/book_access_request.html', context)


@login_required
def submission_create(request, slug):
    """
    Create a new submission for a contest.
    """
    contest = get_object_or_404(Contest, slug=slug)

    # Check if submissions are allowed
    submissions_allowed, message = contest.are_submissions_allowed()
    if not submissions_allowed:
        messages.error(request, message)
        return redirect('contest:contest_detail', slug=slug)

    # Get available book parts - filter for ebooks and audiobooks, exclude full books
    book_parts = Product.objects.filter(
        Q(category__name__icontains='book') |
        Q(category__name__icontains='ebook') |
        Q(category__name__icontains='audiobook')
    ).exclude(book_part='other').distinct()

    # Filter out full books - using both book_part field and name
    book_parts = book_parts.exclude(
        Q(book_part='full') |
        Q(name__icontains='whole') |
        Q(name__icontains='full book') |
        Q(name__icontains='complete')
    )

    if request.method == 'POST':
        form = SubmissionForm(request.POST, request.FILES)
        print(f"Form submitted: {request.POST}")
        print(f"Form is valid: {form.is_valid()}")
        if not form.is_valid():
            print(f"Form errors: {form.errors}")

        if form.is_valid():
            submission = form.save(commit=False)
            submission.user = request.user
            submission.contest = contest
            submission.save()
            print(f"Submission saved with ID: {submission.id}")

            # Handle review creation in the Works app
            from works.models import Review
            book_part = submission.book_part

            # Get all related products (different formats of the same book part)
            related_products = book_part.get_related_products()
            related_product_ids = [p.id for p in related_products]

            # Check if user already has a review for this book part or any related product
            existing_review = Review.objects.filter(
                product_id__in=related_product_ids,
                user=request.user
            ).first()

            # Extract part name from chapter reference or book part
            part_name = "The Age of New Era"

            # First try to determine part from book_part field
            if book_part.book_part == 'part1':
                part_name = "Part I: The Origin"
            elif book_part.book_part == 'part2':
                part_name = "Part II: The Scrutiny"
            elif book_part.book_part == 'part3':
                part_name = "Part III: The Tempus"
            # Fallback to name-based detection if book_part field is not set
            elif book_part.book_part == 'other' or book_part.book_part == 'full':
                book_part_name = book_part.name.lower()
                if "part i" in book_part_name or "part 1" in book_part_name or "origin" in book_part_name:
                    part_name = "Part I: The Origin"
                elif "part ii" in book_part_name or "part 2" in book_part_name or "scrutiny" in book_part_name:
                    part_name = "Part II: The Scrutiny"
                elif "part iii" in book_part_name or "part 3" in book_part_name or "tempus" in book_part_name:
                    part_name = "Part III: The Tempus"

            # If chapter reference contains a chapter number, use that to determine part
            if "Chapter" in submission.chapter_reference:
                chapter_parts = submission.chapter_reference.split(":")
                if len(chapter_parts) > 0:
                    chapter_num = chapter_parts[0].strip()
                    try:
                        chapter_num_int = int(chapter_num.replace("Chapter", "").strip())
                        if 1 <= chapter_num_int <= 17:
                            part_name = "Part I: The Origin"
                        elif 18 <= chapter_num_int <= 27:
                            part_name = "Part II: The Scrutiny"
                        elif 28 <= chapter_num_int <= 49:
                            part_name = "Part III: The Tempus"
                    except ValueError:
                        # If we can't parse the chapter number, stick with the part from book_part
                        pass

            # Each submission has its own book fragment and review text
            # But only the first submission creates a review in the works app

            # Format the review content for the works app
            review_content = (
                f"{submission.review_text}\n\n"
                f'"{submission.book_fragment}"\n\n'
                f'{part_name}\n'
                f'{submission.chapter_reference}'
            )

            if existing_review:
                # User already has a review for this book part
                # We'll keep the existing review and just mark the submission as having a review
                submission.review_created = True
                # Make sure to save the entire submission, not just the review_created field
                submission.save()

                messages.info(
                    request,
                    f"You already have a review for '{book_part.name}'. "
                    f"Your submission has been saved with your new book fragment and description. "
                    f"Your rating from your first submission will be used for all submissions for this book part."
                )
            else:
                # Create a new review
                review = Review(
                    product=book_part,
                    user=request.user,
                    rating=submission.rating,
                    comment=review_content
                )
                review.save()
                submission.review_created = True
                submission.save(update_fields=['review_created'])

                messages.success(
                    request,
                    "Your submission has been received and is pending approval. "
                    "Your review has also been submitted to the book page."
                )
            return redirect('contest:submission_detail', slug=submission.slug)
    else:
        form = SubmissionForm()
        # Limit category choices to this contest's categories
        form.fields['category'].queryset = Category.objects.filter(contest=contest)
        form.fields['book_part'].queryset = book_parts

    context = {
        'form': form,
        'contest': contest,
    }
    return render(request, 'contest/submission_form.html', context)


@login_required
def submission_edit_pk(request, pk):
    """
    Edit an existing submission using primary key.
    """
    submission = get_object_or_404(Submission, pk=pk)
    return submission_edit_view(request, submission)


@login_required
def submission_edit(request, slug):
    """
    Edit an existing submission using slug.
    """
    submission = get_object_or_404(Submission, slug=slug)
    return submission_edit_view(request, submission)


@login_required
def submission_edit_view(request, submission):

    # Check if user is the owner
    if submission.user != request.user:
        return HttpResponseForbidden("You don't have permission to edit this submission.")

    # Check if submissions are allowed
    submissions_allowed, message = submission.contest.are_submissions_allowed()
    if not submissions_allowed:
        messages.error(request, message)
        return redirect('contest:submission_detail', slug=submission.slug)

    if request.method == 'POST':
        form = SubmissionForm(request.POST, request.FILES, instance=submission)
        if form.is_valid():
            form.save()

            # Handle review creation/update in the Works app
            if submission.review_created:
                from works.models import Review
                book_part = submission.book_part

                # Extract part name from chapter reference or book part
                part_name = "The Age of New Era"

                # First try to determine part from book_part field
                if book_part.book_part == 'part1':
                    part_name = "Part I: The Origin"
                elif book_part.book_part == 'part2':
                    part_name = "Part II: The Scrutiny"
                elif book_part.book_part == 'part3':
                    part_name = "Part III: The Tempus"
                # Fallback to name-based detection if book_part field is not set
                elif book_part.book_part == 'other' or book_part.book_part == 'full':
                    book_part_name = book_part.name.lower()
                    if "part i" in book_part_name or "part 1" in book_part_name or "origin" in book_part_name:
                        part_name = "Part I: The Origin"
                    elif "part ii" in book_part_name or "part 2" in book_part_name or "scrutiny" in book_part_name:
                        part_name = "Part II: The Scrutiny"
                    elif "part iii" in book_part_name or "part 3" in book_part_name or "tempus" in book_part_name:
                        part_name = "Part III: The Tempus"

                # If chapter reference contains a chapter number, use that to determine part
                if "Chapter" in submission.chapter_reference:
                    chapter_parts = submission.chapter_reference.split(":")
                    if len(chapter_parts) > 0:
                        chapter_num = chapter_parts[0].strip()
                        try:
                            chapter_num_int = int(chapter_num.replace("Chapter", "").strip())
                            if 1 <= chapter_num_int <= 17:
                                part_name = "Part I: The Origin"
                            elif 18 <= chapter_num_int <= 27:
                                part_name = "Part II: The Scrutiny"
                            elif 28 <= chapter_num_int <= 49:
                                part_name = "Part III: The Tempus"
                        except ValueError:
                            # If we can't parse the chapter number, stick with the part from book_part
                            pass

                # Format the review content
                review_content = (
                    f"{submission.review_text}\n\n"
                    f'"{submission.book_fragment}"\n\n'
                    f'{part_name}\n'
                    f'{submission.chapter_reference}'
                )

                # Get all related products (different formats of the same book part)
                related_products = book_part.get_related_products()
                related_product_ids = [p.id for p in related_products]

                # Check if user already has a review for this book part or any related product
                existing_review = Review.objects.filter(
                    product_id__in=related_product_ids,
                    user=request.user
                ).first()

                if existing_review:
                    # Update the existing review
                    existing_review.rating = submission.rating
                    existing_review.comment = review_content
                    existing_review.save()

                    messages.success(
                        request,
                        "Your submission has been updated. Your book review has also been updated."
                    )
                else:
                    # Create a new review
                    review = Review(
                        product=book_part,
                        user=request.user,
                        rating=submission.rating,
                        comment=review_content
                    )
                    review.save()

                    messages.success(
                        request,
                        "Your submission has been updated. A new book review has been created."
                    )
            else:
                messages.success(request, "Your submission has been updated.")
            # Always use slug for consistent URLs
            return redirect('contest:submission_detail', slug=submission.slug)
    else:
        form = SubmissionForm(instance=submission)
        # Limit category choices to this contest's categories
        form.fields['category'].queryset = Category.objects.filter(contest=submission.contest)
        form.fields['book_part'].queryset = Product.objects.filter(
            Q(category__name__icontains='book') |
            Q(category__name__icontains='ebook') |
            Q(category__name__icontains='audiobook')
        ).exclude(book_part='other').distinct()

        # Filter out full books - using both book_part field and name
        form.fields['book_part'].queryset = form.fields['book_part'].queryset.exclude(
            Q(book_part='full') |
            Q(name__icontains='whole') |
            Q(name__icontains='full book') |
            Q(name__icontains='complete')
        )

    context = {
        'form': form,
        'submission': submission,
        'contest': submission.contest,
    }
    return render(request, 'contest/submission_form.html', context)


@login_required
def submission_delete_pk(request, pk):
    """
    Delete a submission using primary key.
    """
    submission = get_object_or_404(Submission, pk=pk)
    return submission_delete_view(request, submission)


@login_required
def submission_delete(request, slug):
    """
    Delete a submission using slug.
    """
    submission = get_object_or_404(Submission, slug=slug)
    return submission_delete_view(request, submission)


@login_required
def submission_delete_view(request, submission):

    # Check if user is the owner
    if submission.user != request.user:
        return HttpResponseForbidden("You don't have permission to delete this submission.")

    if request.method == 'POST':
        contest_slug = submission.contest.slug

        # Ask if the user wants to delete the associated review as well
        delete_review = request.POST.get('delete_review') == 'on'

        if delete_review and submission.review_created:
            from works.models import Review
            book_part = submission.book_part

            # Get all related products (different formats of the same book part)
            related_products = book_part.get_related_products()
            related_product_ids = [p.id for p in related_products]

            # Find the user's review for this book part or any related product
            existing_review = Review.objects.filter(
                product_id__in=related_product_ids,
                user=request.user
            ).first()

            if existing_review:
                existing_review.delete()
                messages.info(request, "Your book review has also been deleted.")

        submission.delete()
        messages.success(request, "Your submission has been deleted.")
        return redirect('contest:contest_detail', slug=contest_slug)

    return render(request, 'contest/submission_confirm_delete.html', {'submission': submission})


def submission_detail_pk(request, pk):
    """
    Display details of a specific submission using primary key.
    """
    submission = get_object_or_404(Submission, pk=pk)
    return submission_detail_view(request, submission)


def submission_detail(request, slug):
    """
    Display details of a specific submission using slug.
    """
    submission = get_object_or_404(Submission, slug=slug)
    return submission_detail_view(request, submission)


def submission_detail_view(request, submission):

    # Check if submission is approved or if user is the owner
    if not submission.is_approved and (not request.user.is_authenticated or submission.user != request.user):
        messages.error(request, "This submission is not yet approved for public viewing.")
        return redirect('contest:contest_detail', slug=submission.contest.slug)

    # Check if user has liked this submission
    user_liked = False
    if request.user.is_authenticated:
        user_liked = Like.objects.filter(submission=submission, user=request.user).exists()

    # Get jury scores if user is a judge
    jury_score = None
    if request.user.is_authenticated:
        jury_score = JuryScore.objects.filter(submission=submission, judge=request.user).first()

    # Get comments for this submission
    comments = Comment.objects.filter(submission=submission).order_by('-created_at')

    # Get previous and next submissions for navigation
    # Use the same filters as the gallery view
    submissions_query = Submission.objects.filter(
        contest=submission.contest,
        is_approved=True
    ).order_by('id')  # Order by ID for consistent navigation

    # Get the IDs of the previous and next submissions
    prev_submission = submissions_query.filter(id__lt=submission.id).order_by('-id').first()
    next_submission = submissions_query.filter(id__gt=submission.id).order_by('id').first()

    # Handle comment form submission
    if request.method == 'POST' and request.user.is_authenticated:
        comment_form = CommentForm(request.POST)
        if comment_form.is_valid():
            comment = comment_form.save(commit=False)
            comment.submission = submission
            comment.user = request.user
            comment.save()
            messages.success(request, "Your comment has been added.")
            return redirect('contest:submission_detail', slug=submission.slug)
    else:
        comment_form = CommentForm()

    context = {
        'submission': submission,
        'user_liked': user_liked,
        'jury_score': jury_score,
        'comments': comments,
        'comment_form': comment_form,
        'prev_submission': prev_submission,
        'next_submission': next_submission,
    }
    return render(request, 'contest/submission_detail.html', context)


def submission_gallery(request, slug):
    """
    Display a gallery of approved submissions for a contest.
    """
    contest = get_object_or_404(Contest, slug=slug)
    categories = contest.categories.all()
    book_parts = Product.objects.filter(
        Q(category__name__icontains='book') |
        Q(category__name__icontains='ebook') |
        Q(category__name__icontains='audiobook')
    ).exclude(book_part='other').distinct()

    # Get filter parameters
    category = request.GET.get('category')
    book_part_id = request.GET.get('book_part')
    search_query = request.GET.get('search')
    sort_by = request.GET.get('sort', 'likes')
    ai_filter = request.GET.get('ai_used')

    # Start with all approved submissions for this contest
    submissions = Submission.objects.filter(contest=contest, is_approved=True)

    # Apply filters
    if category:
        category_obj = get_object_or_404(Category, contest=contest, name=category)
        submissions = submissions.filter(category=category_obj)

    if book_part_id:
        submissions = submissions.filter(book_part_id=book_part_id)
        current_book_part = get_object_or_404(Product, id=book_part_id).name
    else:
        current_book_part = None

    # Filter by AI usage if specified
    if ai_filter:
        if ai_filter == 'yes':
            submissions = submissions.filter(ai_tools_used=True)
        elif ai_filter == 'no':
            submissions = submissions.filter(ai_tools_used=False)

    if search_query:
        submissions = submissions.filter(
            Q(title__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(user__username__icontains=search_query)
        )

    if ai_filter:
        submissions = submissions.filter(ai_tools_used=ai_filter == 'true')

    # Sort submissions
    if sort_by == 'likes':
        submissions = submissions.annotate(like_count=Count('likes')).order_by('-like_count')
    elif sort_by == 'date':
        submissions = submissions.order_by('-submitted_at')

    # Paginate results
    paginator = Paginator(submissions, 12)  # 12 submissions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'contest': contest,
        'categories': categories,
        'book_parts': book_parts,
        'current_category': category,
        'current_book_part': current_book_part,
        'current_book_part_id': book_part_id,
        'search_query': search_query,
        'sort_by': sort_by,
        'ai_filter': ai_filter,
        'page_obj': page_obj,
    }
    return render(request, 'contest/submission_gallery.html', context)


@login_required
def submission_like_pk(request, pk):
    """
    Like a submission using primary key.
    """
    submission = get_object_or_404(Submission, pk=pk)
    return submission_like_view(request, submission)


@login_required
def submission_like(request, slug):
    """
    Like a submission using slug.
    """
    submission = get_object_or_404(Submission, slug=slug)
    return submission_like_view(request, submission)


@login_required
def submission_like_view(request, submission):

    # Check if submission is approved
    if not submission.is_approved:
        return JsonResponse({'error': 'This submission is not available for voting.'}, status=400)

    # Check if user has already liked this submission
    like, created = Like.objects.get_or_create(submission=submission, user=request.user)

    if created:
        # Check if user has given at least 3 likes to be eligible for the grand prize
        user_likes_count = Like.objects.filter(user=request.user).count()

        # Prepare response data
        response_data = {
            'success': True,
            'likes_count': submission.total_likes,
            'user_likes_count': user_likes_count,
            'eligible': user_likes_count >= 3,
            'likes_needed': max(0, 3 - user_likes_count)
        }

        if user_likes_count >= 3:
            messages.success(
                request,
                "You've now liked enough submissions to be eligible for the grand prize!"
            )
            response_data['message'] = 'Thank you for your vote! You are eligible for the grand prize.'
        else:
            likes_needed = 3 - user_likes_count
            messages.info(
                request,
                f"Thank you for your vote! You need {likes_needed} more likes to be eligible for the grand prize."
            )
            response_data['message'] = f'Thank you for your vote! You need {likes_needed} more likes to be eligible.'

        return JsonResponse(response_data)
    else:
        return JsonResponse({
            'success': False,
            'likes_count': submission.total_likes,
            'message': 'You have already liked this submission.'
        })


@login_required
def submission_unlike_pk(request, pk):
    """
    Unlike a submission using primary key.
    """
    submission = get_object_or_404(Submission, pk=pk)
    return submission_unlike_view(request, submission)


@login_required
def submission_unlike(request, slug):
    """
    Unlike a submission using slug.
    """
    submission = get_object_or_404(Submission, slug=slug)
    return submission_unlike_view(request, submission)


@login_required
def submission_unlike_view(request, submission):

    # Try to find and delete the like
    try:
        like = Like.objects.get(submission=submission, user=request.user)
        like.delete()

        # Get updated like count for the user
        user_likes_count = Like.objects.filter(user=request.user).count()
        eligible = user_likes_count >= 3

        # Prepare response data
        response_data = {
            'success': True,
            'likes_count': submission.total_likes,
            'user_likes_count': user_likes_count,
            'eligible': eligible,
            'likes_needed': max(0, 3 - user_likes_count)
        }

        if eligible:
            response_data['message'] = 'Your vote has been removed. You are still eligible for the grand prize.'
        else:
            likes_needed = 3 - user_likes_count
            response_data['message'] = f'Your vote has been removed. You need {likes_needed} more likes to be eligible.'

            # Add a message to inform the user about their eligibility status
            messages.info(
                request,
                f"You now have {user_likes_count} likes. You need {likes_needed} more to be eligible for the grand prize."
            )

        return JsonResponse(response_data)
    except Like.DoesNotExist:
        return JsonResponse({
            'success': False,
            'likes_count': submission.total_likes,
            'message': 'You have not liked this submission.'
        })


@login_required
def jury_dashboard(request, slug):
    """
    Dashboard for jury members to score submissions.
    """
    # Check if user is a jury member (you'll need to implement this check)
    # For now, we'll assume all staff users are jury members
    if not request.user.is_staff:
        messages.error(request, "You don't have permission to access the jury dashboard.")
        return redirect('contest:contest_detail', slug=slug)

    contest = get_object_or_404(Contest, slug=slug)
    categories = contest.categories.all()

    # Get all approved submissions for this contest
    submissions = Submission.objects.filter(contest=contest, is_approved=True)

    # Filter by category if provided
    category = request.GET.get('category')
    if category:
        submissions = submissions.filter(category__name=category)

    # Get submissions that this jury member has already scored
    scored_submissions = JuryScore.objects.filter(
        judge=request.user,
        submission__contest=contest
    ).values_list('submission_id', flat=True)

    # Separate scored and unscored submissions
    unscored_submissions = submissions.exclude(id__in=scored_submissions)
    scored_submissions = submissions.filter(id__in=scored_submissions)

    context = {
        'contest': contest,
        'categories': categories,
        'unscored_submissions': unscored_submissions,
        'scored_submissions': scored_submissions,
        'current_category': category,
    }
    return render(request, 'contest/jury_dashboard.html', context)


@login_required
def jury_score_submission_pk(request, pk):
    """
    Score a submission as a jury member using primary key.
    """
    # Check if user is a jury member
    if not request.user.is_staff:
        messages.error(request, "You don't have permission to score submissions.")
        return redirect('contest:submission_detail', pk=pk)

    submission = get_object_or_404(Submission, pk=pk)
    return jury_score_submission_view(request, submission)


@login_required
def jury_score_submission(request, slug):
    """
    Score a submission as a jury member using slug.
    """
    # Check if user is a jury member
    if not request.user.is_staff:
        messages.error(request, "You don't have permission to score submissions.")
        return redirect('contest:submission_detail', slug=slug)

    submission = get_object_or_404(Submission, slug=slug)
    return jury_score_submission_view(request, submission)


@login_required
def jury_score_submission_view(request, submission):

    # Check if submission is approved
    if not submission.is_approved:
        messages.error(request, "This submission is not available for scoring.")
        return redirect('contest:jury_dashboard', slug=submission.contest.slug)

    # Check if jury member has already scored this submission
    jury_score = JuryScore.objects.filter(submission=submission, judge=request.user).first()

    if request.method == 'POST':
        form = JuryScoreForm(request.POST, instance=jury_score)
        if form.is_valid():
            score = form.save(commit=False)
            score.submission = submission
            score.judge = request.user
            score.save()

            messages.success(request, "Your score has been recorded.")
            return redirect('contest:jury_dashboard', slug=submission.contest.slug)
    else:
        form = JuryScoreForm(instance=jury_score)

    context = {
        'form': form,
        'submission': submission,
    }
    return render(request, 'contest/jury_score_form.html', context)


def leaderboard(request, slug):
    """
    Display a leaderboard of submissions for a contest.
    """
    contest = get_object_or_404(Contest, slug=slug)
    categories = contest.categories.all()

    # Get filter parameters
    category = request.GET.get('category')

    # Start with all approved submissions for this contest
    submissions = Submission.objects.filter(contest=contest, is_approved=True)

    # Apply category filter if provided
    if category:
        category_obj = get_object_or_404(Category, contest=contest, name=category)
        submissions = submissions.filter(category=category_obj)

    # Get top submissions by likes
    top_liked = submissions.annotate(
        like_count=Count('likes')
    ).order_by('-like_count')[:10]

    # Get top submissions by jury score
    top_jury = submissions.annotate(
        jury_score=Sum('jury_scores__points')
    ).filter(jury_score__isnull=False).order_by('-jury_score')[:10]

    # Get most commented submissions
    most_commented = submissions.annotate(
        comment_count=Count('comments')
    ).order_by('-comment_count')[:10]

    context = {
        'contest': contest,
        'categories': categories,
        'current_category': category,
        'top_liked': top_liked,
        'top_jury': top_jury,
        'most_commented': most_commented,
    }
    return render(request, 'contest/leaderboard.html', context)


@login_required
def check_existing_review(request):
    """
    Check if the user has already reviewed a book part.
    Returns the existing review data if found.
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Only GET requests are allowed'}, status=400)

    book_part_id = request.GET.get('book_part_id')
    if not book_part_id:
        return JsonResponse({'error': 'Book part ID is required'}, status=400)

    try:
        book_part = Product.objects.get(id=book_part_id)

        # Get all related products (different formats of the same book part)
        related_products = book_part.get_related_products()
        related_product_ids = [p.id for p in related_products]
        related_product_ids.append(int(book_part_id))

        # Check if user already has a review for this book part or any related product
        existing_review = Review.objects.filter(
            product_id__in=related_product_ids,
            user=request.user
        ).first()

        if existing_review:
            # Parse the review content to extract book fragment and review text
            review_content = existing_review.comment

            # Try to extract book fragment (text between quotes)
            book_fragment = ""
            review_text = review_content

            # Look for text between quotes
            import re
            quote_match = re.search(r'"([^"]*)"', review_content)
            if quote_match:
                book_fragment = quote_match.group(1)
                # Remove the book fragment from the review text
                parts = review_content.split(f'"{book_fragment}"')
                if len(parts) >= 1:
                    review_text = parts[0].strip()

            return JsonResponse({
                'has_review': True,
                'rating': existing_review.rating,
                'review_text': review_text,
                'book_fragment': book_fragment,
                'review_id': existing_review.id
            })
        else:
            return JsonResponse({'has_review': False})

    except Product.DoesNotExist:
        return JsonResponse({'error': 'Book part not found'}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


def contest_results(request, slug):
    """
    Display the results of a contest.
    """
    contest = get_object_or_404(Contest, slug=slug)

    # Check if results have been announced
    results_announced = contest.results_date and contest.results_date <= timezone.now().date()

    # Add a flag to the contest object for template use
    contest.results_announced = results_announced

    # If results have been announced, get the winners
    grand_winner = None
    category_winners = []
    honorable_mentions = []

    if results_announced:
        # Get the grand prize winner (highest combined score)
        grand_winner = Submission.objects.filter(
            contest=contest,
            is_approved=True
        ).annotate(
            like_count=Count('likes'),
            jury_score=Sum('jury_scores__points')
        ).filter(jury_score__isnull=False).order_by('-jury_score', '-like_count').first()

        # Get category winners (highest score in each category)
        for category in contest.categories.all():
            category_winner = Submission.objects.filter(
                contest=contest,
                category=category,
                is_approved=True
            ).annotate(
                like_count=Count('likes'),
                jury_score=Sum('jury_scores__points')
            ).filter(jury_score__isnull=False).order_by('-jury_score', '-like_count').first()

            if category_winner and category_winner != grand_winner:
                category_winners.append(category_winner)

        # Get honorable mentions (top 8 by likes, excluding winners)
        excluded_ids = [s.id for s in [grand_winner] + category_winners if s]
        honorable_mentions = Submission.objects.filter(
            contest=contest,
            is_approved=True
        ).exclude(
            id__in=excluded_ids
        ).annotate(
            like_count=Count('likes')
        ).order_by('-like_count')[:8]

    context = {
        'contest': contest,
        'grand_winner': grand_winner,
        'category_winners': category_winners,
        'honorable_mentions': honorable_mentions,
    }

    return render(request, 'contest/contest_results.html', context)
