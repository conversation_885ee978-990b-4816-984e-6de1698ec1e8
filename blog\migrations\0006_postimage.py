# Generated by Django 5.1.5 on 2025-04-01 15:31

import blog.models
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('blog', '0005_category_post_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='PostImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(help_text='Image to be embedded in the blog post content', upload_to='blog/content_images/%Y/%m/')),
                ('caption', models.CharField(blank=True, help_text='Caption displayed below the image', max_length=255)),
                ('alt_text', models.CharField(help_text='Alternative text for accessibility (required)', max_length=255, validators=[blog.models.validate_not_empty])),
                ('width', models.PositiveIntegerField(default=800, help_text='Display width in pixels')),
                ('alignment', models.CharField(choices=[('left', 'Left'), ('center', 'Center'), ('right', 'Right')], default='center', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('post', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='content_images', to='blog.post')),
            ],
            options={
                'verbose_name': 'Post Image',
                'verbose_name_plural': 'Post Images',
                'ordering': ['-created_at'],
            },
        ),
    ]
