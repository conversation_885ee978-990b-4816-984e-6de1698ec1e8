# Generated by Django 5.1.5 on 2025-03-13 16:49

from django.db import migrations


def remove_column_if_exists(apps, schema_editor):
    # Get the database connection
    connection = schema_editor.connection
    
    # Check if the column exists
    cursor = connection.cursor()
    
    # Different SQL for different database backends
    if connection.vendor == 'sqlite':
        # For SQLite
        cursor.execute(
            "PRAGMA table_info(works_product);"
        )
        columns = [column[1] for column in cursor.fetchall()]
        if 'download_link_expiry_days' in columns:
            # SQLite doesn't support DROP COLUMN directly in older versions
            # We need to create a new table and copy data
            select_columns = (
                "id, category_id, name, description, price, "
                "created_date, updated_date, image, image_url, "
                "language_id, ebook_file, audiobook_link, "
                "audiobook_sample_link, pages, duration, excerpt"
            )
            create_sql = f"CREATE TABLE works_product_new AS SELECT {select_columns} FROM works_product;"
            cursor.execute(create_sql)
            cursor.execute("DROP TABLE works_product;")
            cursor.execute("ALTER TABLE works_product_new RENAME TO works_product;")
    else:
        # For PostgreSQL and other databases
        if connection.vendor == 'postgresql':
            cursor.execute(
                "SELECT column_name FROM information_schema.columns "
                "WHERE table_name='works_product' "
                "AND column_name='download_link_expiry_days';"
            )
            if cursor.fetchone():
                cursor.execute(
                    "ALTER TABLE works_product "
                    "DROP COLUMN download_link_expiry_days;"
                )
        else:
            # For other databases, try a simple ALTER TABLE
            try:
                cursor.execute(
                    "ALTER TABLE works_product "
                    "DROP COLUMN download_link_expiry_days;"
                )
            except Exception as e:
                print(f"Could not drop column: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('works', '0012_product_duration_alter_product_pages'),
    ]

    operations = [
        migrations.RunPython(
            remove_column_if_exists,
            reverse_code=migrations.RunPython.noop
        ),
    ]
