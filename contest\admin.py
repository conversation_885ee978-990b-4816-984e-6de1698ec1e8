from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count
from .models import Contest, Category, Submission, Like, JuryScore, Comment, BookAccessRequest


class CategoryInline(admin.TabularInline):
    model = Category
    extra = 1


@admin.register(Contest)
class ContestAdmin(admin.ModelAdmin):
    list_display = ('name', 'start_date', 'end_date', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    prepopulated_fields = {'slug': ('name',)}
    inlines = [CategoryInline]


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'contest')
    list_filter = ('contest',)
    search_fields = ('name', 'description')


class LikeInline(admin.TabularInline):
    model = Like
    extra = 0
    readonly_fields = ('user', 'created_at')
    can_delete = False


class JuryScoreInline(admin.TabularInline):
    model = JuryScore
    extra = 0
    readonly_fields = ('judge', 'points', 'created_at')
    can_delete = False


class CommentInline(admin.TabularInline):
    model = Comment
    extra = 0
    readonly_fields = ('user', 'created_at')
    can_delete = True


@admin.register(Submission)
class SubmissionAdmin(admin.ModelAdmin):
    list_display = (
        'title', 'user', 'category', 'contest', 'image_thumbnail',
        'submitted_at', 'is_approved', 'total_likes', 'total_jury_score'
    )
    list_filter = ('contest', 'category', 'is_approved', 'ai_tools_used')
    search_fields = ('title', 'description', 'user__username')
    readonly_fields = ('submitted_at', 'total_likes', 'total_jury_score', 'image_preview')
    inlines = [LikeInline, JuryScoreInline, CommentInline]
    actions = ['approve_submissions', 'unapprove_submissions', 'export_selected_submissions']
    fieldsets = (
        ('Basic Information', {
            'fields': ('title', 'description', 'user', 'contest', 'category', 'is_approved')
        }),
        ('Submission Details', {
            'fields': ('file', 'image_preview', 'book_part', 'rating', 'review_text',
                      'book_fragment', 'chapter_reference', 'ai_tools_used', 'ai_tools_details')
        }),
        ('Statistics', {
            'fields': ('submitted_at', 'total_likes', 'total_jury_score')
        }),
    )

    def image_preview(self, obj):
        """Display a preview of the image in the admin detail view."""
        if obj.file and hasattr(obj.file, 'url'):
            return format_html(
                '<a href="{}" target="_blank"><img src="{}" style="max-height: 300px; max-width: 100%;" /></a>',
                obj.file.url, obj.file.url
            )
        return "No image"
    image_preview.short_description = 'Image Preview'

    def image_thumbnail(self, obj):
        """Display a thumbnail of the image in the admin list view."""
        if obj.file and hasattr(obj.file, 'url'):
            return format_html(
                '<img src="{}" style="height: 50px;" />',
                obj.file.url
            )
        return "No image"
    image_thumbnail.short_description = 'Thumbnail'

    def approve_submissions(self, request, queryset):
        """Approve selected submissions."""
        updated = queryset.update(is_approved=True)
        self.message_user(
            request,
            f"{updated} submission{'s' if updated != 1 else ''} successfully approved."
        )
    approve_submissions.short_description = "Approve selected submissions"

    def unapprove_submissions(self, request, queryset):
        """Unapprove selected submissions."""
        updated = queryset.update(is_approved=False)
        self.message_user(
            request,
            f"{updated} submission{'s' if updated != 1 else ''} marked as not approved."
        )
    unapprove_submissions.short_description = "Unapprove selected submissions"

    def export_selected_submissions(self, request, queryset):
        """Export selected submissions to CSV."""
        import csv
        from django.http import HttpResponse
        from django.utils import timezone

        # Create the HttpResponse object with CSV header
        response = HttpResponse(content_type='text/csv')
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="contest_submissions_{timestamp}.csv"'

        # Create CSV writer
        writer = csv.writer(response)
        writer.writerow([
            'ID', 'Title', 'User', 'Email', 'Category', 'Contest',
            'Submitted At', 'Approved', 'Likes', 'Jury Score',
            'Book Part', 'Chapter', 'Rating', 'Review Text'
        ])

        # Add submission data
        for submission in queryset:
            writer.writerow([
                submission.id,
                submission.title,
                submission.user.username,
                submission.user.email,
                submission.category.name if submission.category else '',
                submission.contest.name if submission.contest else '',
                submission.submitted_at.strftime('%Y-%m-%d %H:%M:%S'),
                'Yes' if submission.is_approved else 'No',
                submission.total_likes,
                submission.total_jury_score,
                submission.book_part.name if submission.book_part else '',
                submission.chapter_reference,
                submission.rating,
                submission.review_text[:100] + '...' if len(submission.review_text) > 100 else submission.review_text
            ])

        return response
    export_selected_submissions.short_description = "Export selected submissions to CSV"


@admin.register(Like)
class LikeAdmin(admin.ModelAdmin):
    list_display = ('user', 'submission', 'created_at', 'user_like_count')
    list_filter = ('submission__contest', 'user')
    search_fields = ('user__username', 'submission__title')
    readonly_fields = ('created_at', 'user_like_count')

    def user_like_count(self, obj):
        """Display the total number of likes given by this user."""
        user_likes = Like.objects.filter(user=obj.user).count()
        eligible = user_likes >= 3
        return format_html(
            '{} <span style="color: {};">({})</span>',
            user_likes,
            'green' if eligible else 'red',
            'Eligible' if eligible else 'Not eligible'
        )
    user_like_count.short_description = 'Total Likes Given (Eligibility)'

    def get_queryset(self, request):
        """Add annotation for user_like_count to make it sortable."""
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            _user_like_count=Count('user__contest_likes', distinct=True)
        )
        return queryset

    def get_list_display(self, request):
        """Dynamically add user_like_count to list_display."""
        list_display = list(super().get_list_display(request))
        if 'user_like_count' not in list_display:
            list_display.append('user_like_count')
        return list_display


@admin.register(JuryScore)
class JuryScoreAdmin(admin.ModelAdmin):
    list_display = ('judge', 'submission', 'points', 'created_at')
    list_filter = ('submission__contest',)
    search_fields = ('judge__username', 'submission__title')
    readonly_fields = ('created_at',)


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('user', 'submission', 'content_preview', 'created_at')
    list_filter = ('submission__contest',)
    search_fields = ('user__username', 'submission__title', 'content')
    readonly_fields = ('created_at',)

    def content_preview(self, obj):
        return obj.content[:50] + '...' if len(obj.content) > 50 else obj.content
    content_preview.short_description = 'Content'


# Create a custom admin view for user eligibility
class UserEligibilityAdmin(admin.ModelAdmin):
    """
    Admin view to show which users are eligible for the grand prize based on their like count.
    """
    change_list_template = 'admin/contest/user_eligibility_change_list.html'

    def get_model_perms(self, request):
        """
        Return empty perms dict to hide from admin index.
        """
        return {}

    def changelist_view(self, request, extra_context=None):
        """
        Custom view to show user eligibility.
        """
        # Get all users who have liked at least one submission
        from django.contrib.auth.models import User
        from django.db.models import Count

        users_with_likes = User.objects.filter(
            contest_likes__isnull=False
        ).annotate(
            like_count=Count('contest_likes')
        ).order_by('-like_count')

        # Determine eligibility
        eligible_users = []
        ineligible_users = []

        for user in users_with_likes:
            if user.like_count >= 3:
                eligible_users.append({
                    'username': user.username,
                    'like_count': user.like_count,
                    'submission_count': Submission.objects.filter(user=user).count(),
                    'email': user.email
                })
            else:
                ineligible_users.append({
                    'username': user.username,
                    'like_count': user.like_count,
                    'submission_count': Submission.objects.filter(user=user).count(),
                    'email': user.email
                })

        # Add to context
        context = {
            'title': 'User Eligibility for Grand Prize',
            'eligible_users': eligible_users,
            'ineligible_users': ineligible_users,
            'total_eligible': len(eligible_users),
            'total_ineligible': len(ineligible_users),
            'total_users': len(eligible_users) + len(ineligible_users),
        }

        return super().changelist_view(request, extra_context=context)


# For the custom view, we'll need to add a URL pattern in urls.py


@admin.register(BookAccessRequest)
class BookAccessRequestAdmin(admin.ModelAdmin):
    list_display = ('user', 'book_part', 'format_type', 'access_type', 'status', 'access_granted', 'created_at')
    list_filter = ('contest', 'format_type', 'access_type', 'status', 'access_granted')
    search_fields = ('user__username', 'book_part__name', 'reason')
    readonly_fields = ('created_at', 'updated_at')
    actions = ['approve_requests', 'reject_requests', 'grant_access', 'create_orders_for_free_requests']
    fieldsets = (
        ('Request Information', {
            'fields': ('user', 'contest', 'book_part', 'category', 'format_type', 'access_type')
        }),
        ('Status', {
            'fields': ('status', 'access_granted')
        }),
        ('Details', {
            'fields': ('reason', 'created_at', 'updated_at')
        }),
    )

    def approve_requests(self, request, queryset):
        queryset.update(status='approved')
        self.message_user(request, f"{queryset.count()} requests have been approved.")
    approve_requests.short_description = "Mark selected requests as approved"

    def reject_requests(self, request, queryset):
        queryset.update(status='rejected')
        self.message_user(request, f"{queryset.count()} requests have been rejected.")
    reject_requests.short_description = "Mark selected requests as rejected"

    def grant_access(self, request, queryset):
        """
        Grant access to selected requests and create orders for free requests.
        This is a combined action that:
        1. Approves the requests
        2. Marks access as granted
        3. Creates orders for free requests
        """
        from checkout.models import Order, OrderItem

        # Update all requests to approved and granted
        queryset.update(access_granted=True, status='approved')

        # Process free requests to create orders
        free_requests = queryset.filter(access_type='free')
        orders_created = 0

        for access_request in free_requests:
            # Check if an order already exists for this user and book part
            existing_order = OrderItem.objects.filter(
                order__user=access_request.user,
                product=access_request.book_part
            ).exists()

            if not existing_order:
                # Create a new order
                order = Order.objects.create(
                    user=access_request.user,
                    email=access_request.user.email,
                    full_name=access_request.user.get_full_name() or access_request.user.username,
                    # order_date is auto-populated
                    payment_status='paid',  # Mark as paid to grant access
                    email_delivery_status='sent',  # Mark as sent since we'll send our own email
                    # Add required fields for the Order model
                    phone_number="N/A",  # Not needed for digital delivery
                    billing_address1="N/A",  # Not needed for digital delivery
                    billing_city="N/A",  # Not needed for digital delivery
                    billing_postcode="A1B2C3",  # Dummy value that passes validation
                    billing_country="US"  # Default country
                )

                # Create order item
                OrderItem.objects.create(
                    order=order,
                    product=access_request.book_part,
                    price=0.00  # Free access
                )

                orders_created += 1

        # Prepare success message
        message = f"Access granted for {queryset.count()} requests."
        if orders_created > 0:
            message += f" Created {orders_created} orders for free access requests. Users will receive email notifications."

        self.message_user(request, message)
    grant_access.short_description = "Grant access to selected requests (Complete Process)"

    def create_orders_for_free_requests(self, request, queryset):
        """
        Create orders for free access requests to make them appear in the user's library.
        This allows free access users to access content the same way as paid users.
        """
        from checkout.models import Order, OrderItem

        free_requests = queryset.filter(access_type='free', status='approved')
        orders_created = 0

        for access_request in free_requests:
            # Check if an order already exists for this user and book part
            existing_order = OrderItem.objects.filter(
                order__user=access_request.user,
                product=access_request.book_part
            ).exists()

            if not existing_order:
                # Create a new order
                order = Order.objects.create(
                    user=access_request.user,
                    email=access_request.user.email,
                    full_name=access_request.user.get_full_name() or access_request.user.username,
                    # order_date is auto-populated
                    payment_status='paid',  # Mark as paid to grant access
                    email_delivery_status='sent',  # Mark as sent since we'll send our own email
                    # Add required fields for the Order model
                    phone_number="N/A",  # Not needed for digital delivery
                    billing_address1="N/A",  # Not needed for digital delivery
                    billing_city="N/A",  # Not needed for digital delivery
                    billing_postcode="A1B2C3",  # Dummy value that passes validation
                    billing_country="US"  # Default country
                )

                # Create order item
                OrderItem.objects.create(
                    order=order,
                    product=access_request.book_part,
                    price=0.00  # Free access
                )

                # Mark access as granted
                access_request.access_granted = True
                access_request.save()

                orders_created += 1

        if orders_created > 0:
            self.message_user(
                request,
                f"Created {orders_created} orders for free access requests. Users can now access content in their library."
            )
        else:
            self.message_user(
                request,
                "No new orders created. Either no free requests were selected or orders already exist."
            )

    create_orders_for_free_requests.short_description = "Create orders for free access requests"
