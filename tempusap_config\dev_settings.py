"""
Development settings for Tempus Author Platform.

This module contains settings that are only used during development
to help with debugging and prevent caching issues.
"""

# Disable browser caching for static files during development
STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'

# Add cache busting for static files
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Set cache to dummy cache during development
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.dummy.DummyCache',
    }
}

# Set browser cache max age to 0 to prevent browser caching
STATIC_MAX_AGE = 0
