from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.utils.html import strip_tags
import logging
import os

# Set up logger
logger = logging.getLogger(__name__)

def send_confirmation_email(order):
    """
    Send the user a confirmation email with enhanced error handling.
    
    This function attempts to send an order confirmation email to the customer.
    If it fails in production, it logs the error. In development mode, it will
    print the email content to the console as a fallback.
    """
    customer_email = order.email
    subject = f'Tempus Author Platform - Order Confirmation #{order.id}'

    # Get order items for the email
    order_items = order.orderitem_set.all()

    # Prepare context for email template
    context = {
        'order': order,
        'contact_email': settings.CONTACT_DISPLAY_EMAIL,
        'order_items': order_items,
        'SITE_URL': settings.SITE_URL,
    }

    # Render email templates
    html_content = render_to_string(
        'checkout/confirmation_emails/confirmation_email_body.html',
        context
    )
    
    # Create a proper plain text version
    text_content = strip_tags(html_content).replace('&nbsp;', ' ')

    logger.info(f"Attempting to send confirmation email for order {order.id} to {customer_email}")
    
    try:
        # Create email message with both HTML and plain text
        email = EmailMultiAlternatives(
            subject,
            text_content,
            settings.DEFAULT_FROM_EMAIL,
            [customer_email]
        )
        
        # Explicitly set content type and attach HTML version
        email.content_subtype = "html"  # Set primary content to be HTML
        email.attach_alternative(html_content, "text/html")
        
        # Send the email
        email.send(fail_silently=False)
        
        logger.info(f"Confirmation email sent successfully for order {order.id}")
        return True
    except Exception as e:
        # Log the error details
        logger.error(f"Failed to send confirmation email for order {order.id}: {str(e)}")
        
        # In development, print the email content to console
        if 'DEVELOPMENT' in os.environ:
            logger.info("Development mode detected. Printing email content to console:")
            logger.info(f"Subject: {subject}")
            logger.info(f"To: {customer_email}")
            logger.info(f"From: {settings.DEFAULT_FROM_EMAIL}")
            logger.info(f"Body: {text_content[:500]}...")  # Print first 500 chars of body
        
        # Re-raise the exception to be handled by the view
        raise
