from django.shortcuts import render, get_object_or_404, redirect, reverse
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from .models import Product, Category, Review
from checkout.models import OrderItem
from .forms import ReviewForm


def all_works(request):
    """
    Display all products/works with optional category filtering.
    """
    works = Product.objects.all()
    categories = Category.objects.all()
    current_category = None

    if 'category' in request.GET:
        category_name = request.GET['category']
        works = works.filter(category__name=category_name)
        current_category = category_name

    context = {
        'works': works,
        'categories': categories,
        'current_category': current_category
    }

    return render(request, 'works/works.html', context)


def work_detail(request, work_id):
    """
    Display detailed information about a specific work.

    Shows work details, approved reviews from all related products,
    and handles user-specific display logic for reviews and purchases.
    """
    work = get_object_or_404(Product, pk=work_id)

    # Get reviews from all related products (same book part)
    reviews = work.get_all_reviews()
    review_form = ReviewForm()

    # Get related products for display
    related_products = work.get_related_products()
    # Remove self from the list for display purposes
    related_products = [p for p in related_products if p.id != work.id]

    user_has_reviewed = False
    user_has_approved_review = False
    user_has_purchased = False
    attachments = None

    if request.user.is_authenticated:
        # Check if user has reviewed any related product
        # Build a query to find reviews by this user for any related product
        related_product_ids = [p.id for p in related_products]
        related_product_ids.append(work.id)  # Include the current product

        user_review = Review.objects.filter(
            user=request.user,
            product_id__in=related_product_ids
        ).first()

        user_has_reviewed = user_review is not None
        user_has_approved_review = (
            user_review.approved if user_review else False
        )

    if request.user.is_authenticated:
        # Check if user has purchased the product or has a free book
        user_has_purchased = OrderItem.objects.filter(
            order__user=request.user,
            product=work,
            order__payment_status__in=['paid', 'free_book']
        ).exists()

        # Check if user has free access through contest
        user_has_free_access = False
        try:
            from contest.models import BookAccessRequest

            user_has_free_access = BookAccessRequest.objects.filter(
                user=request.user,
                book_part=work,
                status='approved',
                access_granted=True
            ).exists()

            if user_has_free_access:
                print(f"User has free access to product {work.id}")
                # Count free access as a purchase for display purposes
                user_has_purchased = True
        except Exception as e:
            # Log the error but continue
            print(f"Error checking free access: {str(e)}")

        # If user has purchased the work or has free access, get all attachments
        # Otherwise, only get promotional attachments
        if user_has_purchased:
            attachments = work.attachments.all()
        else:
            attachments = work.attachments.filter(is_promotional=True)

    context = {
        'work': work,
        'reviews': reviews,
        'review_form': review_form,
        'user_has_reviewed': user_has_reviewed,
        'user_has_approved_review': user_has_approved_review,
        'user_has_purchased': user_has_purchased,
        'attachments': attachments,
        'related_products': related_products,
    }

    return render(request, 'works/work_detail.html', context)


def view_bookcart(request):
    """
    Display the contents of the user's book cart.
    """
    return render(request, 'works/bookcart.html')


def add_to_bookcart(request, work_id):
    """
    Add a work to the book cart.
    Each work can only be added once.
    Redirects to the bookcart page after adding the item.
    """
    work = get_object_or_404(Product, pk=work_id)
    bookcart = request.session.get('bookcart', {})

    if work_id in bookcart:
        messages.info(
            request,
            f'{work.name} is already in your bookcart.'
        )
        return redirect(reverse('view_bookcart'))
    else:
        bookcart[work_id] = True
        messages.success(request, f'Added {work.name} to your bookcart')

    request.session['bookcart'] = bookcart
    # Redirect to bookcart instead of back to the product page
    return redirect(reverse('view_bookcart'))


def adjust_bookcart(request, work_id):
    """
    Remove a work from the book cart.
    """
    bookcart = request.session.get('bookcart', {})
    bookcart.pop(work_id)
    request.session['bookcart'] = bookcart
    return redirect(reverse('view_bookcart'))


def remove_from_bookcart(request, work_id):
    """
    Remove a work from the book cart.
    Handles cases where the item might not exist in the cart.
    """
    try:
        bookcart = request.session.get('bookcart', {})
        work_id = str(work_id)

        if work_id in bookcart:
            bookcart.pop(work_id)
            request.session['bookcart'] = bookcart
            messages.success(request, 'Item removed from the bookcart')
        else:
            messages.info(request, 'This item was not in your bookcart')

        return redirect(reverse('view_bookcart'))

    except Exception as e:
        messages.error(request, f'Error removing item from bookcart: {e}')
        return redirect(reverse('view_bookcart'))


@login_required
def add_review(request, work_id):
    """
    Add a review for a purchased work or work with free access.

    Only allows reviews from users who have purchased the work or have free access.
    Reviews require approval before being displayed.
    Prevents duplicate reviews for different formats of the same book part.
    """
    work = get_object_or_404(Product, id=work_id)

    # Get all related products (different formats of the same book part)
    related_products = work.get_related_products()
    related_product_ids = [p.id for p in related_products]

    # Check if user already has a review for any related product
    existing_review = Review.objects.filter(
        user=request.user,
        product_id__in=related_product_ids
    ).first()

    if existing_review:
        messages.info(
            request,
            'You already have a review for this book part. '
            'You can edit your existing review instead.'
        )
        # Redirect to the product that has the existing review
        return redirect('work_detail', work_id=existing_review.product.id)

    # Check if user has purchased the product or any related product or has a free book
    has_purchased = OrderItem.objects.filter(
        order__user=request.user,
        product_id__in=related_product_ids,
        order__payment_status__in=['paid', 'free_book']
    ).exists()

    # Check if user has free access through contest for this product or any related product
    has_free_access = False
    try:
        from contest.models import BookAccessRequest

        has_free_access = BookAccessRequest.objects.filter(
            user=request.user,
            book_part_id__in=related_product_ids,
            status='approved',
            access_granted=True
        ).exists()
    except Exception as e:
        # Log the error but continue
        print(f"Error checking free access for review: {str(e)}")

    # User can review if they purchased or have free access
    has_access = has_purchased or has_free_access

    if not has_access:
        messages.error(
            request,
            'You can only review books that you have purchased or have access to.'
        )
        return redirect('work_detail', work_id)

    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.product = work
            review.user = request.user
            review.save()

            if not form.cleaned_data.get('comment'):
                messages.info(
                    request,
                    'Thanks for your rating! Feel free to come back'
                    ' and add a written review anytime - '
                    'your experience could help other readers make'
                    ' their decision.'
                )
            else:
                messages.success(
                    request, 'Thank you for your detailed review! It has'
                    ' been submitted for approval.'
                )
    return redirect('work_detail', work_id)


# @login_required  # Removed to allow sample access for non-authenticated users
def audio_player(request, product_id):
    """
    Display an audio player for purchased audiobooks or sample previews.
    Also allows access for users with approved free access requests.
    """
    product = get_object_or_404(Product, pk=product_id)

    # Check if this is a sample preview request
    is_sample = request.GET.get('sample', 'false').lower() == 'true'

    # If it's a sample preview, no purchase check is needed
    if is_sample and product.audiobook_sample_link:
        return render(request, 'works/audio_player.html', {
            'product': product,
        })

    # For full audiobook, check if user has purchased the product or has free access
    user_has_access = False

    if request.user.is_authenticated:
        try:
            # Check if the user has purchased this product or has a free book
            user_has_purchased = OrderItem.objects.filter(
                order__user=request.user,
                product=product,
                order__payment_status__in=['paid', 'free_book']
            ).exists()

            if user_has_purchased:
                user_has_access = True
                print(f"User has purchased or has free book access to product {product.id}")
            else:
                # Check if user has free access through contest
                try:
                    from contest.models import BookAccessRequest

                    user_has_free_access = BookAccessRequest.objects.filter(
                        user=request.user,
                        book_part=product,
                        status='approved',
                        access_granted=True
                    ).exists()

                    if user_has_free_access:
                        user_has_access = True
                        print(f"User has free access to product {product.id}")
                except Exception as e:
                    # Log the error but continue
                    print(f"Error checking free access: {str(e)}")

        except Exception as e:
            # Log the specific error
            print(f"Error checking purchases: {e}")

    # If user is not authenticated or doesn't have access
    if not user_has_access:
        from django.shortcuts import redirect

        messages.error(
            request,
            'You must purchase this product or have approved free access to view the audiobook.'
        )
        return redirect('work_detail', work_id=product_id)

    return render(request, 'works/audio_player.html', {
        'product': product,
    })
