import logging
from django.shortcuts import get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from .models import Product, ProductAttachment
from .s3_utils import get_audiobook_s3_url

logger = logging.getLogger(__name__)


@login_required
def audiobook_url_example(request, product_id):
    """
    Example view that retrieves and logs the S3 URL for an audiobook file.
    
    This view demonstrates how to:
    1. Retrieve a product with an audiobook attachment
    2. Get the S3 URL for the audiobook file
    3. Log the URL for debugging purposes
    4. Return the URL in a response
    
    Args:
        request: The HTTP request
        product_id: The ID of the product containing the audiobook
        
    Returns:
        JsonResponse with the audiobook URL or an error message
    """
    # Get the product
    product = get_object_or_404(Product, pk=product_id)
    
    # Log product information
    logger.info(
        f"Retrieving audiobook URL for product: {product.name} "
        f"(ID: {product_id})"
    )
    
    # Find audiobook attachments for this product
    audiobook_attachments = ProductAttachment.objects.filter(
        product=product,
        name__icontains='audiobook',
        attachment_type='file'
    )
    
    if not audiobook_attachments.exists():
        logger.warning(
            f"No audiobook attachments found for product {product_id}"
        )
        return JsonResponse({
            'success': False,
            'error': 'No audiobook attachments found for this product'
        })
    
    # Get the first audiobook attachment
    audiobook = audiobook_attachments.first()
    
    # Get the S3 URL for the audiobook file
    audiobook_url = get_audiobook_s3_url(audiobook.file)
    
    if not audiobook_url:
        return JsonResponse({
            'success': False,
            'error': 'Could not generate audiobook URL'
        })
    
    # Return the URL in the response
    return JsonResponse({
        'success': True,
        'product_name': product.name,
        'audiobook_name': audiobook.name,
        'audiobook_url': audiobook_url
    })


@login_required
def audiobook_direct_file_example(request, product_id):
    """
    Alternative example for products that have a direct audiobook file field.
    
    This example assumes you've added a FileField directly to the Product model
    for storing audiobook files, rather than using the ProductAttachment model.
    
    Args:
        request: The HTTP request
        product_id: The ID of the product containing the audiobook
        
    Returns:
        JsonResponse with the audiobook URL or an error message
    """
    # Get the product
    product = get_object_or_404(Product, pk=product_id)
    
    # For this example, let's assume we've added an 'audiobook_file' field:
    # audiobook_file = models.FileField(
    #     upload_to='audiobooks/', 
    #     null=True, 
    #     blank=True
    # )
    
    # Check if the product has an audiobook file
    if not hasattr(product, 'audiobook_file') or not product.audiobook_file:
        logger.warning(f"No audiobook file found for product {product_id}")
        return JsonResponse({
            'success': False,
            'error': 'No audiobook file found for this product'
        })
    
    # Get the S3 URL for the audiobook file
    audiobook_url = get_audiobook_s3_url(product.audiobook_file)
    
    if not audiobook_url:
        return JsonResponse({
            'success': False,
            'error': 'Could not generate audiobook URL'
        })
    
    # Return the URL in the response
    return JsonResponse({
        'success': True,
        'product_name': product.name,
        'audiobook_url': audiobook_url
    }) 