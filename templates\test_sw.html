{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container mt-5">
    <h1>Service Worker Test Page</h1>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Service Worker Status</h2>
        </div>
        <div class="card-body">
            <div id="sw-status">Checking service worker status...</div>
            <div id="sw-scope"></div>
            <div id="sw-cache"></div>
            
            <div class="mt-3">
                <button id="check-sw" class="btn btn-primary">Check Service Worker</button>
                <button id="unregister-sw" class="btn btn-danger">Unregister Service Worker</button>
            </div>
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h2>Audio Test</h2>
        </div>
        <div class="card-body">
            <p>Test audio playback and caching:</p>
            <audio id="test-audio" controls>
                <source src="https://cdn.freesound.org/previews/328/328857_230356-lq.mp3" type="audio/mpeg">
                Your browser does not support the audio element.
            </audio>
            
            <div class="mt-3">
                <button id="reload-audio" class="btn btn-secondary">Reload Audio</button>
            </div>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header">
            <h2>Console Output</h2>
        </div>
        <div class="card-body">
            <pre id="console-output" style="max-height: 300px; overflow-y: auto;"></pre>
        </div>
    </div>
</div>

<script>
    // Override console.log to display in the page
    const originalConsoleLog = console.log;
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    
    function addToConsoleOutput(message, type = 'log') {
        const consoleOutput = document.getElementById('console-output');
        const timestamp = new Date().toLocaleTimeString();
        let color = 'black';
        
        if (type === 'error') color = 'red';
        if (type === 'warn') color = 'orange';
        
        // Convert objects to strings
        if (typeof message === 'object') {
            message = JSON.stringify(message, null, 2);
        }
        
        consoleOutput.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span>\n`;
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }
    
    console.log = function() {
        originalConsoleLog.apply(console, arguments);
        Array.from(arguments).forEach(arg => addToConsoleOutput(arg, 'log'));
    };
    
    console.error = function() {
        originalConsoleError.apply(console, arguments);
        Array.from(arguments).forEach(arg => addToConsoleOutput(arg, 'error'));
    };
    
    console.warn = function() {
        originalConsoleWarn.apply(console, arguments);
        Array.from(arguments).forEach(arg => addToConsoleOutput(arg, 'warn'));
    };
    
    // Service Worker functions
    function checkServiceWorker() {
        const swStatus = document.getElementById('sw-status');
        const swScope = document.getElementById('sw-scope');
        const swCache = document.getElementById('sw-cache');
        
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistration()
                .then(registration => {
                    if (registration) {
                        swStatus.textContent = `Service Worker registered (${registration.active ? 'active' : 'inactive'})`;
                        swScope.textContent = `Scope: ${registration.scope}`;
                        console.log('Service Worker is registered with scope:', registration.scope);
                        
                        // Check caches
                        if ('caches' in window) {
                            caches.keys().then(cacheNames => {
                                swCache.textContent = `Caches: ${cacheNames.join(', ')}`;
                                console.log('Available caches:', cacheNames);
                            });
                        } else {
                            swCache.textContent = 'Cache API not available';
                        }
                    } else {
                        swStatus.textContent = 'No Service Worker registered';
                        swScope.textContent = '';
                        swCache.textContent = '';
                        console.warn('No Service Worker is registered');
                    }
                })
                .catch(error => {
                    swStatus.textContent = `Error checking Service Worker: ${error}`;
                    console.error('Error checking Service Worker:', error);
                });
        } else {
            swStatus.textContent = 'Service Workers not supported in this browser';
            console.warn('Service Workers not supported in this browser');
        }
    }
    
    function unregisterServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations()
                .then(registrations => {
                    for (let registration of registrations) {
                        registration.unregister();
                        console.log('Service Worker unregistered');
                    }
                    checkServiceWorker();
                })
                .catch(error => {
                    console.error('Error unregistering Service Worker:', error);
                });
        }
    }
    
    // Audio functions
    function reloadAudio() {
        const audio = document.getElementById('test-audio');
        const currentTime = audio.currentTime;
        const wasPlaying = !audio.paused;
        
        // Reload the audio
        audio.load();
        
        // Restore position and play state
        audio.addEventListener('loadedmetadata', function onLoaded() {
            audio.currentTime = currentTime;
            if (wasPlaying) audio.play();
            audio.removeEventListener('loadedmetadata', onLoaded);
        });
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Check service worker status
        checkServiceWorker();
        
        // Add event listeners
        document.getElementById('check-sw').addEventListener('click', checkServiceWorker);
        document.getElementById('unregister-sw').addEventListener('click', unregisterServiceWorker);
        document.getElementById('reload-audio').addEventListener('click', reloadAudio);
        
        // Log audio events
        const audio = document.getElementById('test-audio');
        
        audio.addEventListener('loadstart', () => console.log('Audio: loadstart'));
        audio.addEventListener('loadedmetadata', () => console.log('Audio: loadedmetadata'));
        audio.addEventListener('loadeddata', () => console.log('Audio: loadeddata'));
        audio.addEventListener('canplay', () => console.log('Audio: canplay'));
        audio.addEventListener('canplaythrough', () => console.log('Audio: canplaythrough'));
        audio.addEventListener('play', () => console.log('Audio: play'));
        audio.addEventListener('pause', () => console.log('Audio: pause'));
        audio.addEventListener('seeking', () => console.log('Audio: seeking'));
        audio.addEventListener('seeked', () => console.log('Audio: seeked'));
        audio.addEventListener('error', (e) => console.error('Audio error:', e.target.error));
    });
</script>
{% endblock %} 