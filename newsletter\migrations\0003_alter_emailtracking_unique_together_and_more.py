# Generated by Django 5.1.5 on 2025-02-23 17:25

import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('newsletter', '0002_remove_newsletter_is_draft_emailtracking_click_count_and_more'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='emailtracking',
            unique_together=set(),
        ),
        migrations.AlterField(
            model_name='emailtracking',
            name='tracking_token',
            field=models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
        ),
    ]
