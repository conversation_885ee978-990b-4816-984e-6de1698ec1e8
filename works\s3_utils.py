import logging
from django.conf import settings
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


def get_s3_url(file_field, custom_domain=None):
    """
    Retrieves the S3 URL for a file stored via a FileField.
    
    Args:
        file_field: A Django FileField instance
        custom_domain: Optional custom domain to use instead of the default S3
                      domain
    
    Returns:
        str: The full HTTPS URL to the file
    """
    if not file_field:
        logger.warning("Attempted to get S3 URL for empty file field")
        return None
    
    # Check if we're using S3 storage
    if not settings.USE_AWS:
        logger.info(
            f"Not using S3 storage, returning standard URL: {file_field.url}"
        )
        return file_field.url
    
    # Get the file's URL
    file_url = file_field.url
    
    # Log the original URL
    logger.info(f"Original file URL: {file_url}")
    
    # If a custom domain is provided, use it instead of the default S3 domain
    if custom_domain:
        # Parse the original URL
        parsed_url = urlparse(file_url)
        path = parsed_url.path
        
        # Construct new URL with custom domain
        new_url = f"https://{custom_domain}{path}"
        logger.info(f"Custom domain URL: {new_url}")
        return new_url
    
    # Ensure the URL uses HTTPS
    if file_url.startswith('http:'):
        file_url = file_url.replace('http:', 'https:', 1)
        logger.info(f"Converted to HTTPS URL: {file_url}")
    
    return file_url


def get_audiobook_s3_url(audiobook_file):
    """
    Retrieves and logs the S3 URL for an audiobook file.
    
    Args:
        audiobook_file: A Django FileField instance containing an audiobook
                       file
    
    Returns:
        str: The full HTTPS URL to the audiobook file
    """
    # Get the custom domain from settings if available
    custom_domain = getattr(settings, 'AWS_S3_CUSTOM_DOMAIN', None)
    
    # Get the S3 URL
    s3_url = get_s3_url(audiobook_file, custom_domain)
    
    if s3_url:
        logger.info(f"Audiobook S3 URL: {s3_url}")
    else:
        logger.warning("Could not generate S3 URL for audiobook")
    
    return s3_url 