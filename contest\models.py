from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils.text import slugify
from django.urls import reverse
from works.models import Product
from utils.image_validation import validate_image


class Contest(models.Model):
    """
    Model representing a contest.
    """
    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True)
    description = models.TextField()
    terms_url = models.URLField(blank=True, null=True)
    start_date = models.DateField(help_text="Date when the contest is announced and visible")
    submissions_open_date = models.DateField(help_text="Date when submissions can start being accepted", null=True, blank=True)
    end_date = models.DateField(help_text="Final date for accepting submissions")
    results_date = models.DateField(help_text="Date when final results and prizes will be announced", null=True, blank=True)
    is_active = models.Bo<PERSON>anField(default=True)
    submissions_enabled = models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text="Toggle to manually enable/disable submissions regardless of dates")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('contest:contest_detail', kwargs={'slug': self.slug})

    def are_submissions_allowed(self):
        """
        Check if submissions are currently allowed based on dates and manual toggle.
        """
        from django.utils import timezone
        now = timezone.now().date()

        # Check if contest is active
        if not self.is_active:
            return False, "This contest is not currently active."

        # Check if submissions are manually disabled
        if not self.submissions_enabled:
            return False, "Submissions are currently disabled for this contest."

        # Check if contest has started
        if now < self.start_date:
            return False, f"This contest has not started yet. It will begin on {self.start_date.strftime('%B %d, %Y')}."

        # Check if contest has ended
        if now > self.end_date:
            return False, "This contest has ended and is no longer accepting submissions."

        # Check if submissions are open yet
        if self.submissions_open_date and now < self.submissions_open_date:
            return False, f"Submissions for this contest will open on {self.submissions_open_date.strftime('%B %d, %Y')}."

        # All checks passed, submissions are allowed
        return True, "Submissions are currently being accepted."


class Category(models.Model):
    """
    Model representing a contest category.
    """
    contest = models.ForeignKey(Contest, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = 'Categories'
        unique_together = ('contest', 'name')

    def __str__(self):
        return f"{self.name} - {self.contest.name}"


def submission_file_path(instance, filename):
    """
    Generate the upload path for submission files.
    """
    # Create a path with contest slug, category name, and preserve original filename
    # Add username prefix to avoid filename collisions
    safe_filename = filename.replace(' ', '_')
    return f'contest_submissions/{instance.contest.slug}/{instance.category.name}/{instance.user.username}_{safe_filename}'


class Submission(models.Model):
    """
    Model representing a contest submission.
    """
    contest = models.ForeignKey(Contest, on_delete=models.CASCADE, related_name='submissions')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='submissions')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='contest_submissions')
    title = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, blank=True)
    description = models.TextField()
    file = models.FileField(upload_to=submission_file_path, validators=[validate_image])
    book_part = models.ForeignKey(
        Product,
        on_delete=models.SET_NULL,
        null=True,
        related_name='contest_submissions',
        verbose_name="Book Part",
        help_text="Select the book part that inspired your artwork."
    )
    rating = models.IntegerField(
        choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5')],
        verbose_name="Rating (1-5 stars)",
        help_text="Your rating for the book part that inspired your artwork."
    )
    review_text = models.TextField(
        help_text="48-200 word review explaining how the selected part inspired your artwork."
    )
    book_fragment = models.TextField(
        help_text="The fragment of the book that inspired your artwork."
    )
    chapter_reference = models.CharField(
        max_length=255,
        help_text="Chapter number and title where the fragment is from."
    )
    submitted_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_approved = models.BooleanField(default=False)
    ai_tools_used = models.BooleanField(default=False)
    ai_tools_details = models.TextField(blank=True)

    # Field for 3D model URLs (Sketchfab, ArtStation, etc.)
    model_3d_url = models.URLField(
        verbose_name="3D Model URL",
        blank=True,
        help_text="For 3D model submissions, provide a link to your model on Sketchfab, ArtStation, or similar platforms."
    )

    # Track if a review has been created in the Works app
    review_created = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if not self.slug:
            # Create a slug from the title and a random string to ensure uniqueness
            base_slug = slugify(self.title)
            if len(base_slug) > 40:
                base_slug = base_slug[:40]

            # Add first few characters of description for more context
            desc_slug = ""
            if self.description:
                desc_words = self.description.split()[:3]  # First 3 words
                desc_slug = "-".join([slugify(word) for word in desc_words])
                if desc_slug:
                    base_slug = f"{base_slug}-{desc_slug}"

            # Add a random string to ensure uniqueness
            import random
            import string
            random_str = ''.join(random.choices(string.ascii_lowercase + string.digits, k=6))
            self.slug = f"{base_slug}-{random_str}"

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.title} by {self.user.username}"

    def get_absolute_url(self):
        if self.slug:
            return reverse('contest:submission_detail', kwargs={'slug': self.slug})
        else:
            return reverse('contest:submission_detail', kwargs={'pk': self.pk})

    @property
    def total_likes(self):
        return min(self.likes.count(), 1000)  # Cap at 1000 as per rules

    @property
    def total_jury_score(self):
        return self.jury_scores.aggregate(models.Sum('points'))['points__sum'] or 0

    @property
    def total_score(self):
        return self.total_likes + self.total_jury_score


class Like(models.Model):
    """
    Model representing a like on a submission.
    """
    submission = models.ForeignKey(Submission, on_delete=models.CASCADE, related_name='likes')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='contest_likes')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('submission', 'user')

    def __str__(self):
        return f"{self.user.username} liked {self.submission.title}"


class JuryScore(models.Model):
    """
    Model representing a jury score for a submission.
    """
    submission = models.ForeignKey(Submission, on_delete=models.CASCADE, related_name='jury_scores')
    judge = models.ForeignKey(User, on_delete=models.CASCADE, related_name='jury_scores')
    points = models.IntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(100)],
        help_text="Score between 1 and 100"
    )
    comment = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('submission', 'judge')

    def __str__(self):
        return f"{self.judge.username} scored {self.points} for {self.submission.title}"


class Comment(models.Model):
    """
    Model representing a comment on a submission.
    """
    submission = models.ForeignKey(Submission, on_delete=models.CASCADE, related_name='comments')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='contest_comments')
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Comment by {self.user.username} on {self.submission.title}"


class BookAccessRequest(models.Model):
    """
    Model representing a book access request for contest participants.
    """
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('rejected', 'Rejected'),
    )

    ACCESS_TYPE_CHOICES = (
        ('free', 'Free Access'),
        ('paid', 'Paid Access'),
    )

    contest = models.ForeignKey(Contest, on_delete=models.CASCADE, related_name='book_access_requests')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='book_access_requests')
    book_part = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='contest_access_requests')
    category = models.ForeignKey(Category, on_delete=models.CASCADE, related_name='book_access_requests')
    format_type = models.CharField(max_length=20, choices=(('ebook', 'E-book'), ('audiobook', 'Audiobook')))
    access_type = models.CharField(max_length=10, choices=ACCESS_TYPE_CHOICES)
    reason = models.TextField(blank=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='pending')
    access_granted = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        unique_together = ('user', 'book_part')

    def __str__(self):
        return f"{self.user.username}'s request for {self.book_part.name} ({self.get_access_type_display()})"
