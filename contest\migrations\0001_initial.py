# Generated by Django 5.1.5 on 2025-04-25 06:40

import contest.models
import django.core.validators
import django.db.models.deletion
import utils.image_validation
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('works', '0014_fix_download_link_expiry_days'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Contest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('slug', models.SlugField(max_length=255, unique=True)),
                ('description', models.TextField()),
                ('terms_url', models.URLField(blank=True, null=True)),
                ('start_date', models.DateField()),
                ('end_date', models.DateField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField(blank=True)),
                ('contest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to='contest.contest')),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'unique_together': {('contest', 'name')},
            },
        ),
        migrations.CreateModel(
            name='Submission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('file', models.FileField(upload_to=contest.models.submission_file_path, validators=[utils.image_validation.validate_image])),
                ('review_text', models.TextField(help_text='48-200 word review explaining how the selected part inspired your artwork.')),
                ('book_fragment', models.TextField(help_text='The fragment of the book that inspired your artwork.')),
                ('chapter_reference', models.CharField(help_text='Chapter number and title where the fragment is from.', max_length=255)),
                ('submitted_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_approved', models.BooleanField(default=False)),
                ('book_part', models.ForeignKey(limit_choices_to={'category__name': 'Book'}, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='contest_submissions', to='works.product')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='contest.category')),
                ('contest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='submissions', to='contest.contest')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contest_submissions', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Like',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contest_likes', to=settings.AUTH_USER_MODEL)),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='likes', to='contest.submission')),
            ],
            options={
                'unique_together': {('submission', 'user')},
            },
        ),
        migrations.CreateModel(
            name='JuryScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('points', models.IntegerField(help_text='Score between 1 and 100', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(100)])),
                ('comment', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('judge', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jury_scores', to=settings.AUTH_USER_MODEL)),
                ('submission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='jury_scores', to='contest.submission')),
            ],
            options={
                'unique_together': {('submission', 'judge')},
            },
        ),
    ]
