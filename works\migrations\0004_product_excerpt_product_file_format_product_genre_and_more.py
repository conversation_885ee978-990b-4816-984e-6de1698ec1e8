# Generated by Django 5.1.5 on 2025-02-20 13:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('works', '0003_alter_review_comment_alter_review_rating'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='excerpt',
            field=models.TextField(blank=True, help_text='A sample excerpt from the work'),
        ),
        migrations.AddField(
            model_name='product',
            name='file_format',
            field=models.CharField(default='PDF', max_length=20),
        ),
        migrations.AddField(
            model_name='product',
            name='genre',
            field=models.Char<PERSON>ield(default='Add genre', help_text='Genre of the work', max_length=100),
        ),
        migrations.AddField(
            model_name='product',
            name='language',
            field=models.Char<PERSON>ield(default='English', max_length=50),
        ),
        migrations.AddField(
            model_name='product',
            name='pages',
            field=models.PositiveIntegerField(default=1, help_text='Number of pages in the work'),
        ),
        migrations.AddField(
            model_name='product',
            name='reading_time',
            field=models.PositiveIntegerField(default=1, help_text='Estimated reading time in minutes'),
        ),
    ]
