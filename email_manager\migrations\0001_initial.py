# Generated by Django 5.1.5 on 2025-05-01 08:53

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EmailConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('require_email_verification', models.BooleanField(default=True, help_text='If enabled, users must verify their email address to fully access the site')),
                ('hourly_email_limit', models.IntegerField(default=90, help_text='Maximum emails to send per hour (Dreamhost limit is 100)')),
                ('queue_batch_size', models.IntegerField(default=10, help_text='Number of emails to process in one batch')),
                ('queue_batch_delay', models.IntegerField(default=30, help_text='Seconds to wait between batches')),
                ('send_welcome_emails', models.BooleanField(default=True, help_text='Send welcome emails to new users')),
                ('send_newsletter_confirmation', models.<PERSON><PERSON><PERSON><PERSON>ield(default=False, help_text='Send confirmation emails for newsletter subscriptions')),
                ('combine_order_emails', models.BooleanField(default=True, help_text='Combine order confirmation and digital content emails')),
                ('notify_admin_on_limit', models.BooleanField(default=True, help_text='Notify admin when approaching email limits')),
                ('admin_email', models.EmailField(blank=True, help_text='Email to send admin notifications to', max_length=254, null=True)),
            ],
            options={
                'verbose_name': 'Email Configuration',
                'verbose_name_plural': 'Email Configuration',
            },
        ),
        migrations.CreateModel(
            name='QueuedEmail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255)),
                ('recipient', models.EmailField(max_length=254)),
                ('html_content', models.TextField()),
                ('text_content', models.TextField()),
                ('from_email', models.EmailField(max_length=254)),
                ('has_attachments', models.BooleanField(default=False)),
                ('attachment_data', models.TextField(blank=True, help_text='JSON data for attachments', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('failed', 'Failed')], default='pending', max_length=10)),
                ('priority', models.IntegerField(choices=[(1, 'Critical'), (2, 'Important'), (3, 'Normal'), (4, 'Low')], default=3)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('last_attempt', models.DateTimeField(blank=True, null=True)),
                ('content_type', models.CharField(blank=True, max_length=50, null=True)),
                ('object_id', models.CharField(blank=True, max_length=50, null=True)),
            ],
            options={
                'verbose_name': 'Queued Email',
                'verbose_name_plural': 'Queued Emails',
                'ordering': ['priority', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='EmailCounter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(default=django.utils.timezone.now)),
                ('hour', models.IntegerField()),
                ('count', models.IntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Email Counter',
                'verbose_name_plural': 'Email Counters',
                'unique_together': {('date', 'hour')},
            },
        ),
    ]
