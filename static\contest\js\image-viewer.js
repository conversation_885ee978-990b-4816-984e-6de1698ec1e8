/**
 * Image Viewer for Submission Details
 * Features:
 * - Fullscreen view
 * - Zoom functionality
 * - Right-click protection
 * - Easy exit
 */

document.addEventListener('DOMContentLoaded', function() {
    // Create the image viewer overlay
    const imageViewerHTML = `
        <div class="image-viewer-overlay">
            <button class="image-viewer-close" title="Close (Esc)">
                <i class="fas fa-times"></i>
            </button>
            <div class="image-viewer-container">
                <img src="" alt="Fullscreen view" class="image-viewer-img zoom-level-1">
                <div class="image-viewer-controls">
                    <button class="image-viewer-btn zoom-out-btn" title="Zoom out">
                        <i class="fas fa-search-minus"></i>
                    </button>
                    <button class="image-viewer-btn zoom-reset-btn" title="Reset zoom">
                        <i class="fas fa-compress"></i>
                    </button>
                    <button class="image-viewer-btn zoom-in-btn" title="Zoom in">
                        <i class="fas fa-search-plus"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', imageViewerHTML);

    // Get elements
    const imageContainer = document.querySelector('.submission-image-container');
    const submissionImage = imageContainer?.querySelector('img');

    // If there's no image, don't proceed
    if (!submissionImage) return;

    // Add fullscreen button and protect the image
    submissionImage.parentElement.classList.add('protected-image');
    const fullscreenBtn = document.createElement('button');
    fullscreenBtn.className = 'fullscreen-btn';
    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i> View Fullscreen';
    submissionImage.parentElement.appendChild(fullscreenBtn);

    // Get viewer elements
    const imageViewer = document.querySelector('.image-viewer-overlay');
    const viewerImage = imageViewer.querySelector('.image-viewer-img');
    const closeBtn = imageViewer.querySelector('.image-viewer-close');
    const zoomInBtn = imageViewer.querySelector('.zoom-in-btn');
    const zoomOutBtn = imageViewer.querySelector('.zoom-out-btn');
    const zoomResetBtn = imageViewer.querySelector('.zoom-reset-btn');

    // Zoom and pan variables
    let currentZoomLevel = 1;
    const maxZoomLevel = 4;
    let isPanning = false;
    let startX, startY, startTranslateX = 0, startTranslateY = 0;
    let translateX = 0, translateY = 0;

    // Open viewer
    fullscreenBtn.addEventListener('click', function() {
        viewerImage.src = submissionImage.src;
        viewerImage.alt = submissionImage.alt;
        imageViewer.style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Prevent scrolling
        resetZoom();
    });

    // Close viewer
    function closeViewer() {
        imageViewer.style.display = 'none';
        document.body.style.overflow = ''; // Restore scrolling
    }

    closeBtn.addEventListener('click', closeViewer);

    // Close on Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && imageViewer.style.display === 'flex') {
            closeViewer();
        }
    });

    // Close when clicking outside the image
    imageViewer.addEventListener('click', function(e) {
        if (e.target === imageViewer) {
            closeViewer();
        }
    });

    // Zoom functions with targeted zooming
    function updateZoom(clientX, clientY) {
        // Get image dimensions and position
        const rect = viewerImage.getBoundingClientRect();

        if (currentZoomLevel === 1) {
            // Reset transform when at level 1
            viewerImage.style.transform = 'scale(1)';
            translateX = 0;
            translateY = 0;
            imageViewer.style.cursor = 'zoom-in';
            return;
        }

        if (clientX && clientY && currentZoomLevel > 1) {
            // Calculate relative position within the image (0 to 1)
            const relativeX = (clientX - rect.left) / rect.width;
            const relativeY = (clientY - rect.top) / rect.height;

            // Set transform origin based on click position
            viewerImage.style.transformOrigin = `${relativeX * 100}% ${relativeY * 100}%`;
        }

        // Apply zoom and translation
        viewerImage.style.transform = `scale(${currentZoomLevel}) translate(${translateX}px, ${translateY}px)`;

        // Update cursor
        imageViewer.style.cursor = currentZoomLevel > 1 ? 'move' : 'zoom-in';
        viewerImage.parentElement.classList.toggle('panning', currentZoomLevel > 1);
    }

    function zoomIn(clientX, clientY) {
        if (currentZoomLevel < maxZoomLevel) {
            currentZoomLevel++;
            updateZoom(clientX, clientY);
        }
    }

    function zoomOut() {
        if (currentZoomLevel > 1) {
            currentZoomLevel--;
            updateZoom();
        }
    }

    function resetZoom() {
        currentZoomLevel = 1;
        translateX = 0;
        translateY = 0;
        updateZoom();
    }

    // Zoom controls
    zoomInBtn.addEventListener('click', function() {
        zoomIn();
    });
    zoomOutBtn.addEventListener('click', zoomOut);
    zoomResetBtn.addEventListener('click', resetZoom);

    // Zoom on image click with targeted zooming
    viewerImage.addEventListener('click', function(e) {
        e.stopPropagation();
        if (currentZoomLevel === 1) {
            zoomIn(e.clientX, e.clientY);
        } else {
            resetZoom();
        }
    });

    // Pan functionality when zoomed in
    viewerImage.addEventListener('mousedown', function(e) {
        if (currentZoomLevel > 1) {
            isPanning = true;
            startX = e.clientX;
            startY = e.clientY;
            startTranslateX = translateX;
            startTranslateY = translateY;
            viewerImage.parentElement.classList.add('panning');
        }
    });

    document.addEventListener('mousemove', function(e) {
        if (isPanning && currentZoomLevel > 1) {
            const dx = (e.clientX - startX) / currentZoomLevel;
            const dy = (e.clientY - startY) / currentZoomLevel;
            translateX = startTranslateX + dx;
            translateY = startTranslateY + dy;
            viewerImage.style.transform = `scale(${currentZoomLevel}) translate(${translateX}px, ${translateY}px)`;
        }
    });

    document.addEventListener('mouseup', function() {
        isPanning = false;
        viewerImage.parentElement.classList.remove('panning');
    });

    // Mouse wheel zoom
    viewerImage.addEventListener('wheel', function(e) {
        e.preventDefault();
        if (e.deltaY < 0) {
            zoomIn(e.clientX, e.clientY);
        } else {
            zoomOut();
        }
    });

    // Prevent right-click on images - more aggressive approach
    function preventRightClick(e) {
        e.preventDefault();
        e.stopPropagation();
        return false;
    }

    // Apply right-click protection to all images
    submissionImage.addEventListener('contextmenu', preventRightClick, true);
    viewerImage.addEventListener('contextmenu', preventRightClick, true);

    // Add global right-click protection for the image container
    document.querySelector('.protected-image').addEventListener('contextmenu', preventRightClick, true);

    // Disable drag and drop for images
    submissionImage.addEventListener('dragstart', preventRightClick, true);
    viewerImage.addEventListener('dragstart', preventRightClick, true);

    // Add CSS to prevent selection
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            .protected-image img {
                -webkit-user-select: none;
                -khtml-user-select: none;
                -moz-user-select: none;
                -o-user-select: none;
                user-select: none;
                -webkit-user-drag: none;
                -khtml-user-drag: none;
                -moz-user-drag: none;
                -o-user-drag: none;
                user-drag: none;
                pointer-events: none;
            }

            .fullscreen-btn {
                pointer-events: all;
            }
        </style>
    `);

    // Add no-select class to body when viewer is open
    imageViewer.addEventListener('mouseenter', function() {
        document.body.classList.add('no-select');
    });

    imageViewer.addEventListener('mouseleave', function() {
        document.body.classList.remove('no-select');
    });

    // Touch gestures for mobile
    let touchStartX = 0;
    let touchStartY = 0;
    let lastTouchDistance = 0;
    let touchPanning = false;

    // Handle touch start
    viewerImage.addEventListener('touchstart', function(e) {
        if (e.touches.length === 1) {
            // Single touch - prepare for panning or tap
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;

            if (currentZoomLevel > 1) {
                touchPanning = true;
                startTranslateX = translateX;
                startTranslateY = translateY;
            }
        } else if (e.touches.length === 2) {
            // Two fingers - prepare for pinch zoom
            lastTouchDistance = Math.hypot(
                e.touches[0].clientX - e.touches[1].clientX,
                e.touches[0].clientY - e.touches[1].clientY
            );
        }
        e.preventDefault();
    });

    // Handle touch move
    viewerImage.addEventListener('touchmove', function(e) {
        e.preventDefault();

        if (e.touches.length === 1 && touchPanning) {
            // Pan the image when zoomed in
            const dx = (e.touches[0].clientX - touchStartX) / currentZoomLevel;
            const dy = (e.touches[0].clientY - touchStartY) / currentZoomLevel;
            translateX = startTranslateX + dx;
            translateY = startTranslateY + dy;
            viewerImage.style.transform = `scale(${currentZoomLevel}) translate(${translateX}px, ${translateY}px)`;
        } else if (e.touches.length === 2) {
            // Calculate new distance between touches
            const touchDistance = Math.hypot(
                e.touches[0].clientX - e.touches[1].clientX,
                e.touches[0].clientY - e.touches[1].clientY
            );

            // Calculate center point between the two touches
            const centerX = (e.touches[0].clientX + e.touches[1].clientX) / 2;
            const centerY = (e.touches[0].clientY + e.touches[1].clientY) / 2;

            // Determine if we're zooming in or out
            if (touchDistance > lastTouchDistance + 10) {
                // Zooming in
                if (currentZoomLevel < maxZoomLevel) {
                    currentZoomLevel = Math.min(currentZoomLevel + 0.5, maxZoomLevel);
                    updateZoom(centerX, centerY);
                }
            } else if (touchDistance < lastTouchDistance - 10) {
                // Zooming out
                if (currentZoomLevel > 1) {
                    currentZoomLevel = Math.max(currentZoomLevel - 0.5, 1);
                    updateZoom(centerX, centerY);
                }
            }

            lastTouchDistance = touchDistance;
        }
    });

    // Handle touch end
    viewerImage.addEventListener('touchend', function(e) {
        if (e.touches.length === 0) {
            touchPanning = false;

            // Check if it was a tap (not a pan)
            const touchEndX = e.changedTouches[0].clientX;
            const touchEndY = e.changedTouches[0].clientY;
            const diffX = touchStartX - touchEndX;
            const diffY = touchStartY - touchEndY;

            if (Math.abs(diffX) < 10 && Math.abs(diffY) < 10) {
                // It's a tap - toggle zoom
                if (currentZoomLevel === 1) {
                    zoomIn(touchEndX, touchEndY);
                } else {
                    resetZoom();
                }
            }
        }
    });

    // Prevent default touch behavior to avoid browser gestures
    viewerImage.addEventListener('touchstart', function(e) { e.preventDefault(); }, { passive: false });
    viewerImage.addEventListener('touchmove', function(e) { e.preventDefault(); }, { passive: false });
    viewerImage.addEventListener('touchend', function(e) { e.preventDefault(); }, { passive: false });
});
