from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.urls import reverse
from taggit.managers import TaggableManager
from django.utils.text import slugify
from image_cropping import ImageRatioField


def validate_not_empty(value):
    """
    Validates that a field value is not empty or just whitespace.
    """
    if not value.strip():
        raise ValidationError('This field cannot be empty or just whitespace.')


class Category(models.Model):
    """
    Model representing a blog post category.
    """
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('category_detail', kwargs={'slug': self.slug})


class Post(models.Model):
    """
    Model representing a blog post in the system.
    """

    STATUS_CHOICES = (
        (0, "Draft"),
        (1, "Published")
    )

    post_title = models.CharField(
        max_length=200,
        unique=True,
        help_text="Title of the post",
        validators=[validate_not_empty]
    )
    post_slug = models.SlugField(
        max_length=200,
        unique=True,
        help_text="URL-friendly version of the title",
        blank=False,
        null=False,

    )
    post_author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name="blog_posts",
        limit_choices_to={'is_staff': True}
    )
    post_featured_image = models.ImageField(
        upload_to='blog/featured_images/%Y/',
        help_text="Featured image for the blog post",
        blank=True,
        null=True
    )
    # Image cropping fields for different display contexts
    featured_cropping = ImageRatioField(
        'post_featured_image', '16x9',
        help_text="Crop for featured post section"
    )
    card_cropping = ImageRatioField(
        'post_featured_image', '4x3',
        help_text="Crop for blog cards"
    )
    detail_cropping = ImageRatioField(
        'post_featured_image', '3x2',
        help_text="Crop for post detail view"
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        related_name='posts',
        null=True,
        blank=True
    )
    post_content = models.TextField(
        help_text="Main content of the post"
    )
    post_excerpt = models.TextField(
        blank=True,
        help_text="Brief summary of the post"
    )
    post_created_on = models.DateTimeField(auto_now_add=True)
    post_updated_at = models.DateTimeField(auto_now=True)
    post_status = models.IntegerField(
        choices=STATUS_CHOICES,
        default=0,
        help_text="0: Draft, 1: Published"
    )

    tags = TaggableManager()

    def get_absolute_url(self):
        """
        Returns the URL to access a particular post instance.
        """
        return reverse('post_detail', kwargs={'slug': self.post_slug})

    def get_featured_image_url(self, crop_type='featured'):
        """
        Returns the cropped image URL for the specified crop type.
        Falls back to original image if no cropping is set.
        """
        if not self.post_featured_image:
            return None

        from easy_thumbnails.files import get_thumbnailer

        # Define size mappings for different crop types
        size_map = {
            'featured': (800, 450),  # 16:9 ratio
            'card': (400, 300),      # 4:3 ratio
            'detail': (600, 400),    # 3:2 ratio
        }

        crop_field_map = {
            'featured': self.featured_cropping,
            'card': self.card_cropping,
            'detail': self.detail_cropping,
        }

        crop_data = crop_field_map.get(crop_type)
        size = size_map.get(crop_type, (800, 600))

        try:
            thumbnailer = get_thumbnailer(self.post_featured_image)

            # If we have crop data, use it
            if crop_data:
                # Parse crop data (format: "x,y,x2,y2")
                crop_coords = crop_data.split(',')
                if len(crop_coords) == 4:
                    try:
                        x, y, x2, y2 = [int(float(coord)) for coord in crop_coords]
                        # Create crop box for easy_thumbnails
                        crop_box = (x, y, x2, y2)

                        thumbnail = thumbnailer.get_thumbnail({
                            'size': size,
                            'box': crop_box,
                            'crop': True,
                            'quality': 85
                        })
                        return thumbnail.url
                    except (ValueError, TypeError):
                        pass

            # Fallback: return smart-cropped image
            thumbnail = thumbnailer.get_thumbnail({
                'size': size,
                'crop': 'smart',
                'quality': 85
            })
            return thumbnail.url

        except Exception as e:
            # Final fallback: return original image
            return self.post_featured_image.url

    class Meta:
        """
        Meta class for Post model.
        """
        ordering = ['-post_created_on']
        verbose_name = 'Post'
        verbose_name_plural = 'Posts'

    def __str__(self):
        """
        String representation of the Post model.
        """
        return self.post_title

    def save(self, *args, **kwargs):
        """
        Custom save method to automatically generate post excerpt.
        """
        if not self.post_excerpt and self.post_content:
            from django.utils.html import strip_tags
            self.post_excerpt = strip_tags(self.post_content)[:150] + '...'
        if not self.post_slug:
            self.post_slug = slugify(self.post_title)
        super().save(*args, **kwargs)


class PostImage(models.Model):
    """
    Model representing images embedded within blog post content.
    """
    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='content_images'
    )
    image = models.ImageField(
        upload_to='blog/content_images/%Y/%m/',
        help_text="Image to be embedded in the blog post content"
    )
    caption = models.CharField(
        max_length=255,
        help_text="Caption displayed below the image",
        blank=True
    )
    alt_text = models.CharField(
        max_length=255,
        help_text="Alternative text for accessibility (required)",
        validators=[validate_not_empty]
    )
    width = models.PositiveIntegerField(
        default=800,
        help_text="Display width in pixels"
    )
    alignment = models.CharField(
        max_length=10,
        choices=(
            ('left', 'Left'),
            ('center', 'Center'),
            ('right', 'Right')
        ),
        default='center'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Post Image'
        verbose_name_plural = 'Post Images'
        
    def __str__(self):
        return f"Image for {self.post.post_title} ({self.id})"
    
    def get_html(self):
        """
        Returns the HTML for embedding this image in a post.
        """
        html = f'<figure class="blog-image text-{self.alignment} mb-4">'
        html += f'<img src="{self.image.url}" alt="{self.alt_text}" ' \
                f'style="max-width: {self.width}px;" ' \
                f'class="img-fluid rounded shadow-sm" />'
        if self.caption:
            html += f'<figcaption class="text-center text-muted mt-2 ' \
                   f'fst-italic small">{self.caption}</figcaption>'
        html += '</figure>'
        return html


class Comment(models.Model):
    """
    Model representing comments on blog posts.
    """

    post = models.ForeignKey(
        Post,
        on_delete=models.CASCADE,
        related_name='comments'
    )
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='blog_comments'
    )
    content = models.TextField(
        validators=[validate_not_empty]
    )
    created_on = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    active = models.BooleanField(
        default=True,
        help_text="Uncheck to hide the comment"
    )

    class Meta:
        """
        Meta class for Comment model.
        """
        ordering = ['created_on']
        verbose_name = 'Comment'
        verbose_name_plural = 'Comments'

    def __str__(self):
        """
        String representation of the Comment model.
        """
        return f'Comment by {self.author} on {self.post}'
