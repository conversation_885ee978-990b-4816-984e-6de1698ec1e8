# Generated by Django 5.1.5 on 2025-02-12 11:22

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('checkout', '0007_alter_orderitem_order'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='billing_postcode',
            field=models.CharField(help_text='Enter a 4-6 characters of postcode.', max_length=6, validators=[django.core.validators.RegexValidator(message='Postcode must be 4-6 characters and contain only letters and numbers', regex='^[A-Za-z0-9]{4,6}$')]),
        ),
    ]
