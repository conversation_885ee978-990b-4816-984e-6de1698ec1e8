/**
 * Newsletter signup integration for the registration form.
 * 
 * Features:
 * - Newsletter subscription checkbox handling
 * - Google Sheets integration for storing subscriber data
 * - Loading spinner for visual feedback
 * - Success toast notification
 * - Seamless form submission continuation
 * - Password validation feedback
 * 
 * Dependencies:
 * - Bootstrap for spinner and toast components
 * - Font Awesome for icons
 * - Google Apps Script backend for newsletter data storage
 */

document.addEventListener('DOMContentLoaded', function() {
    const signupForm = document.querySelector('form[action="/accounts/signup/"]');
    const newsletterCheckbox = document.getElementById('newsletterSignup');
    const emailInput = document.getElementById('id_email');
    const nameInput = document.getElementById('id_username');
    const spinner = document.getElementById('newsletterSpinner');
    const password1Input = document.getElementById('id_password1');
    const password2Input = document.getElementById('id_password2');

    if (!signupForm || !newsletterCheckbox || !emailInput) {
        return;
    }

    // Add password match validation
    if (password1Input && password2Input) {
        // Create feedback element for password mismatch
        const feedbackDiv = document.createElement('div');
        feedbackDiv.classList.add('invalid-feedback');
        feedbackDiv.textContent = 'Passwords do not match';
        feedbackDiv.style.display = 'none';
        password2Input.parentNode.appendChild(feedbackDiv);

        // Add event listener for password confirmation
        password2Input.addEventListener('input', function() {
            if (password1Input.value !== password2Input.value) {
                password2Input.classList.add('is-invalid');
                feedbackDiv.style.display = 'block';
            } else {
                password2Input.classList.remove('is-invalid');
                feedbackDiv.style.display = 'none';
            }
        });

        // Also check when password1 changes and password2 has a value
        password1Input.addEventListener('input', function() {
            if (password2Input.value && password1Input.value !== password2Input.value) {
                password2Input.classList.add('is-invalid');
                feedbackDiv.style.display = 'block';
            } else if (password2Input.value) {
                password2Input.classList.remove('is-invalid');
                feedbackDiv.style.display = 'none';
            }
        });
    }

    signupForm.addEventListener('submit', async function(e) {
        // Check passwords match before submission
        if (password1Input && password2Input && password1Input.value !== password2Input.value) {
            e.preventDefault();
            password2Input.classList.add('is-invalid');
            const feedbackDiv = password2Input.parentNode.querySelector('.invalid-feedback');
            if (feedbackDiv) {
                feedbackDiv.style.display = 'block';
            }
            password2Input.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return;
        }

        if (newsletterCheckbox.checked) {
            e.preventDefault();
            
            try {
                // Show spinner
                spinner.classList.remove('d-none');
                
                const googleScriptUrl = newsletterCheckbox.dataset.formUrl;
                if (!googleScriptUrl) {
                    throw new Error('Google Script URL not found');
                }

                const formData = new URLSearchParams();
                formData.append('Date', new Date().toISOString());
                formData.append('Email', emailInput.value);
                formData.append('Name', nameInput ? nameInput.value : '');

                const response = await fetch(googleScriptUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Accept': 'application/json'
                    },
                    body: formData,
                    // Add timeout to prevent hanging
                    signal: AbortSignal.timeout(3000) // 3 second timeout
                });

                if (!response.ok) {
                    throw new Error('Newsletter subscription failed');
                }

                // Show success toast
                const toast = new bootstrap.Toast(document.getElementById('newsletterToast'));
                toast.show();

            } catch (error) {
                console.error('Newsletter subscription failed:', error);
                // Continue with form submission even if newsletter fails
            } finally {
                // Hide spinner
                spinner.classList.add('d-none');
                // Submit the form programmatically to continue with Django signup
                signupForm.removeEventListener('submit', arguments.callee);
                signupForm.submit();
            }
        }
    });
});