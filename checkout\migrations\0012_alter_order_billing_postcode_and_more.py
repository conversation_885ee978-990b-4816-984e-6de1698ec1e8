# Generated by Django 5.1.5 on 2025-04-01 13:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('checkout', '0011_orderitem_quantity'),
    ]

    operations = [
        migrations.AlterField(
            model_name='order',
            name='billing_postcode',
            field=models.CharField(help_text='Enter a 4-6 characters of postcode.', max_length=6, validators=[django.core.validators.RegexValidator(message='Postcode must be 4-8 characters and can contain letters, numbers, and hyphens', regex='^[A-Za-z0-9-]{4,8}$')]),
        ),
        migrations.AlterField(
            model_name='order',
            name='payment_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('paid', 'Paid'), ('failed', 'Failed'), ('refunded', 'Refunded')], default='pending', max_length=50),
        ),
    ]
