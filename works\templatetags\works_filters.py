from django import template
from django.templatetags.static import static
from django.utils.safestring import mark_safe

register = template.Library()


@register.filter
def default_image(image_url):
    if image_url:
        return image_url
    return static('images/noimage.png')


@register.filter
def format_contest_review(text):
    """
    Format a review text from a contest submission.

    The expected format is:
    - Review text
    - Book fragment in quotes
    - Part name
    - Chapter reference

    This filter will format it with proper HTML styling.
    """
    if not text:
        return ''

    # Split the text by newlines
    lines = text.split('\n')

    # Check if this is a contest review (has at least 4 lines with specific patterns)
    is_contest_review = False
    part_line = None
    chapter_line = None

    # Look for the part and chapter lines
    for i, line in enumerate(lines):
        if line.startswith('Part '):
            part_line = i
        elif line.startswith('Chapter '):
            chapter_line = i

    # If we found both part and chapter lines, this is likely a contest review
    if part_line is not None and chapter_line is not None:
        is_contest_review = True

    if not is_contest_review:
        # If it's not a contest review, return the text as is
        return text.replace('\n', '<br>')

    # Extract the parts
    # Everything before the part line is the review text and book fragment
    review_and_fragment = '\n'.join(lines[:part_line])

    # Split by quotes to extract the book fragment
    parts = review_and_fragment.split('"')
    if len(parts) >= 3:
        review_text = parts[0].strip()
        book_fragment = parts[1].strip()
    else:
        # If no quotes found, just use the whole text as review
        review_text = review_and_fragment
        book_fragment = ""

    # Get the part and chapter references
    part_name = lines[part_line].strip()
    chapter_reference = lines[chapter_line].strip()

    # Format the HTML
    html = f"""
    <div class="contest-review">
        <p>{review_text}</p>
        <blockquote class="blockquote fst-italic mb-3">
            {book_fragment}
        </blockquote>
        <div class="text-end">
            <p class="mb-0 fw-bold text-dark">{part_name}</p>
            <p class="mb-0 fw-bold text-dark">{chapter_reference}</p>
        </div>
    </div>
    """

    return mark_safe(html)
