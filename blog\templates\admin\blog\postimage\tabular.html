{% extends "admin/edit_inline/tabular.html" %}
{% load i18n admin_urls %}

{% block field_sets %}
{{ block.super }}
{% for inline_admin_form in inline_admin_formset %}
  {% if inline_admin_form.original %}
  <tr class="row-image-html">
    <td colspan="{{ inline_admin_formset.formset.form.fields|length|add:1 }}">
      <div class="image-html-container" style="background-color: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #ddd;">
        <div style="margin-bottom: 5px; font-weight: bold;">Copy this HTML to embed in your post:</div>
        <div class="form-row">
          <textarea id="image-html-{{ inline_admin_form.original.pk }}" 
                   class="image-html form-control" readonly 
                   style="width: 80%; height: 80px; font-family: monospace;">{{ inline_admin_form.original.get_html }}</textarea>
          <button type="button" class="copy-html-btn button" 
                  data-id="{{ inline_admin_form.original.pk }}"
                  style="margin-left: 10px; background-color: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 4px;">
            Copy HTML
          </button>
        </div>
      </div>
    </td>
  </tr>
  {% endif %}
{% endfor %}
{% endblock %}

{% block inline_formset_js %}
{{ block.super }}
<script>
(function($) {
  $(document).ready(function() {
    $('.copy-html-btn').on('click', function() {
      const id = $(this).data('id');
      const textarea = document.getElementById('image-html-' + id);
      textarea.select();
      document.execCommand('copy');
      
      // Show feedback
      const originalText = $(this).text();
      $(this).text('Copied!');
      $(this).css('background-color', '#007bff');
      setTimeout(() => {
        $(this).text(originalText);
        $(this).css('background-color', '#28a745');
      }, 1500);
    });
  });
})(django.jQuery);
</script>
{% endblock %} 