{% extends "base.html" %}
{% load static %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-12">
            <div class="bg-black bg-opacity-75 p-4 rounded mb-4">
                <div class="row mb-3">
                    <div class="col">
                        <hr>
                        <h2 class="logo-font mb-0">Your Book Cart</h2>
                        <hr>
                    </div>
                </div>
                
                {% if bookcart_items %}
                    <div class="row">
                        <div class="col-12 col-lg-8">
                            <!-- Cart Items -->
                            {% for item in bookcart_items %}
                                <div class="card mb-3 bg-dark bg-opacity-50">
                                    <div class="row g-0">
                                        <div class="col-4 col-md-3 p-2">
                                            <a href="{% url 'work_detail' item.product.id %}" aria-label="View details of {{ item.product.name }}">
                                                {% if item.product.image %}
                                                    <img src="{{ item.product.image.url }}" class="img-fluid rounded" alt="{{ item.product.name }}">
                                                {% else %}
                                                    <img src="{% static 'images/noimage.png' %}" class="img-fluid rounded" alt="{{ item.product.name }}">
                                                {% endif %}
                                            </a>
                                        </div>
                                        <div class="col-8 col-md-9">
                                            <div class="card-body p-2 p-md-3">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h5 class="card-title mb-1">
                                                            <a href="{% url 'work_detail' item.product.id %}" class="text-decoration-none text-white" aria-label="View details of {{ item.product.name }}">
                                                                {{ item.product.name }}
                                                            </a>
                                                        </h5>
                                                        <p class="card-text mb-1">
                                                            <span class="badge bg-primary">{{ item.product.category }}</span>
                                                        </p>
                                                    </div>
                                                    <h5 class="text-primary mb-0">${{ item.price }}</h5>
                                                </div>
                                                
                                                <div class="d-flex justify-content-end mt-3">
                                                    <a class="btn btn-sm btn-outline-danger" href="{% url 'remove_from_bookcart' item.item_id %}" aria-label="Remove {{ item.product.name }} from cart">
                                                        <i class="fas fa-trash-alt me-1"></i> Remove
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- Order Summary -->
                        <div class="col-12 col-lg-4">
                            <div class="card mb-3 bg-dark bg-opacity-50 text-white">
                                <div class="card-header bg-primary bg-opacity-75">
                                    <h4 class="mb-0">Order Summary</h4>
                                </div>
                                <div class="card-body">
                                    <div class="d-flex justify-content-between mb-2">
                                        <span>Subtotal:</span>
                                        <span>${{ total }}</span>
                                    </div>
                                    <hr>
                                    <div class="d-flex justify-content-between mb-2">
                                        <strong>Total:</strong>
                                        <strong class="text-primary">${{ total }}</strong>
                                    </div>
                                    <p class="small text-muted mb-3">Final price (taxes may apply in some locations)</p>
                                    
                                    <div class="d-grid gap-2 mt-4">
                                        <a href="{% url 'checkout' %}" class="btn btn-primary" aria-label="Proceed to secure checkout">
                                            <span class="text-uppercase">Secure Checkout</span>
                                            <span class="icon">
                                                <i class="fas fa-lock" aria-hidden="true"></i>
                                            </span>
                                        </a>
                                        <a href="{% url 'works' %}" class="btn btn-outline-secondary" aria-label="Continue shopping">
                                            <span class="icon">
                                                <i class="fas fa-chevron-left"></i>
                                            </span>
                                            <span>Continue Shopping</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-4x mb-4 text-muted"></i>
                        <h3 class="mb-4">Your book cart is empty</h3>
                        <p class="lead mb-4">Looks like you haven't added any books to your cart yet.</p>
                        <a href="{% url 'works' %}" class="btn btn-primary btn-lg">
                            <span class="icon">
                                <i class="fas fa-book me-2"></i>
                            </span>
                            <span>Explore Our Collection</span>
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}