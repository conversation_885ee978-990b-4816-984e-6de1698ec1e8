document.addEventListener('DOMContentLoaded', function() {
    // Get references to navbar elements
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    // Function to close the navbar when clicking outside
    document.addEventListener('click', function(event) {
        // Only proceed if the navbar is expanded
        const isNavbarExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
        
        // Check if click was outside the navbar and the navbar is expanded
        if (isNavbarExpanded && 
            !navbarCollapse.contains(event.target) && 
            !navbarToggler.contains(event.target)) {
            
            // Simulate a click on the navbar toggler to close it
            navbarToggler.click();
        }
    });
    
    // Function to close navbar on scroll
    let lastScrollTop = 0;
    window.addEventListener('scroll', function() {
        // Only proceed if navbar is expanded
        const isNavbarExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
        
        if (isNavbarExpanded) {
            // Get current scroll position
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Check if user has scrolled more than 10px
            if (Math.abs(lastScrollTop - scrollTop) > 10) {
                // Close the navbar
                navbarToggler.click();
            }

            lastScrollTop = scrollTop;
        }
    }, { passive: true }); // Using passive listener for better scroll performance

    // Enhanced keyboard navigation for navbar
    document.addEventListener('keydown', function(event) {
        const isNavbarExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';

        // Close navbar with Escape key
        if (event.key === 'Escape' && isNavbarExpanded) {
            navbarToggler.click();
            navbarToggler.focus(); // Return focus to toggle button
        }

        // Handle arrow key navigation within navbar
        if (isNavbarExpanded && (event.key === 'ArrowDown' || event.key === 'ArrowUp')) {
            const navLinks = navbarCollapse.querySelectorAll('.nav-link:not([disabled])');
            const currentIndex = Array.from(navLinks).indexOf(document.activeElement);

            if (currentIndex !== -1) {
                event.preventDefault();
                let nextIndex;

                if (event.key === 'ArrowDown') {
                    nextIndex = (currentIndex + 1) % navLinks.length;
                } else {
                    nextIndex = (currentIndex - 1 + navLinks.length) % navLinks.length;
                }

                navLinks[nextIndex].focus();
            }
        }
    });

    // Improve focus management when navbar opens/closes
    navbarToggler.addEventListener('click', function() {
        setTimeout(() => {
            const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
            if (isExpanded) {
                // Focus first nav link when navbar opens
                const firstNavLink = navbarCollapse.querySelector('.nav-link');
                if (firstNavLink) {
                    firstNavLink.focus();
                }
            }
        }, 100); // Small delay to ensure DOM is updated
    });
});