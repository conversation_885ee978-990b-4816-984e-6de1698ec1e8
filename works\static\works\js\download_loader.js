/**
 * Tempus Loader - Handles showing and hiding the custom loader animation
 * for download operations in the audiobook player.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Tempus Loader initialized');
    
    // Initialize the loader manager
    const loaderManager = {
        loaderElement: document.getElementById('tempusLoader'),
        isVisible: false,
        
        /**
         * Show the loader with optional custom message
         * @param {string} message - Optional custom message to display
         */
        showLoader: function(message) {
            if (this.loaderElement) {
                // Update message if provided
                if (message) {
                    const textElement = this.loaderElement.querySelector('.loader-text');
                    if (textElement) {
                        textElement.textContent = message;
                    }
                }
                
                // Show the loader
                this.loaderElement.style.display = 'flex';
                
                // Move the loader to be centered in the viewport
                this.loaderElement.classList.add('active');
                
                // Mark as visible
                this.isVisible = true;
                
                console.log('Loader shown with message:', message);
            } else {
                console.error('Tempus loader element not found');
            }
        },
        
        /**
         * Hide the loader
         */
        hideLoader: function() {
            if (this.loaderElement) {
                this.loaderElement.style.display = 'none';
                this.loaderElement.classList.remove('active');
                this.isVisible = false;
                console.log('Loader hidden');
            } else {
                console.error('Tempus loader element not found');
            }
        },
        
        /**
         * Check if the loader is currently visible
         * @returns {boolean} True if the loader is visible
         */
        isLoaderVisible: function() {
            return this.isVisible;
        }
    };
    
    // Attach to download buttons
    const downloadButtons = [
        document.getElementById('downloadCurrentChapter'),
        document.getElementById('downloadRemainingChapters'),
        document.getElementById('downloadEntireAudiobook')
    ];
    
    downloadButtons.forEach(button => {
        if (button) {
            button.addEventListener('click', function() {
                // Get the button text to customize the loader message
                const actionText = this.textContent.trim();
                loaderManager.showLoader(`Preparing ${actionText}...`);
                
                // The actual download logic is handled in other JS files
                console.log('Download button clicked:', actionText);
            });
        }
    });
    
    // Expose the loader manager globally so other scripts can use it
    window.tempusLoader = loaderManager;
    
    // Add a global event listener for the Escape key to hide the loader in case it gets stuck
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && window.tempusLoader && window.tempusLoader.isLoaderVisible()) {
            console.log('Escape key pressed, hiding loader');
            window.tempusLoader.hideLoader();
            
            // Also hide the overlay if it exists
            const overlay = document.getElementById('tempusOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }
    });
}); 