from django.core.exceptions import ValidationError
from django.conf import settings
import os

def validate_image(image):
    """
    Centralized image validation for the entire project.
    Validates:
    - File size (max 5MB by default)
    - File extension/type
    - Image dimensions (if specified)
    """
    # Get max size from settings or use default 10MB
    max_size = getattr(settings, 'MAX_IMAGE_SIZE', 10 * 1024 * 1024)
    allowed_extensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif']

    if not image:
        return

    # Check file size
    if image.size > max_size:
        raise ValidationError(
            f'Image size should not exceed {max_size/1024/1024:.1f}MB'
        )

    # Check file extension
    ext = os.path.splitext(image.name)[1].lower()
    if ext not in allowed_extensions:
        raise ValidationError(
            f'Invalid file type. Allowed types are: {", ".join(allowed_extensions)}'
        )

    # Check image dimensions if it's for the contest submission
    if hasattr(image, 'file') and hasattr(image, '_committed') and not image._committed:
        try:
            from PIL import Image as PILImage
            img = PILImage.open(image)
            width, height = img.size

            # For contest submissions, we recommend at least 1200px width
            # but we don't enforce it strictly
            if width < 1200 or height < 800:
                raise ValidationError(
                    f'For best quality, image should be at least 1200x800 pixels. '
                    f'Your image is {width}x{height} pixels.'
                )
        except ImportError:
            # If PIL is not available, skip dimension validation
            pass
        except Exception as e:
            # If there's an error reading the image, skip dimension validation
            pass
