(function($) {
    'use strict';
    
    // Clean up tag input on form submission
    $(document).ready(function() {
        $('form').on('submit', function() {
            var tagInput = $('.django-select2');
            var tags = tagInput.val();
            if (tags) {
                // Remove duplicates while preserving order
                var seen = new Set();
                tags = tags.filter(function(tag) {
                    var normalized = tag.toLowerCase();
                    if (!seen.has(normalized)) {
                        seen.add(normalized);
                        return true;
                    }
                    return false;
                }).map(function(tag) {
                    return tag.replace(/[\[\]'"]/g, '').trim();
                });
                tagInput.val(tags);
            }
        });
    });
})(jQuery);
