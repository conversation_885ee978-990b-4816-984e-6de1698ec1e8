from django import template
from django.template.defaultfilters import stringfilter

register = template.Library()

@register.filter
@stringfilter
def split(value, arg):
    """
    Split a string by the given delimiter and return the list.
    Usage: {{ value|split:"delimiter" }}
    """
    return value.split(arg)

@register.filter
@stringfilter
def prepend(value, arg):
    """
    Prepend a string to the given value.
    Usage: {{ value|prepend:"prefix" }}
    """
    return str(arg) + str(value)

@register.filter
def get_item(value, index):
    """
    Get an item from a list by index.
    Usage: {{ value|get_item:index }}
    """
    try:
        return value[index]
    except (IndexError, TypeError):
        return ''
