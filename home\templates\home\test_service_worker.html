{% extends "base.html" %}
{% load static %}

{% block extra_title %} - Service Worker Test{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/sw_status.css' %}">
{% endblock %}

{% block page_header %}
<div class="container header-container">
    <div class="row">
        <div class="col-12 text-center">
            <h1 class="display-4 text-black">Service Worker Test</h1>
            <hr class="w-50 mb-1">
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12 col-md-8 offset-md-2">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h2 class="card-title h4 mb-0">Enhanced Service Worker Test</h2>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        This page tests the enhanced service worker's capabilities including audio caching, 
                        offline page serving, and cache management.
                    </p>
                    
                    <!-- This is the element that the ServiceWorkerManager will look for -->
                    <div id="sw-status" class="sw-status mb-4">
                        <div class="sw-status-header">
                            <span class="sw-status-title">Offline Status</span>
                            <button class="sw-status-toggle" aria-label="Toggle offline status panel">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                        <div class="sw-status-content">
                            <div class="sw-status-info">
                                <div class="sw-status-loading">Checking status...</div>
                                <div class="sw-status-details" style="display: none;">
                                    <!-- Status details will be populated by the ServiceWorkerManager -->
                                </div>
                            </div>
                            <div class="sw-status-actions">
                                <button id="sw-refresh-status" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-sync-alt"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Our custom status display for the test page -->
                    <div class="alert alert-info" id="test-sw-status">
                        Checking service worker status...
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="h5">Test Audio File</h3>
                        <audio controls class="w-100" id="test-audio">
                            <source src="{% static 'audio/test-audio.mp3' %}" type="audio/mpeg">
                            Your browser does not support the audio element.
                        </audio>
                        <small class="text-muted">Play this audio file once to cache it for offline use.</small>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="h5">Service Worker Tests</h3>
                        <div class="list-group">
                            <a href="/offline/" class="list-group-item list-group-item-action" target="_blank">
                                <i class="fas fa-wifi-slash"></i> Test Offline Page
                            </a>
                            <button id="test-cache-status" class="list-group-item list-group-item-action">
                                <i class="fas fa-database"></i> Test Cache Status
                            </button>
                            <button id="test-clear-audio" class="list-group-item list-group-item-action">
                                <i class="fas fa-trash"></i> Test Clear Audio Cache
                            </button>
                            <button id="test-offline-mode" class="list-group-item list-group-item-action">
                                <i class="fas fa-plane"></i> Simulate Offline Mode
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="h5">Test Results</h3>
                        <div class="card">
                            <div class="card-body bg-light">
                                <pre id="test-results" class="mb-0" style="max-height: 200px; overflow-y: auto;">No tests run yet.</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const statusElement = document.getElementById('test-sw-status');
        const resultsElement = document.getElementById('test-results');
        
        // Log to the test results area
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            resultsElement.textContent += `\n[${timestamp}] ${message}`;
            resultsElement.scrollTop = resultsElement.scrollHeight;
        }
        
        // Check service worker status
        function checkServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistration().then(function(registration) {
                    if (registration) {
                        statusElement.className = 'alert alert-success';
                        statusElement.innerHTML = `
                            <strong>Service Worker Active!</strong><br>
                            Scope: ${registration.scope}<br>
                            State: ${registration.active ? 'Active' : registration.installing ? 'Installing' : 'Waiting'}
                        `;
                        log('Service worker is registered and active.');
                    } else {
                        statusElement.className = 'alert alert-warning';
                        statusElement.textContent = 'Service Worker is not registered.';
                        log('Service worker is not registered.');
                    }
                }).catch(function(error) {
                    statusElement.className = 'alert alert-danger';
                    statusElement.textContent = 'Error checking Service Worker: ' + error;
                    log('Error checking service worker: ' + error);
                });
            } else {
                statusElement.className = 'alert alert-danger';
                statusElement.textContent = 'Service Workers are not supported in this browser.';
                log('Service Workers are not supported in this browser.');
            }
        }
        
        // Test cache status
        document.getElementById('test-cache-status').addEventListener('click', function() {
            log('Requesting cache status...');
            
            if (window.swManager) {
                window.swManager.requestCacheStatus();
                log('Cache status request sent to service worker.');
                
                // Listen for the response
                const messageHandler = function(event) {
                    if (event.data && event.data.type === 'CACHE_STATUS') {
                        log(`Cache status received: 
                            Audio files: ${event.data.audioFiles}
                            Static files: ${event.data.staticFiles}
                            HTML files: ${event.data.htmlFiles}
                            Storage: ${event.data.storage.used} / ${event.data.storage.total} (${event.data.storage.percent}%)
                            Version: ${event.data.version}`);
                        
                        // Remove the listener after receiving the response
                        navigator.serviceWorker.removeEventListener('message', messageHandler);
                    }
                };
                
                navigator.serviceWorker.addEventListener('message', messageHandler);
                
                // Set a timeout to remove the listener if no response is received
                setTimeout(function() {
                    navigator.serviceWorker.removeEventListener('message', messageHandler);
                    log('No cache status response received within 5 seconds.');
                }, 5000);
            } else {
                log('Service Worker Manager not available.');
            }
        });
        
        // Test clear audio cache
        document.getElementById('test-clear-audio').addEventListener('click', function() {
            log('Clearing audio cache...');
            
            if (window.swManager) {
                // Override the confirm dialog for testing
                const originalConfirm = window.confirm;
                window.confirm = function() { return true; };
                
                window.swManager.clearCache('audio');
                log('Audio cache clear request sent to service worker.');
                
                // Restore the original confirm function
                window.confirm = originalConfirm;
                
                // Listen for the response
                const messageHandler = function(event) {
                    if (event.data && event.data.type === 'CACHE_CLEARED') {
                        log(`Cache cleared: ${event.data.cacheType}`);
                        
                        // Remove the listener after receiving the response
                        navigator.serviceWorker.removeEventListener('message', messageHandler);
                    }
                };
                
                navigator.serviceWorker.addEventListener('message', messageHandler);
                
                // Set a timeout to remove the listener if no response is received
                setTimeout(function() {
                    navigator.serviceWorker.removeEventListener('message', messageHandler);
                    log('No cache clear response received within 5 seconds.');
                }, 5000);
            } else {
                log('Service Worker Manager not available.');
            }
        });
        
        // Test offline mode
        document.getElementById('test-offline-mode').addEventListener('click', function() {
            log('Simulating offline mode...');
            
            // Create a custom offline event
            const offlineEvent = new Event('offline');
            window.dispatchEvent(offlineEvent);
            
            log('Offline event dispatched. Check if the offline indicator appears.');
            
            // After 5 seconds, simulate coming back online
            setTimeout(function() {
                log('Simulating online mode...');
                const onlineEvent = new Event('online');
                window.dispatchEvent(onlineEvent);
                log('Online event dispatched. Check if the offline indicator disappears.');
            }, 5000);
        });
        
        // Check service worker on page load
        checkServiceWorker();
        
        // Log initial message
        log('Test page loaded. Ready to run tests.');
    });
</script>
{% endblock %} 