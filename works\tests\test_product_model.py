from django.test import TestCase
from works.models import Product


class ProductModelTest(TestCase):
    """Test cases for the Product model."""

    def setUp(self):
        """Set up test data."""
        self.product_with_ebook = Product.objects.create(
            name="Test Ebook",
            description="A test ebook",
            price=9.99,
            ebook_file="test.pdf"
        )
        
        self.product_with_audiobook = Product.objects.create(
            name="Test Audiobook",
            description="A test audiobook",
            price=14.99,
            audiobook_link="https://example.com/audio.mp3"
        )
        
        self.product_with_both = Product.objects.create(
            name="Test Both",
            description="A test product with both ebook and audiobook",
            price=19.99,
            ebook_file="test_both.pdf",
            audiobook_link="https://example.com/audio_both.mp3"
        )
        
        self.product_without_digital = Product.objects.create(
            name="Test No Digital",
            description="A test product without digital content",
            price=29.99
        )

    def test_is_digital_property(self):
        """Test the is_digital property."""
        self.assertTrue(self.product_with_ebook.is_digital)
        self.assertTrue(self.product_with_audiobook.is_digital)
        self.assertTrue(self.product_with_both.is_digital)
        self.assertFalse(self.product_without_digital.is_digital)

    def test_has_digital_content_method(self):
        """Test the has_digital_content method."""
        self.assertTrue(self.product_with_ebook.has_digital_content())
        self.assertTrue(self.product_with_audiobook.has_digital_content())
        self.assertTrue(self.product_with_both.has_digital_content())
        self.assertFalse(self.product_without_digital.has_digital_content())
