from django.core.management.base import BaseCommand
from checkout.models import Order
from checkout.signals import send_ebook_on_payment_confirmation


class Command(BaseCommand):
    help = 'Manually test ebook delivery for a specific order'

    def add_arguments(self, parser):
        parser.add_argument('order_id', type=int, help='The ID of the order to test')

    def handle(self, *args, **options):
        order_id = options['order_id']
        
        try:
            order = Order.objects.get(id=order_id)
            self.stdout.write(self.style.SUCCESS(f'Found order {order_id} with payment status: {order.payment_status}'))
            
            # Manually trigger the signal function
            self.stdout.write(self.style.SUCCESS(f'Manually triggering ebook delivery for order {order_id}...'))
            send_ebook_on_payment_confirmation(sender=Order, instance=order, created=False)
            
            self.stdout.write(self.style.SUCCESS('Ebook delivery function executed. Check console output for details.'))
            
        except Order.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Order with ID {order_id} not found!'))
