from decimal import Decimal
from django.shortcuts import get_object_or_404
from works.models import Product


def bookcart_contents(request):
    """
    Context processor for book cart data.

    Makes book cart information available to all templates, including:
    - List of cart items
    - Total price of all items
    - Total number of items in cart
    """
    bookcart = request.session.get('bookcart', {})

    total = Decimal('0.00')
    product_count = len(bookcart)
    bookcart_items = []

    for item_id in bookcart:
        book = get_object_or_404(Product, pk=item_id)
        total += book.price
        bookcart_items.append({
            'item_id': item_id,
            'price': book.price,
            'product': book,
            'subtotal': book.price
        })

    context = {
        'bookcart': bookcart,
        'bookcart_items': bookcart_items,
        'total': total,
        'product_count': product_count,
    }

    return context
