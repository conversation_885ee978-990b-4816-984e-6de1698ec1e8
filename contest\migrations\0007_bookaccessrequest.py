# Generated by Django 5.1.5 on 2025-04-27 15:37

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contest', '0006_contest_results_date'),
        ('works', '0014_fix_download_link_expiry_days'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BookAccessRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('format_type', models.CharField(choices=[('ebook', 'E-book'), ('audiobook', 'Audiobook')], max_length=20)),
                ('access_type', models.CharField(choices=[('free', 'Free Access'), ('paid', 'Paid Access')], max_length=10)),
                ('reason', models.TextField(blank=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected')], default='pending', max_length=10)),
                ('access_granted', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('book_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contest_access_requests', to='works.product')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='book_access_requests', to='contest.category')),
                ('contest', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='book_access_requests', to='contest.contest')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='book_access_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'unique_together': {('user', 'book_part')},
            },
        ),
    ]
