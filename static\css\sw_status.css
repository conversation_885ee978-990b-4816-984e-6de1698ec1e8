/**
 * Service Worker Status Styles
 */

.sw-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 300px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    overflow: hidden;
    transition: all 0.3s ease;
    max-height: 40px;
}

.sw-status.expanded {
    max-height: 400px;
}

.sw-status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #007bff;
    color: white;
    cursor: pointer;
}

.sw-status-title {
    font-weight: bold;
    font-size: 14px;
}

.sw-status-toggle {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sw-status.expanded .sw-status-toggle i {
    transform: rotate(180deg);
}

.sw-status-content {
    padding: 15px;
}

.sw-status-loading {
    text-align: center;
    padding: 10px;
    color: #666;
    font-style: italic;
}

.sw-status-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.sw-status-label {
    font-weight: bold;
    color: #555;
}

.sw-status-value {
    color: #007bff;
}

.sw-status-storage-bar {
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    margin: 8px 0 15px;
    overflow: hidden;
}

.sw-status-storage-progress {
    height: 100%;
    background-color: #28a745;
    border-radius: 3px;
    transition: width 0.3s ease;
}

.sw-status-version {
    font-size: 12px;
    color: #6c757d;
    text-align: right;
    margin-top: 5px;
    margin-bottom: 10px;
}

.sw-status-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.sw-status-button {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.sw-status-button:hover {
    background-color: #e9ecef;
}

.sw-status-button i {
    margin-right: 5px;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .sw-status {
        width: calc(100% - 40px);
        bottom: 10px;
        right: 10px;
        left: 10px;
    }
} 