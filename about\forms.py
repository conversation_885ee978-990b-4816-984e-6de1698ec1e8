from django import forms
from .models import CollaborationRequest
from works.models import Product


class CollaborationForm(forms.ModelForm):
    """
    Form for handling collaboration requests.

    This form provides a user interface for submitting collaboration requests.
    It includes custom styling for form fields and validation for name and
    message fields.
    """

    name = forms.CharField(
        label='Name',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Your Full Name'
        })
    )
    email = forms.EmailField(
        label='Email',
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '<EMAIL>'
        })
    )
    subject = forms.CharField(
        label='Subject',
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Brief subject of your request'
        })
    )
    collaboration_type = forms.ChoiceField(
        label='Collaboration Type',
        choices=CollaborationRequest.COLLABORATION_TYPES,
        widget=forms.Select(attrs={
            'class': 'form-control',
            'id': 'id_collaboration_type'
        })
    )
    
    message = forms.CharField(
        label='Message',
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'placeholder': 'Tell me about your project or idea...',
            'rows': 4,
            'id': 'id_message'
        })
    )

    # Fields for book reviews
    book_id = forms.ModelChoiceField(
        label='Select Book',
        queryset=Product.objects.all(),
        required=False,
        empty_label="Select a book to review",
        widget=forms.Select(attrs={
            'class': 'form-control',
            'id': 'id_book_id'
        })
    )
    
    rating = forms.ChoiceField(
        label='Book Rating',
        required=False,
        choices=[(str(i), str(i)) for i in range(1, 6)],
        widget=forms.RadioSelect(attrs={
            'class': 'rating-input',
            'id': 'id_rating'
        })
    )

    class Meta:
        """
        Meta class for CollaborationForm.

        Specifies the model to use and configures form fields and widgets.
        """
        model = CollaborationRequest
        fields = ['name', 'email', 'subject', 'collaboration_type', 'message']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Your Full Name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '<EMAIL>'
            }),
            'subject': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Brief subject of your request'
            }),
            'collaboration_type': forms.Select(attrs={
                'class': 'form-control'
            }),
            'message': forms.Textarea(attrs={
                'class': 'form-control',
                'placeholder': 'Tell me about your project or idea...',
                'rows': 4
            })
        }

    def clean_name(self):
        """
        Custom validation for the name field.

        Ensures that the name field does not contain any numbers.

        Returns:
            str: The cleaned name value.

        Raises:
            ValidationError: If the name contains numbers.
        """
        name = self.cleaned_data.get('name', '').strip()
        if any(char.isdigit() for char in name):
            raise forms.ValidationError("Name should not contain numbers.")
        return name

    def clean_message(self):
        """
        Custom validation for the message field.

        Ensures that the message is at least 10 characters long.

        Returns:
            str: The cleaned message value.

        Raises:
            ValidationError: If the message is shorter than 10 characters.
        """
        message = self.cleaned_data.get('message', '').strip()
        if len(message) < 10:
            raise forms.ValidationError(
                "Message must be at least 10 characters long."
            )
        return message

    def clean(self):
        """
        Custom form validation.
        
        Ensures that rating and book_id are provided when collaboration_type 
        is 'REVIEW'.
        """
        cleaned_data = super().clean()
        collaboration_type = cleaned_data.get('collaboration_type')
        rating = cleaned_data.get('rating')
        book_id = cleaned_data.get('book_id')
        
        if collaboration_type == 'REVIEW':
            if not rating:
                self.add_error(
                    'rating', 
                    'Please provide a rating for the book review.'
                )
            if not book_id:
                self.add_error(
                    'book_id', 
                    'Please select which book you are reviewing.'
                )
                
        return cleaned_data

    def save(self, commit=True):
        """
        Custom save method to include rating and book title in the message 
        for reviews.
        """
        instance = super().save(commit=False)
        
        # If this is a book review, format the message to include rating and book
        if self.cleaned_data.get('collaboration_type') == 'REVIEW':
            rating = self.cleaned_data.get('rating')
            book = self.cleaned_data.get('book_id')
            original_message = self.cleaned_data.get('message', '')
            
            if book:
                formatted_message = (
                    f"BOOK REVIEW\n\n"
                    f"Book: {book.name}\n"
                    f"Book ID: {book.id}\n"
                    f"Rating: {rating}/5\n\n"
                    f"Review:\n{original_message}"
                )
                instance.message = formatted_message
            
        if commit:
            instance.save()
            
        return instance
