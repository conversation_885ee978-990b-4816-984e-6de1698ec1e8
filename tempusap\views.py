import os
import logging
from django.http import HttpResponse
from django.conf import settings
from django.views.decorators.cache import cache_control

logger = logging.getLogger(__name__)


@cache_control(max_age=0, no_cache=True, no_store=True, must_revalidate=True)
def service_worker(request):
    """
    Serve the service worker file with appropriate headers.
    
    The service worker needs to be served from the root of the domain
    to have the broadest scope. We disable caching to ensure
    updates are applied immediately.
    """
    path = os.path.join(settings.BASE_DIR, 'serviceworker.js')
    logger.info(f"Attempting to serve service worker from: {path}")
    
    try:
        if not os.path.exists(path):
            logger.error(f"Service worker file not found at: {path}")
            return HttpResponse(
                "Service worker file not found.",
                content_type='text/plain',
                status=404
            )
            
        with open(path, 'r', encoding='utf-8') as sw_file:
            content = sw_file.read()
        
        response = HttpResponse(content, content_type='application/javascript')
        # Add additional headers to ensure proper service worker handling
        response['Service-Worker-Allowed'] = '/'
        response['X-Content-Type-Options'] = 'nosniff'
        logger.info("Service worker served successfully")
        return response
    except FileNotFoundError:
        logger.error(f"Service worker file not found at: {path}")
        return HttpResponse(
            "Service worker file not found.",
            content_type='text/plain',
            status=404
        )
    except Exception as e:
        logger.error(f"Error serving service worker: {str(e)}")
        return HttpResponse(
            f"Error serving service worker: {str(e)}",
            content_type='text/plain',
            status=500
        )


@cache_control(max_age=86400)  # Cache for 1 day
def manifest(request):
    """
    Serve the web app manifest file.
    
    The manifest provides metadata for the web app to make it installable.
    """
    # First try the static directory in BASE_DIR
    path = os.path.join(settings.BASE_DIR, 'static', 'manifest.json')
    
    # If not found, try STATIC_ROOT
    if not os.path.exists(path) and hasattr(settings, 'STATIC_ROOT') and settings.STATIC_ROOT:
        path = os.path.join(settings.STATIC_ROOT, 'manifest.json')
    
    # If still not found, try STATICFILES_DIRS
    if not os.path.exists(path) and hasattr(settings, 'STATICFILES_DIRS'):
        for static_dir in settings.STATICFILES_DIRS:
            alt_path = os.path.join(static_dir, 'manifest.json')
            if os.path.exists(alt_path):
                path = alt_path
                break
    
    logger.info(f"Attempting to serve manifest from: {path}")
    
    try:
        if not os.path.exists(path):
            logger.error(f"Manifest file not found at: {path}")
            return HttpResponse(
                "Manifest file not found.",
                content_type='text/plain',
                status=404
            )
            
        with open(path, 'r', encoding='utf-8') as manifest_file:
            content = manifest_file.read()
        
        response = HttpResponse(
            content, content_type='application/manifest+json'
        )
        logger.info("Manifest served successfully")
        return response
    except FileNotFoundError:
        logger.error(f"Manifest file not found at: {path}")
        return HttpResponse(
            "Manifest file not found.",
            content_type='text/plain',
            status=404
        )
    except Exception as e:
        logger.error(f"Error serving manifest: {str(e)}")
        return HttpResponse(
            f"Error serving manifest: {str(e)}",
            content_type='text/plain',
            status=500
        )


@cache_control(max_age=86400)  # Cache for 1 day
def offline_page(request):
    """
    Serve the offline page.
    
    This page is shown when a user is offline and tries to access a page
    that isn't cached.
    """
    path = os.path.join(settings.BASE_DIR, 'static', 'offline.html')
    
    logger.info(f"Attempting to serve offline page from: {path}")
    
    try:
        if not os.path.exists(path):
            logger.error(f"Offline page not found at: {path}")
            return HttpResponse(
                "You are offline and the requested page is not available.",
                content_type='text/plain',
                status=503
            )
            
        with open(path, 'r', encoding='utf-8') as offline_file:
            content = offline_file.read()
        
        response = HttpResponse(content, content_type='text/html')
        logger.info("Offline page served successfully")
        return response
    except FileNotFoundError:
        logger.error(f"Offline page not found at: {path}")
        return HttpResponse(
            "You are offline and the requested page is not available.",
            content_type='text/plain',
            status=503
        )
    except Exception as e:
        logger.error(f"Error serving offline page: {str(e)}")
        return HttpResponse(
            "Error serving offline page.",
            content_type='text/plain',
            status=500
        ) 