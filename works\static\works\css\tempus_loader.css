/* Tempus Loader Overlay */
.tempus-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    backdrop-filter: blur(3px);
    cursor: not-allowed;
    pointer-events: all !important;
}

/* Tempus Loader */
.tempus-loader {
    padding: 20px;
    border-radius: 10px;
    background-color: rgba(33, 37, 41, 0.9);
    max-width: 300px;
    margin: 0 auto;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.tempus-loader-gif {
    max-width: 180px;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.loader-text {
    color: #f8f9fa;
    font-style: italic;
    font-size: 16px;
    margin-top: 15px;
    text-align: center;
    font-weight: 300;
    letter-spacing: 0.5px;
} 