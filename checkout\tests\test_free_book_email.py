from django.test import TestCase
from django.contrib.auth.models import User
from works.models import Product
from checkout.models import Order, OrderItem
from checkout.free_book_email import send_free_book_email


class FreeBookEmailTest(TestCase):
    """Test cases for the free book email functionality."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create products with different digital content
        self.product_with_ebook = Product.objects.create(
            name="Test Ebook",
            description="A test ebook",
            price=9.99,
            ebook_file="test.pdf"
        )
        
        self.product_with_audiobook = Product.objects.create(
            name="Test Audiobook",
            description="A test audiobook",
            price=14.99,
            audiobook_link="https://example.com/audio.mp3"
        )
        
        self.product_without_digital = Product.objects.create(
            name="Test No Digital",
            description="A test product without digital content",
            price=29.99
        )
        
        # Create orders
        self.order_with_ebook = Order.objects.create(
            user=self.user,
            full_name='Test User',
            email='<EMAIL>',
            phone_number='1234567890',
            payment_status='paid',
            email_delivery_status='pending'
        )
        
        self.order_with_audiobook = Order.objects.create(
            user=self.user,
            full_name='Test User',
            email='<EMAIL>',
            phone_number='1234567890',
            payment_status='paid',
            email_delivery_status='pending'
        )
        
        self.order_without_digital = Order.objects.create(
            user=self.user,
            full_name='Test User',
            email='<EMAIL>',
            phone_number='1234567890',
            payment_status='paid',
            email_delivery_status='pending'
        )
        
        # Create order items
        OrderItem.objects.create(
            order=self.order_with_ebook,
            product=self.product_with_ebook,
            quantity=1,
            price=self.product_with_ebook.price
        )
        
        OrderItem.objects.create(
            order=self.order_with_audiobook,
            product=self.product_with_audiobook,
            quantity=1,
            price=self.product_with_audiobook.price
        )
        
        OrderItem.objects.create(
            order=self.order_without_digital,
            product=self.product_without_digital,
            quantity=1,
            price=self.product_without_digital.price
        )

    def test_send_free_book_email_with_ebook(self):
        """Test sending free book email with ebook content."""
        # This test will not actually send an email but will check if the function runs without errors
        result = send_free_book_email(self.order_with_ebook)
        self.order_with_ebook.refresh_from_db()
        
        # The function should return False in test environment since no actual email is sent
        # but the email_delivery_status should be updated
        self.assertNotEqual(self.order_with_ebook.email_delivery_status, 'failed')

    def test_send_free_book_email_with_audiobook(self):
        """Test sending free book email with audiobook content."""
        # This test will not actually send an email but will check if the function runs without errors
        result = send_free_book_email(self.order_with_audiobook)
        self.order_with_audiobook.refresh_from_db()
        
        # The function should return False in test environment since no actual email is sent
        # but the email_delivery_status should be updated
        self.assertNotEqual(self.order_with_audiobook.email_delivery_status, 'failed')

    def test_send_free_book_email_without_digital(self):
        """Test sending free book email without digital content."""
        # This should fail because there's no digital content
        result = send_free_book_email(self.order_without_digital)
        self.order_without_digital.refresh_from_db()
        
        # The function should return False and the email_delivery_status should be 'failed'
        self.assertEqual(self.order_without_digital.email_delivery_status, 'failed')
