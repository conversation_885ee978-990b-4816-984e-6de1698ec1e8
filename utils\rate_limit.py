from django.core.cache import cache
from django.http import HttpResponseForbidden
from functools import wraps
from django.conf import settings
import time

def rate_limit(key_prefix, limit=5, period=60):
    """
    Rate limiting decorator
    :param key_prefix: Prefix for the cache key
    :param limit: Number of allowed requests per period
    :param period: Time period in seconds
    """
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped_view(request, *args, **kwargs):
            # Create a unique cache key based on the user's IP and the view
            if request.user.is_authenticated:
                key = f"rate_limit:{key_prefix}:{request.user.id}"
            else:
                key = f"rate_limit:{key_prefix}:{request.META.get('REMOTE_ADDR')}"
            
            # Get the current request count and timestamp
            request_history = cache.get(key, {"count": 0, "reset_time": time.time()})
            
            # Reset count if period has passed
            if time.time() - request_history["reset_time"] > period:
                request_history = {"count": 0, "reset_time": time.time()}
            
            # Increment request count
            request_history["count"] += 1
            
            # Check if limit is exceeded
            if request_history["count"] > limit:
                return HttpResponseForbidden(
                    "Rate limit exceeded. Please try again later."
                )
            
            # Update cache
            cache.set(key, request_history, timeout=period)
            
            return view_func(request, *args, **kwargs)
        return _wrapped_view
    return decorator
