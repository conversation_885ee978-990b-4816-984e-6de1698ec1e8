/* Latest Updates Section Cards */
.update-card {
    max-height: 420px;
}

.image-container {
    height: 200px;
    overflow: hidden;
}

.image-container img {
    object-fit: cover;
    height: 100%;
    width: 100%;
}

.card-body {
    display: flex;
    flex-direction: column;
}

.card-text {
    font-size: small;
}

.button-container {
    margin-top: auto;
}

h3 {
    color: green;
}

.highlight {
    background-color: #ffeb3b;
    padding: 0 2px;
    border-radius: 2px;
}

/* Add consistent hover effect for all blog and work card links */
.blog-link {
    color: #000;
    transition: color 0.2s ease-in-out;
}

.blog-link:hover {
    color: #0d6efd !important;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-10px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.work-image-container {
    overflow: hidden;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.work-image-container img {
    width: auto;
    height: 100%;
    object-fit: cover;
}

.blog-image-container {
    overflow: hidden;
    background-color: #f8f9fa;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.blog-image-container img {
    width: auto;
    height: 100%;
    object-fit: cover;
}

.image-wrapper {
    background-color: black;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 200px;
}

.image-wrapper img {
    width: auto;
    height: 200px;
    object-fit: cover;
}