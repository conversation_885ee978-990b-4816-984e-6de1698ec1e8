/**
 * Audio Player Analytics
 * Tracks user listening behavior without modifying existing player code
 */
class AudioPlayerAnalytics {
  constructor() {
    // Check if we're on the audio player page
    const audioPlayerContainer = document.getElementById('audioPlayerContainer');
    if (!audioPlayerContainer) {
      console.log('Not on audio player page, analytics disabled');
      return; // Exit early if not on audio player page
    }
    
    // Analytics data
    this.sessions = [];
    this.currentSession = null;
    this.events = [];
    this.userId = this.getUserId();
    this.workId = this.getWorkId();
    
    // Configuration
    this.sendInterval = 60000; // Send data every minute
    this.inactivityThreshold = 300000; // 5 minutes of inactivity = new session
    this.storageKey = 'audio_analytics_data';
    this.endpointUrl = '/api/analytics/audio/'; // Your backend endpoint
    this.lastPosition = 0;
    this.lastChapter = null;
    
    // Initialize
    this.init();
  }
  
  /**
   * Initialize the analytics module
   */
  init() {
    console.log('Initializing Audio Player Analytics');
    
    // Load existing data from localStorage
    this.loadData();
    
    // Start a new session
    this.startSession();
    
    // Set up event listeners
    this.setupEventListeners();
    
    // Set up periodic sending
    this.setupPeriodicSending();
    
    // Set up page visibility change detection
    this.setupVisibilityTracking();
    
    // Set up position tracking
    this.setupPositionTracking();
  }
  
  /**
   * Get the current user ID
   */
  getUserId() {
    // Try to get from a data attribute or cookie
    const userIdElement = document.querySelector('[data-user-id]');
    if (userIdElement && !isNaN(userIdElement.dataset.userId)) {
      return parseInt(userIdElement.dataset.userId, 10);
    }
    
    // Check if user is logged in by looking for username in the navbar
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown) {
      // For anonymous analytics, just use a timestamp as numeric ID
      return Date.now();
    }
    
    // Or from URL
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('user_id') && !isNaN(urlParams.get('user_id'))) {
      return parseInt(urlParams.get('user_id'), 10);
    }
    
    // Or generate an anonymous numeric ID
    let anonymousId = localStorage.getItem('anonymous_user_id');
    if (!anonymousId || isNaN(anonymousId)) {
      anonymousId = Date.now();
      localStorage.setItem('anonymous_user_id', anonymousId);
    }
    
    return parseInt(anonymousId, 10);
  }
  
  /**
   * Get the current work ID
   */
  getWorkId() {
    // Try to get from a data attribute
    const workIdElement = document.querySelector('[data-work-id]');
    if (workIdElement && !isNaN(workIdElement.dataset.workId)) {
      return parseInt(workIdElement.dataset.workId, 10);
    }
    
    // Try to get from product-id attribute (used in audio_player.html)
    const productIdElement = document.querySelector('[data-product-id]');
    if (productIdElement && !isNaN(productIdElement.dataset.productId)) {
      return parseInt(productIdElement.dataset.productId, 10);
    }
    
    // Or from URL path
    const pathParts = window.location.pathname.split('/');
    const worksIndex = pathParts.indexOf('works');
    if (worksIndex !== -1 && pathParts.length > worksIndex + 1) {
      const workId = pathParts[worksIndex + 1];
      if (!isNaN(workId)) {
        return parseInt(workId, 10);
      }
    }
    
    // Or from URL parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('work_id') && !isNaN(urlParams.get('work_id'))) {
      return parseInt(urlParams.get('work_id'), 10);
    }
    
    // Default to 1 if no valid work ID is found
    return 1;
  }
  
  /**
   * Set up event listeners for player interactions
   */
  setupEventListeners() {
    const audioPlayerContainer = document.getElementById('audioPlayerContainer');
    if (!audioPlayerContainer) {
      return; // Exit if not on audio player page
    }
    
    // Listen for clicks on the player
    if (audioPlayerContainer) {
      audioPlayerContainer.addEventListener('click', (event) => {
        // Determine what was clicked
        const target = event.target;
        
        // Play/pause button
        if (target.closest('.play-button') || target.closest('.pause-button')) {
          const isPlaying = target.closest('.pause-button') !== null;
          this.trackEvent(isPlaying ? 'pause' : 'play');
        }
        
        // Skip buttons
        if (target.closest('.skip-back')) {
          this.trackEvent('skip_back');
        }
        if (target.closest('.skip-forward')) {
          this.trackEvent('skip_forward');
        }
      });
    }
    
    // Track progress bar interactions
    const progressContainer = audioPlayerContainer.querySelector('.progress-container');
    if (progressContainer) {
      progressContainer.addEventListener('click', () => {
        this.trackEvent('seek');
      });
    }
    
    // Track speed changes
    const speedSelector = audioPlayerContainer.querySelector('.speed-selector');
    if (speedSelector) {
      speedSelector.addEventListener('change', (event) => {
        this.trackEvent('speed_change', { speed: event.target.value });
      });
    }
    
    // Track chapter changes
    document.addEventListener('chapterChanged', (event) => {
      if (event.detail && event.detail.chapterIndex !== undefined) {
        this.trackEvent('chapter_change', { 
          chapterIndex: event.detail.chapterIndex,
          chapterTitle: event.detail.chapterTitle || ''
        });
        this.lastChapter = event.detail.chapterIndex;
      }
    });
    
    // Track when audio ends
    document.addEventListener('audioEnded', () => {
      // Mark as completed when audio ends
      if (this.currentSession) {
        this.currentSession.completed = true;
        this.currentSession.progressPercentage = 100;
      }
      
      this.trackEvent('completed', { 
        completed: true,
        progressPercentage: 100
      });
      
      this.endSession();
      this.startSession(); // Start a new session in case they replay
    });
  }
  
  /**
   * Set up position tracking
   */
  setupPositionTracking() {
    // Check if we're on the audio player page
    const audioPlayerContainer = document.getElementById('audioPlayerContainer');
    if (!audioPlayerContainer) {
      console.log('Not on audio player page, position tracking disabled');
      return; // Exit early if not on audio player page
    }
    
    // Check for position updates every second
    setInterval(() => {
      const position = this.getCurrentPlaybackPosition();
      if (position > 0 && position !== this.lastPosition) {
        this.lastPosition = position;
        
        // Update session data
        if (this.currentSession) {
          this.currentSession.lastPosition = position;
          this.currentSession.lastUpdate = Date.now();
          
          // Update progress percentage if we have total duration
          if (window.audiobookPlayer && window.audiobookPlayer.getDuration) {
            const totalDuration = window.audiobookPlayer.getDuration();
            if (totalDuration > 0) {
              this.currentSession.progressPercentage = Math.min(
                100, 
                Math.round((position / totalDuration) * 100)
              );
              
              // If we're at 99% or more, consider it completed
              if (this.currentSession.progressPercentage >= 99) {
                this.currentSession.completed = true;
              }
            }
          }
        }
      }
    }, 1000);
  }
  
  /**
   * Track playback position
   * Only records position periodically to avoid too many events
   */
  trackPlaybackPosition(currentTime) {
    // Only track every 10 seconds to avoid excessive data
    if (Math.floor(currentTime) % 10 === 0 && currentTime > 0) {
      // Calculate progress percentage if possible
      let progressPercentage = 0;
      if (window.audiobookPlayer && window.audiobookPlayer.getDuration) {
        const totalDuration = window.audiobookPlayer.getDuration();
        if (totalDuration > 0) {
          progressPercentage = Math.min(
            100, 
            Math.round((currentTime / totalDuration) * 100)
          );
        }
      }
      
      this.trackEvent('position_update', { 
        position: currentTime,
        chapter: this.lastChapter,
        progressPercentage: progressPercentage
      });
      
      // Update session data
      if (this.currentSession) {
        this.currentSession.lastPosition = currentTime;
        this.currentSession.lastUpdate = Date.now();
        this.currentSession.progressPercentage = progressPercentage;
        
        // If we're at 99% or more, consider it completed
        if (progressPercentage >= 99) {
          this.currentSession.completed = true;
        }
      }
    }
  }
  
  /**
   * Start a new listening session
   */
  startSession() {
    // End previous session if exists
    if (this.currentSession) {
      this.endSession();
    }
    
    // Create new session
    this.currentSession = {
      id: Date.now().toString(),
      userId: String(this.userId),  // Ensure userId is a string
      workId: String(this.workId),  // Ensure workId is a string
      startTime: Date.now(),
      endTime: null,
      duration: 0,
      initialPosition: this.getCurrentPlaybackPosition(),
      lastPosition: this.getCurrentPlaybackPosition(),
      lastUpdate: Date.now(),
      events: [],
      completed: false,  // Match the backend field name
      progressPercentage: 0  // Track listening progress percentage
    };
    
    console.log('Started new listening session', this.currentSession.id);
    
    // Track session start event
    this.trackEvent('session_start');
  }
  
  /**
   * End the current session
   */
  endSession() {
    if (!this.currentSession) return;
    
    // Update session data
    this.currentSession.endTime = Date.now();
    this.currentSession.duration = this.currentSession.endTime - this.currentSession.startTime;
    
    // Calculate progress percentage if we have total duration
    if (window.audiobookPlayer && window.audiobookPlayer.getDuration) {
      const totalDuration = window.audiobookPlayer.getDuration();
      if (totalDuration > 0) {
        this.currentSession.progressPercentage = Math.min(
          100, 
          Math.round((this.currentSession.lastPosition / totalDuration) * 100)
        );
      }
    }
    
    // Rename completed property to match backend expectation
    // The backend field is 'completed', not 'isCompleted'
    if (this.currentSession.hasOwnProperty('completed')) {
      // Already using the correct property name, keep as is
    } else if (this.currentSession.hasOwnProperty('isCompleted')) {
      // Copy from isCompleted if it exists
      this.currentSession.completed = this.currentSession.isCompleted;
      delete this.currentSession.isCompleted;
    } else {
      // Ensure the property exists
      this.currentSession.completed = false;
    }
    
    // Add to sessions array
    this.sessions.push(this.currentSession);
    
    // Track session end event
    this.trackEvent('session_end', {
      duration: this.currentSession.duration,
      sessionId: this.currentSession.id,
      completed: this.currentSession.completed,
      progressPercentage: this.currentSession.progressPercentage
    });
    
    console.log('Ended listening session', this.currentSession.id, 'Duration:', this.formatDuration(this.currentSession.duration));
    
    // Save data
    this.saveData();
    
    // Reset current session
    this.currentSession = null;
  }
  
  /**
   * Track an event
   */
  trackEvent(eventType, eventData = {}) {
    const position = this.getCurrentPlaybackPosition();
    const event = {
      id: Date.now().toString() + '_' + Math.random().toString(36).substr(2, 5),
      type: eventType,
      timestamp: Date.now(),
      sessionId: this.currentSession ? this.currentSession.id : null,
      userId: String(this.userId),  // Ensure userId is a string
      workId: String(this.workId),  // Ensure workId is a string
      data: eventData,
      url: window.location.href,
      playbackPosition: position,
      chapter: this.lastChapter
    };
    
    // Add to events array
    this.events.push(event);
    
    // Add to current session events if we have a session
    if (this.currentSession) {
      this.currentSession.events.push(event.id);
      this.currentSession.lastUpdate = Date.now();
    }
  }
  
  /**
   * Get current playback position
   */
  getCurrentPlaybackPosition() {
    // Try to get from audio element
    const audioElement = document.getElementById('audioElement');
    if (audioElement) {
      return audioElement.currentTime;
    }
    
    // Try to get from Howler if available
    if (window.audiobookPlayer && typeof window.audiobookPlayer.getCurrentTime === 'function') {
      return window.audiobookPlayer.getCurrentTime();
    }
    
    // Try to get from progress display
    const currentTimeElement = document.getElementById('currentTime');
    if (currentTimeElement) {
      const timeText = currentTimeElement.textContent;
      return this.parseTimeToSeconds(timeText);
    }
    
    // Try to get chapter information
    if (window.audiobookPlayer && window.audiobookPlayer.currentChapter) {
      this.lastChapter = {
        chapterNumber: window.audiobookPlayer.currentChapter.chapterNumber,
        partNumber: window.audiobookPlayer.currentChapter.partNumber
      };
    }
    
    return this.lastPosition || 0;
  }
  
  /**
   * Parse time string (e.g. "1:23") to seconds
   */
  parseTimeToSeconds(timeString) {
    if (!timeString) return 0;
    
    const parts = timeString.split(':');
    if (parts.length === 2) {
      return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    } else if (parts.length === 3) {
      return parseInt(parts[0]) * 3600 + parseInt(parts[1]) * 60 + parseInt(parts[2]);
    }
    
    return 0;
  }
  
  /**
   * Format duration in milliseconds to human-readable string
   */
  formatDuration(duration) {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }
  
  /**
   * Set up periodic sending of analytics data
   */
  setupPeriodicSending() {
    // Check if we're on the audio player page
    const audioPlayerContainer = document.getElementById('audioPlayerContainer');
    if (!audioPlayerContainer) {
      console.log('Not on audio player page, periodic sending disabled');
      return; // Exit early if not on audio player page
    }
    
    setInterval(() => {
      // Check for inactivity
      if (this.currentSession) {
        const inactiveTime = Date.now() - this.currentSession.lastUpdate;
        if (inactiveTime > this.inactivityThreshold) {
          console.log('Detected inactivity, ending session');
          this.endSession();
          this.startSession();
        }
      }
      
      // Send data if we have any
      if (this.events.length > 0 || this.sessions.length > 0) {
        this.sendData();
      }
    }, this.sendInterval);
  }
  
  /**
   * Set up tracking for page visibility changes
   */
  setupVisibilityTracking() {
    // Check if we're on the audio player page
    const audioPlayerContainer = document.getElementById('audioPlayerContainer');
    if (!audioPlayerContainer) {
      console.log('Not on audio player page, visibility tracking disabled');
      return; // Exit early if not on audio player page
    }
    
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        // User left the page
        this.trackEvent('page_hidden');
        
        // End session if user leaves for too long
        this.visibilityChangeTime = Date.now();
      } else if (document.visibilityState === 'visible') {
        // User returned to the page
        this.trackEvent('page_visible');
        
        // Check if we need to start a new session
        if (this.visibilityChangeTime) {
          const timeAway = Date.now() - this.visibilityChangeTime;
          if (timeAway > this.inactivityThreshold) {
            console.log('User was away for too long, starting new session');
            this.endSession();
            this.startSession();
          }
        }
      }
    });
    
    // Handle page unload
    window.addEventListener('beforeunload', () => {
      this.trackEvent('page_unload');
      this.endSession();
      this.saveData();
      
      // Attempt to send data before page unloads
      this.sendDataSync();
    });
  }
  
  /**
   * Save analytics data to localStorage
   */
  saveData() {
    try {
      const data = {
        sessions: this.sessions,
        events: this.events,
        lastSaved: Date.now()
      };
      
      localStorage.setItem(this.storageKey, JSON.stringify(data));
    } catch (error) {
      console.error('Error saving analytics data:', error);
      
      // If localStorage is full, clear old events
      if (error.name === 'QuotaExceededError') {
        this.clearOldData();
        this.saveData();
      }
    }
  }
  
  /**
   * Load analytics data from localStorage
   */
  loadData() {
    try {
      const dataString = localStorage.getItem(this.storageKey);
      if (dataString) {
        const data = JSON.parse(dataString);
        this.sessions = data.sessions || [];
        this.events = data.events || [];
        console.log(`Loaded ${this.sessions.length} sessions and ${this.events.length} events from storage`);
      }
    } catch (error) {
      console.error('Error loading analytics data:', error);
      // Reset data if there's an error
      this.sessions = [];
      this.events = [];
    }
  }
  
  /**
   * Clear old data to free up localStorage space
   */
  clearOldData() {
    // Keep only the last 10 sessions
    if (this.sessions.length > 10) {
      this.sessions = this.sessions.slice(-10);
    }
    
    // Keep only the last 100 events
    if (this.events.length > 100) {
      this.events = this.events.slice(-100);
    }
    
    console.log('Cleared old analytics data to free up space');
  }
  
  /**
   * Send analytics data to the server
   */
  sendData() {
    // Don't send if there's no data
    if (this.events.length === 0 && this.sessions.length === 0) {
      return;
    }
    
    // Make a deep copy of sessions to ensure we don't alter the originals
    const sessionsToSend = this.sessions.map(session => {
      // Create a clean copy of the session with the exact fields the backend expects
      return {
        id: session.id,
        userId: session.userId,
        workId: session.workId,
        startTime: session.startTime,
        endTime: session.endTime,
        duration: session.duration,
        initialPosition: session.initialPosition,
        lastPosition: session.lastPosition,
        completed: session.completed !== undefined ? session.completed : false,
        progressPercentage: session.progressPercentage || 0
      };
    });
    
    // Prepare data to send
    const dataToSend = {
      userId: String(this.userId),  // Ensure userId is a string
      workId: String(this.workId),  // Ensure workId is a string
      sessions: sessionsToSend,
      events: [...this.events],
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    console.log('Sending analytics data:', dataToSend);
    
    // Send data to server
    fetch(this.endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.getCsrfToken()
      },
      body: JSON.stringify(dataToSend),
      credentials: 'same-origin'
    })
    .then(response => {
      if (response.ok) {
        console.log(`Successfully sent ${this.events.length} events and ${this.sessions.length} sessions`);
        
        // Clear sent data
        this.events = [];
        this.sessions = [];
        
        // Save updated state
        this.saveData();
      } else {
        // Try to get more detailed error information
        response.json().then(errorData => {
          console.error('Error sending analytics data:', errorData);
        }).catch(() => {
          console.error('Error sending analytics data:', response.statusText);
        });
      }
    })
    .catch(error => {
      console.error('Error sending analytics data:', error);
    });
  }
  
  /**
   * Send data synchronously before page unload
   */
  sendDataSync() {
    if (this.events.length === 0 && this.sessions.length === 0) {
      return;
    }
    
    // Use navigator.sendBeacon for reliable data sending during page unload
    if (navigator.sendBeacon) {
      // Ensure userId and workId are strings
      const userId = String(this.userId);
      const workId = String(this.workId);
      
      const dataToSend = {
        userId: userId,
        workId: workId,
        sessions: this.sessions,
        events: this.events,
        timestamp: Date.now(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };
      
      console.log('Sending analytics data synchronously before unload:', dataToSend);
      
      try {
        const blob = new Blob([JSON.stringify(dataToSend)], { type: 'application/json' });
        const success = navigator.sendBeacon(this.endpointUrl, blob);
        
        if (success) {
          console.log('Successfully sent analytics data before page unload');
          localStorage.removeItem(this.storageKey);
        } else {
          console.error('Failed to send analytics data before page unload');
        }
      } catch (error) {
        console.error('Error sending analytics data before page unload:', error);
      }
    }
  }
  
  /**
   * Get CSRF token from cookie
   */
  getCsrfToken() {
    const name = 'csrftoken';
    const cookieValue = document.cookie
      .split('; ')
      .find(row => row.startsWith(name + '='));
      
    if (cookieValue) {
      return cookieValue.split('=')[1];
    }
    
    // Try to get from a hidden input field
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (csrfInput) {
      return csrfInput.value;
    }
    
    return '';
  }
}

// Initialize the analytics when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Check if we're on the audio player page
  const audioPlayerContainer = document.getElementById('audioPlayerContainer');
  if (!audioPlayerContainer) {
    console.log('Not on audio player page, analytics initialization skipped');
    return; // Exit early if not on audio player page
  }
  
  // Initialize the analytics
  window.audioPlayerAnalytics = new AudioPlayerAnalytics();
}); 