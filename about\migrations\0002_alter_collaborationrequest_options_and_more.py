# Generated by Django 5.1.5 on 2025-02-03 12:16

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('about', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='collaborationrequest',
            options={'ordering': ['-created_at'], 'verbose_name': 'Collaboration Request', 'verbose_name_plural': 'Collaboration Requests'},
        ),
        migrations.AddField(
            model_name='collaborationrequest',
            name='is_read',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='collaborationrequest',
            name='status',
            field=models.CharField(choices=[('NEW', 'New'), ('READ', 'Read'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('DECLINED', 'Declined')], default='NEW', max_length=20),
        ),
        migrations.Add<PERSON>ield(
            model_name='collaborationrequest',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='collaborationrequest',
            name='collaboration_type',
            field=models.CharField(choices=[('PROJECT', 'Project Collaboration'), ('MENTORSHIP', 'Mentorship'), ('BUSINESS', 'Business Opportunity'), ('WRITING', 'Writing Partnership'), ('REVIEW', 'Book Review'), ('BETA', 'Beta Reading'), ('OTHER', 'Other')], default='PROJECT', max_length=20),
        ),
        migrations.AlterField(
            model_name='collaborationrequest',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, db_index=True),
        ),
        migrations.AlterField(
            model_name='collaborationrequest',
            name='email',
            field=models.EmailField(max_length=254, validators=[django.core.validators.EmailValidator()]),
        ),
        migrations.AlterField(
            model_name='collaborationrequest',
            name='message',
            field=models.TextField(validators=[django.core.validators.MinLengthValidator(10, 'Message must be at least 10 characters long')]),
        ),
        migrations.AlterField(
            model_name='collaborationrequest',
            name='name',
            field=models.CharField(max_length=100, validators=[django.core.validators.MinLengthValidator(2, 'Name must be at least 2 characters long')]),
        ),
        migrations.AddIndex(
            model_name='collaborationrequest',
            index=models.Index(fields=['created_at'], name='about_colla_created_146278_idx'),
        ),
        migrations.AddIndex(
            model_name='collaborationrequest',
            index=models.Index(fields=['status'], name='about_colla_status_cc2213_idx'),
        ),
        migrations.AddIndex(
            model_name='collaborationrequest',
            index=models.Index(fields=['is_read'], name='about_colla_is_read_26108b_idx'),
        ),
    ]
