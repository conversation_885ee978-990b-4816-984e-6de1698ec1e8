from django.urls import path
from . import views

app_name = 'newsletter'

urlpatterns = [
    path('subscribe/', views.SubscribeView.as_view(), name='subscribe'),
    path('subscribe/success/', views.SubscribeSuccessView.as_view(), name='subscribe_success'),
    path('confirm/<uuid:token>/', views.confirm_subscription, name='confirm_subscription'),
    path('unsubscribe/<uuid:token>/', views.unsubscribe, name='unsubscribe'),
    path('preview/<int:pk>/', views.NewsletterPreviewView.as_view(), name='preview'),
    path('track/open/<uuid:token>/', views.track_open, name='track_open'),
    path('track/click/<uuid:token>/<str:link_id>/', views.track_click, name='track_click'),
    path('debug/scheduled/', views.debug_scheduled_newsletters, name='debug_scheduled'),
]
