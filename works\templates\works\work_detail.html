{% extends "base.html" %}
{% load static %}
{% load works_filters %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'works/css/work_details.css' %}">
{% endblock %}

{% block opengraph %}
<!-- Open Graph / Facebook -->
<meta property="og:type" content="book">
<meta property="og:url" content="{{ request.build_absolute_uri }}">
<meta property="og:title" content="{{ work.name }} | Tempus Author Platform">
<meta property="og:description" content="{{ work.description|truncatechars:200|striptags|safe }}">
<meta property="og:image" content="{% if work.image %}{{ MEDIA_URL_WITH_HOST }}{{ work.image.name }}{% else %}{{ STATIC_URL_WITH_HOST }}images/noimage.png{% endif %}">
<meta property="og:site_name" content="Tempus Author Platform">
<meta property="og:price:amount" content="{{ work.price }}">
<meta property="og:price:currency" content="USD">
<meta property="og:logo" content="{{ STATIC_URL_WITH_HOST }}images/logo.png">

<!-- Twitter -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:url" content="{{ request.build_absolute_uri }}">
<meta name="twitter:title" content="{{ work.name }} | Tempus Author Platform">
<meta name="twitter:description" content="{{ work.description|truncatechars:200|striptags|safe }}">
<meta name="twitter:image" content="{% if work.image %}{{ MEDIA_URL_WITH_HOST }}{{ work.image.name }}{% else %}{{ STATIC_URL_WITH_HOST }}images/noimage.png{% endif %}">
{% endblock %}

{% block extrahead %}
<link rel="canonical" href="{{ request.build_absolute_uri }}">

<script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Book",
        "name": "{{ work.name }}",
        "author": "Rafal Zygula",
        "image": "{% if work.image %}{{ MEDIA_URL_WITH_HOST }}{{ work.image.name }}{% else %}{{ STATIC_URL_WITH_HOST }}images/noimage.png{% endif %}",
        "description": "{{ work.description }}"
    }
    </script>
{% endblock %}

{% block content %}
<div id="productDetailPage" class="container my-5">
    <div class="row d-flex align-items-stretch mb-4">
        <!-- Image Column -->
        <div class="col-12 col-md-6 col-lg-4 mb-4">
            <div class="card bg-white bg-opacity-25 p-1 rounded">
                {% if work.image %}
                    <img src="{{ work.image.url }}" class="card-img-top img-fluid" alt="{{ work.name }}">
                {% else %}
                    <img src="{% static 'images/noimage.png' %}" class="card-img-top img-fluid" alt="{{ work.name }}">
                {% endif %}
            </div>
        </div>

        <!-- Details Column -->
        <div class="col-12 col-md-6 col-lg-6">
            <div class="d-flex flex-column bg-black bg-opacity-75 p-3 rounded">
                <h1 class="mb-4">{{ work.name }}</h1>

                <!-- Category Badge -->
                <div class="mb-3">
                    <span class="badge bg-primary">{{ work.category.friendly_name }}</span>
                </div>

                <!-- Price and Rating Row -->
                <div class="row mb-4">
                    <div class="col-6">
                        <h2 class="text-primary mb-0">${{ work.price }}</h2>
                        <small class="text-muted">Final price (taxes may apply in some locations)</small>
                    </div>
                    <div class="col-6">
                        {% with average_rating=work.average_rating %}
                            {% if average_rating %}
                                <p class="text-primary mb-0">
                                    <i class="fas fa-star text-warning"></i>
                                    {{ average_rating|floatformat:1 }} / 5
                                    ({{ work.total_reviews_count }} reviews)
                                </p>
                            {% else %}
                                <p class="text-primary mb-0">No ratings yet</p>
                            {% endif %}
                        {% endwith %}
                    </div>
                </div>

                <!-- Additional Details -->
                <div class="card mb-0">
                    <div class="card-body text-white">
                        <h3 class="card-title h5">Work Details</h3>
                        <div class="row">
                            <div class="col-6">
                                <p class="mb-2">
                                    <i class="fas fa-book me-2"></i>
                                    {% if work.category.name == 'ebook' %}
                                        <span>Pages: {{ work.pages }}</span>
                                    {% elif work.category.name == 'audiobook' %}
                                        <span>Duration: {{ work.duration }} minutes</span>
                                    {% endif %}
                                </p>
                                {% if work.category.name == 'ebook' %}
                                <p class="mb-2">
                                    <i class="fas fa-clock me-2"></i>
                                    <span>Reading time: ~{{ work.reading_time }} minutes</span>
                                </p>
                                {% endif %}
                                <p class="mb-2">
                                    <i class="fas fa-list me-2"></i>
                                    <span>Genre:
                                        {% for genre in work.genre.all %}
                                            {{ genre.name }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    </span>
                                </p>
                            </div>
                            <div class="col-6">
                                <p class="mb-2">
                                    <i class="fas fa-language me-2"></i>
                                    <span>Language: {{ work.language }}</span>
                                </p>
                                <p class="mb-2">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    <span>Published: {{ work.created_date|date:"F j, Y" }}</span>
                                </p>
                                <p class="mb-2">
                                    <i class="fas fa-file me-2"></i>
                                    <span>Format:
                                        {% for format in work.file_format.all %}
                                            {{ format.name }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Description -->
    <div class="row">
        <div class="col-12">
            <div class="mb-4 bg-black bg-opacity-75 p-4 rounded">
                <h3>Description</h3>
                <hr>
                <p class="lead">{{ work.description|safe }}</p>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    {% if related_products %}
    <div class="row">
        <div class="col-12">
            <div class="mb-4 bg-black bg-opacity-75 p-4 rounded">
                <h3>Other Formats</h3>
                <hr>
                <div class="row">
                    {% for product in related_products %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">{{ product.name }}</h5>
                                <p class="card-text">
                                    <span class="badge bg-primary">{{ product.category.friendly_name }}</span>
                                    <span class="ms-2 text-white">${{ product.price }}</span>
                                </p>
                                <a href="{% url 'work_detail' product.id %}" class="btn btn-outline-primary">View Format</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                <p class="mt-3 text-muted">
                    <i class="fas fa-info-circle"></i>
                    Reviews are shared across all formats of the same book part.
                </p>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Action Buttons -->
    <form class="form" action="{% url 'add_to_bookcart' work.id %}" method="POST">
        {% csrf_token %}

        <!-- Purchase Section -->
        {% if not user_has_purchased %}
        <div class="card mb-4 purchase-card">
            <div class="card-body">
                <h4 class="card-title">Access Options</h4>
                <div class="form-row">
                    <div class="d-flex gap-2 flex-wrap">
                        {% if user.is_authenticated %}
                            <input type="submit" class="btn btn-primary" value="{% if work.category.name == 'ebook' %}Get eBook{% elif work.category.name == 'audiobook' %}Get Audiobook{% else %}Get Content{% endif %}">
                        {% else %}
                            <a href="{% url 'account_login' %}?next={{ request.path|urlencode }}" class="btn btn-primary">
                                {% if work.category.name == 'ebook' %}
                                    Get eBook
                                {% elif work.category.name == 'audiobook' %}
                                    Get Audiobook
                                {% else %}
                                    Get Content
                                {% endif %}
                            </a>
                            <div class="alert alert-info mt-2 w-100">
                                {% if work.category.name == 'ebook' %}
                                    Please log in or register to access this eBook. Once acquired, you can download to read on your device.
                                {% elif work.category.name == 'audiobook' %}
                                    Please log in or register to access this audiobook. Once acquired, you can listen online or offline in our audiobook player.
                                {% else %}
                                    Please log in or register to access this content. Once acquired, you can enjoy it online or/and offline.
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <input type="hidden" name="redirect_url" value="{{ request.path }}">
            </div>
        </div>
        {% endif %}

        <!-- Content Access Section -->
        <div class="card mb-4 content-access-card">
            <div class="card-body">
                <h4 class="card-title">{% if user_has_purchased %}Your Content Access{% else %}Content Preview{% endif %}</h4>

                <div class="d-flex flex-wrap gap-2">
                    <!-- Audiobook Access -->
                    {% if work.audiobook_link %}
                        {% if user_has_purchased %}
                            <a href="{% url 'audio_player' work.id %}" class="btn btn-success">
                                <i class="fas fa-headphones me-2"></i>Play Audiobook
                            </a>
                        {% elif work.audiobook_sample_link %}
                            {% if user.is_authenticated %}
                                <a href="{% url 'audio_player' work.id %}?sample=true" class="btn btn-outline-success">
                                    <i class="fas fa-headphones me-2"></i>Preview Audiobook
                                </a>
                            {% else %}
                                <a href="{% url 'account_login' %}?next={{ request.path }}" class="btn btn-outline-success">
                                    <i class="fas fa-headphones me-2"></i>Log in to Preview
                                </a>
                                <small class="text-muted d-block mt-1">Free sample available after login</small>
                            {% endif %}
                        {% endif %}
                    {% endif %}

                    <!-- E-book Access -->
                    {% if work.category.name == 'ebook' %}
                        {% if user_has_purchased and work.ebook_file %}
                            <a href="{{ work.ebook_file.url }}" class="btn btn-success" download>
                                <i class="fas fa-download me-2"></i>Download E-book
                            </a>
                        {% elif not user_has_purchased %}
                            {% if user.is_authenticated %}
                                <a href="javascript:void(0);" class="btn btn-outline-success" onclick="document.querySelector('.nav-tabs .nav-link[data-bs-target=\'#excerpt\']').click();">
                                    <i class="fas fa-book-open me-2"></i>Read Sample
                                </a>
                                <small class="text-muted d-block mt-1">Preview available in the Excerpt tab</small>
                            {% else %}
                                <a href="{% url 'account_login' %}?next={{ request.path }}" class="btn btn-outline-success">
                                    <i class="fas fa-book-open me-2"></i>Log in to Preview
                                </a>
                                <small class="text-muted d-block mt-1">Free sample available after login</small>
                            {% endif %}
                        {% endif %}
                    {% endif %}

                    <!-- No Content Message -->
                    {% if user_has_purchased %}
                        {% if work.category.name == 'ebook' and not work.ebook_file %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                The eBook file is being prepared. Please check back later.
                            </div>
                        {% elif work.category.name == 'audiobook' and not work.audiobook_link %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                The audiobook is being prepared. Please check back later.
                            </div>
                        {% elif not work.ebook_file and not work.audiobook_link %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Content is being prepared. Please check back later.
                            </div>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Navigation and Sharing -->
        <div class="card mb-4 navigation-card">
            <div class="card-body">
                <div class="d-flex gap-3 flex-wrap justify-content-between">
                    <div>
                        <a href="{% url 'works' %}" class="btn btn-outline-secondary text-white" aria-label="Back to Works">
                            <i class="fas fa-arrow-left me-2"></i>Back to Works
                        </a>
                    </div>
                    <div class="d-flex gap-2">
                        <!-- Copy Link Button -->
                        <button type="button" class="btn btn-outline-primary" id="copyLink" aria-label="Copy Link">
                            <i class="fas fa-link me-2"></i>Copy Link
                        </button>

                        <!-- Share Button (visible on all devices) -->
                        <button type="button" class="btn btn-outline-primary" id="shareButton" aria-label="Share">
                            <i class="fas fa-share-alt me-2"></i>Share
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Toast Notification -->
        <div class="toast-container position-fixed top-0 end-0 p-3">
            <div id="copyLinkToast" class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="bi bi-check-circle me-2"></i>Link copied to clipboard
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </form>

    <!-- Additional Information Section -->
    <div class="row mt-5 mb-5">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#excerpt" aria-label="Excerpt">Excerpt</button>
                        </li>
                        <li class="nav-item">
                            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#reviews" aria-label="Reviews">Reviews</button>
                        </li>
                        {% if attachments %}
                        <li class="nav-item">
                            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#attachments" aria-label="Attachments">Attachments</button>
                        </li>
                        {% endif %}
                    </ul>
                </div>
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="excerpt">
                        <p>{{ work.excerpt|safe }}</p>
                    </div>
                    <div class="tab-pane fade" id="reviews">
                        <div class="mb-3">
                            <h4>Reviews for {{ work.name }}</h4>
                            <p class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Reviews are shared across all formats of the same book part.
                            </p>
                        </div>

                        {% if user.is_authenticated %}
                            {% if user_has_reviewed and not user_has_approved_review %}
                                <div class="alert alert-info">
                                    Your review has been submitted and is pending approval.
                                </div>
                            {% elif not user_has_reviewed %}
                                {% if user_has_purchased %}
                                    <form method="POST" action="{% url 'add_review' work.id %}">
                                        {% csrf_token %}
                                        {{ review_form }}
                                        <button type="submit" class="btn btn-primary mt-2" aria-label="Submit Review">Submit Review</button>
                                    </form>
                                {% else %}
                                    <div class="alert alert-info">
                                        Only verified purchasers can review this book. Purchase this book to share your review!
                                    </div>
                                {% endif %}
                            {% endif %}
                        {% else %}
                        <div class="alert alert-info">
                            Please <a href="{% url 'account_login' %}" aria-label="Login">login</a> to leave a review.
                        </div>
                        {% endif %}
                        <div class="reviews-list mt-4">
                            {% for review in reviews %}
                                <div class="review-card mb-3">
                                    <div class="d-flex align-items-center mb-2">
                                        {% if review.user.profile %}
                                            {% if review.user.profile.profile_image %}
                                                <img src="{{ review.user.profile.profile_image.url }}" alt="Profile Image" class="rounded-circle nav-profile-image">
                                            {% else %}
                                                <img src="{{ MEDIA_URL }}profile_images/default.jpg" alt="Default Profile Image" class="rounded-circle nav-profile-image">
                                            {% endif %}
                                        {% else %}
                                            <img src="{{ MEDIA_URL }}profile_images/default.jpg" alt="Default Profile Img" class="rounded-circle nav-profile-image">
                                        {% endif %}
                                        <strong>{{ review.user.username }}</strong>
                                        <div class="rating">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star text-warning"></i>
                                                {% else %}
                                                    <i class="far fa-star"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="mb-1">{{ review.comment|format_contest_review }}</div>
                                    <div class="text-end">
                                        <small class="text-muted">{{ review.created_date|date:"F j, Y" }}</small>
                                    </div>
                                </div>
                            {% empty %}
                                <p>No approved reviews yet.</p>
                            {% endfor %}
                        </div>
                    </div>

                    {% if attachments %}
                    <div class="tab-pane fade" id="attachments">
                        <h4 class="mb-4">Available Attachments</h4>
                        <div class="row">
                            {% for attachment in attachments %}
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ attachment.name }}</h5>
                                        {% if attachment.description %}
                                        <p class="card-text">{{ attachment.description }}</p>
                                        {% endif %}

                                        {% if attachment.is_promotional %}
                                        <span class="badge bg-warning mb-2">Promotional</span>
                                        {% endif %}

                                        <div class="mt-3">
                                            {% if attachment.attachment_type == 'file' and attachment.file %}
                                            <a href="{{ attachment.file.url }}" class="btn btn-primary" download>
                                                <i class="fas fa-download me-2"></i>Download File
                                            </a>
                                            {% elif attachment.attachment_type == 'link' and attachment.link %}
                                            <a href="{{ attachment.link }}" class="btn btn-primary" target="_blank">
                                                <i class="fas fa-external-link-alt me-2"></i>Access Link
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <div class="card-footer text-muted">
                                        Added: {{ attachment.created_date|date:"F j, Y" }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        {% if user_has_purchased %}
                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i>
                            Thank you for your purchase! You have access to all attachments for this product.
                        </div>
                        {% else %}
                        <div class="alert alert-warning mt-4">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Purchase this product to access all attachments. Currently, you can only see promotional materials.
                        </div>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block postloadjs %}
<script>
    // Handle the Read Sample button click
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the tab functionality
        var tabElements = document.querySelectorAll('.nav-tabs .nav-link');
        tabElements.forEach(function(tab) {
            tab.addEventListener('click', function(event) {
                event.preventDefault();

                // Remove active class from all tabs and panes
                tabElements.forEach(function(t) {
                    t.classList.remove('active');
                });

                var tabPanes = document.querySelectorAll('.tab-pane');
                tabPanes.forEach(function(pane) {
                    pane.classList.remove('show', 'active');
                });

                // Add active class to clicked tab
                this.classList.add('active');

                // Show the corresponding tab pane
                var target = this.getAttribute('data-bs-target');
                var pane = document.querySelector(target);
                if (pane) {
                    pane.classList.add('show', 'active');
                }

                // Scroll to the tab content
                document.querySelector('.card-header').scrollIntoView({behavior: 'smooth'});
            });
        });

        // Handle the Copy Link button
        document.getElementById('copyLink').addEventListener('click', function() {
            var url = window.location.href;
            navigator.clipboard.writeText(url).then(function() {
                var toast = new bootstrap.Toast(document.getElementById('copyLinkToast'));
                toast.show();
            });
        });

        // Handle the Share button if Web Share API is available
        if (navigator.share) {
            document.getElementById('shareButton').addEventListener('click', function() {
                navigator.share({
                    title: '{{ work.name }}',
                    text: 'Check out {{ work.name }} on Tempus Author Platform',
                    url: window.location.href
                });
            });
        } else {
            // Hide the share button if Web Share API is not available
            document.getElementById('shareButton').style.display = 'none';
        }
    });
</script>
{% endblock %}
