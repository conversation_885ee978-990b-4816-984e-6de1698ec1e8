TEMPUS LOADER GIF INSTRUCTIONS

Please place your "tempusloader.gif" file in this directory.

The GIF will be used as a loading indicator when users download audiobook chapters.
For best results, use a GIF that:
- Has dimensions around 180px wide
- Has a transparent or dark background
- Represents your brand's literary aesthetic
- Is 2-3 seconds in length (looping animation)

After adding the file, run the Django collectstatic command:
python manage.py collectstatic

This will ensure the GIF is properly served to users. 