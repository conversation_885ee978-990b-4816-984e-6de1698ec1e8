/**
 * Tempus Loader Extension - Enhances the existing audio manager with visual feedback
 * This extension adds a loading indicator and dimming overlay for download operations
 */

// Use an IIFE to avoid global variable conflicts
(function() {
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Tempus Loader Extension loaded');
        
        // Set up event listeners for download events
        setupDownloadEventListeners();
        
        // Wait a bit for other scripts to initialize, then override functions
        setTimeout(() => {
            overrideDownloadFunctions();
        }, 1000);
        
        /**
         * Set up event listeners for download events
         */
        function setupDownloadEventListeners() {
            // Listen for download start
            window.addEventListener('chapter-download-started', (event) => {
                console.log('Download started event received');
                const { metadata } = event.detail;
                showLoader(`Downloading "${metadata.title}"...`);
                showOverlay();
            });
            
            // Listen for download completion
            window.addEventListener('chapter-download-completed', (event) => {
                console.log('Download completed event received');
                setTimeout(() => {
                    hideLoader();
                    hideOverlay();
                }, 500); // Short delay to ensure user sees the completion
            });
            
            // Listen for download failure
            window.addEventListener('chapter-download-failed', (event) => {
                console.log('Download failed event received');
                hideLoader();
                hideOverlay();
            });
            
            // Listen for download progress
            window.addEventListener('chapter-download-progress', (event) => {
                const { metadata, progress } = event.detail;
                const progressPercent = Math.round(progress * 100);
                showLoader(`Downloading "${metadata.title}"... ${progressPercent}%`);
            });
            
            // Monitor the download progress container
            const progressObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const progressContainer = document.getElementById('downloadProgressContainer');
                        if (progressContainer && progressContainer.style.display !== 'none') {
                            // Progress container is visible, hide our loader
                            console.log('Progress container visible, hiding loader');
                            hideLoader();
                            hideOverlay();
                        }
                    }
                });
            });
            
            const progressContainer = document.getElementById('downloadProgressContainer');
            if (progressContainer) {
                progressObserver.observe(progressContainer, { attributes: true });
                console.log('Observing progress container');
            }
            
            // Monitor download status alerts
            const statusObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const statusContainer = document.getElementById('downloadStatus');
                        if (statusContainer && statusContainer.style.display !== 'none') {
                            // Status alert is visible, hide our loader
                            console.log('Status alert visible, hiding loader');
                            hideLoader();
                            hideOverlay();
                        }
                    }
                });
            });
            
            const statusContainer = document.getElementById('downloadStatus');
            if (statusContainer) {
                statusObserver.observe(statusContainer, { attributes: true });
                console.log('Observing status container');
            }
            
            // Fallback: Hide loader after a timeout if no events are received
            const downloadButtons = [
                document.getElementById('downloadCurrentChapter'),
                document.getElementById('downloadRemainingChapters'),
                document.getElementById('downloadEntireAudiobook')
            ];
            
            downloadButtons.forEach(button => {
                if (button) {
                    button.addEventListener('click', function() {
                        // Set a fallback timeout to hide the loader if no events are received
                        setTimeout(() => {
                            if (isLoaderVisible()) {
                                console.log('Fallback: Hiding loader after timeout');
                                hideLoader();
                                hideOverlay();
                            }
                        }, 15000); // 15 seconds fallback
                    });
                }
            });
        }
        
        /**
         * Override download functions to add visual feedback
         */
        function overrideDownloadFunctions() {
            // Check if audioPlayer exists
            if (!window.audioPlayer) {
                console.warn('audioPlayer not found, cannot override functions');
                return;
            }
            
            // Store original functions
            const originalQueueCurrentChapter = window.audioPlayer.queueCurrentChapter;
            const originalQueueRemainingChapters = window.audioPlayer.queueRemainingChapters;
            const originalQueueEntireAudiobook = window.audioPlayer.queueEntireAudiobook;
            
            // Override with our versions that show/hide the loader
            if (originalQueueCurrentChapter) {
                window.audioPlayer.queueCurrentChapter = function() {
                    console.log('Queue current chapter called');
                    showLoader('Downloading current chapter...');
                    showOverlay();
                    return originalQueueCurrentChapter.apply(this, arguments);
                };
            }
            
            if (originalQueueRemainingChapters) {
                window.audioPlayer.queueRemainingChapters = function() {
                    console.log('Queue remaining chapters called');
                    showLoader('Downloading remaining chapters...');
                    showOverlay();
                    return originalQueueRemainingChapters.apply(this, arguments);
                };
            }
            
            if (originalQueueEntireAudiobook) {
                window.audioPlayer.queueEntireAudiobook = function() {
                    console.log('Queue entire audiobook called');
                    showLoader('Downloading entire audiobook...');
                    showOverlay();
                    return originalQueueEntireAudiobook.apply(this, arguments);
                };
            }
            
            console.log('Successfully enhanced audioPlayer with visual feedback');
        }
        
        /**
         * Show the loader with a message
         */
        function showLoader(message) {
            const loader = document.getElementById('tempusLoader');
            if (!loader) return;
            
            const loaderText = loader.querySelector('.loader-text');
            if (loaderText) {
                loaderText.textContent = message || 'Preparing your literary journey...';
            }
            
            loader.style.display = 'flex';
        }
        
        /**
         * Hide the loader
         */
        function hideLoader() {
            const loader = document.getElementById('tempusLoader');
            if (loader) {
                loader.style.display = 'none';
            }
        }
        
        /**
         * Check if loader is visible
         */
        function isLoaderVisible() {
            const loader = document.getElementById('tempusLoader');
            return loader && loader.style.display !== 'none';
        }
        
        /**
         * Show the overlay
         */
        function showOverlay() {
            const overlay = document.getElementById('tempusOverlay');
            if (overlay) {
                overlay.style.display = 'block';
            }
        }
        
        /**
         * Hide the overlay
         */
        function hideOverlay() {
            const overlay = document.getElementById('tempusOverlay');
            if (overlay) {
                overlay.style.display = 'none';
            }
        }
        
        // Expose loader functions globally
        window.tempusLoader = {
            showLoader: showLoader,
            hideLoader: hideLoader,
            isLoaderVisible: isLoaderVisible
        };
    });
})(); 