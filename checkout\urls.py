from django.urls import path
from . import views
from .webhooks import stripe_webhook

urlpatterns = [
    path('', views.checkout, name='checkout'),
    path('checkout_success/<order_id>/', 
         views.checkout_success, 
         name='checkout_success'),
    path('cache_checkout_data/', 
         views.cache_checkout_data, 
         name='cache_checkout_data'),
    path('create-order/', 
         views.create_order, 
         name='create_order'),
    path('payment-complete/', 
         views.payment_complete, 
         name='payment_complete'),
    path('webhook/', stripe_webhook, name='webhook'),
]
