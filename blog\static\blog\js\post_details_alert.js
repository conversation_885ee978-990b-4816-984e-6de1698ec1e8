// Copy link and share button functionality
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById("copyLink").addEventListener("click", function() {
        navigator.clipboard.writeText(window.location.href).then(() => {
            const toastElement = document.getElementById('copyLinkToast');
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 1800
            });
            toast.show();
        }).catch(err => {
            console.error('Failed to copy: ', err);
            alert('Failed to copy link. Please try again.');
        });
    });

    document.getElementById("shareButton").addEventListener("click", function() {
        if (navigator.share) {
            navigator.share({
                title: document.title,
                url: window.location.href
            }).then(() => {
                console.log('Thanks for sharing!');
            }).catch(console.error);
        } else {
            alert('Web Share API is not supported in your browser.');
        }
    });
});