.toast-container {
    z-index: 9999;
}

.toast {
    font-size: 0.875rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: opacity 0.15s linear;
}

.toast.show {
    opacity: 0.9;
}

/* Related Posts Styles */
.related-posts .blog-card {
    transition: transform 0.2s ease-in-out;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.related-posts .blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.related-posts .card-title a {
    transition: color 0.2s ease-in-out;
}

.related-posts .card-title a:hover {
    color: #0d6efd !important;
}

.related-posts-slider {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
    scrollbar-color: rgba(0,0,0,0.2) transparent;
}

.related-posts-slider::-webkit-scrollbar {
    height: 6px;
}

.related-posts-slider::-webkit-scrollbar-track {
    background: transparent;
}

.related-posts-slider::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,0.2);
    border-radius: 3px;
}