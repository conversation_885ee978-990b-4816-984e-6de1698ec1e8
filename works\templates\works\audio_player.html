{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'works/css/audio_player.css' %}">
<link rel="stylesheet" href="{% static 'works/css/tempus_loader.css' %}">
{% endblock %}

{% block content %}
<div id="audioPlayerContainer" class="audio-player-container" 
     data-metadata-url="{% if request.GET.sample %}{{ product.audiobook_sample_link }}{% else %}{{ product.audiobook_link }}{% endif %}" 
     data-product-id="{{ product.id }}"
     {% if request.GET.sample %}data-sample-mode="true"{% endif %}>
    
    <!-- Header with Logo -->
    <div class="player-header">
        <div class="site-logo">
            <img src="{% static 'images/logo.png' %}" alt="Site Logo" class="logo-image">
        </div>
        <div class="header-content">
            <h2>{{ product.name }}</h2>
            <div class="author">By {{ product.author|default:"Rafal Zygula" }}</div>
            <a href="{% url 'work_detail' product.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Product
            </a>
        </div>
    </div>
    
    <!-- Cover Image Section -->
    <div class="cover-image-section">
        <img id="currentChapterCover" class="main-cover-image" src="" alt="Book Cover" style="display: none;">
    </div>
    
    <!-- Main Player Section -->
    <div class="player-main">
        <!-- Player Panel -->
        <div class="player-panel">
            <div class="card">
                <div class="card-body">
                    <!-- Current Chapter Info -->
                    <div class="current-chapter-info">
                        <div class="current-chapter-text">
                            <h3 id="currentChapterTitle" class="current-chapter-title">Loading audiobook...</h3>
                            <div id="currentPartTitle" class="current-part-title"></div>
                        </div>
                    </div>
                    
                    <!-- Custom Audio Player -->
                    <div class="custom-audio-player">
                        <!-- Hidden native audio element -->
                        <audio id="audioElement" preload="metadata"></audio>
                        
                        <!-- Progress Bar -->
                        <div id="progressContainer" class="progress-container">
                            <div id="progressBar" class="progress-bar"></div>
                        </div>
                        
                        <!-- Time Display -->
                        <div class="time-display">
                            <span id="currentTime">0:00</span>
                            <span id="duration">0:00</span>
                        </div>
                        
                        <!-- Control Buttons -->
                        <div class="controls-buttons">
                            <div class="main-controls">
                                <button id="prevButton" class="control-button" aria-label="Previous Chapter">
                                    <i class="fas fa-step-backward"></i>
                                </button>
                                
                                <button id="playPauseButton" class="control-button play-pause-button" aria-label="Play/Pause">
                                    <i id="playPauseIcon" class="fas fa-play"></i>
                                </button>
                                
                                <button id="nextButton" class="control-button" aria-label="Next Chapter">
                                    <i class="fas fa-step-forward"></i>
                                </button>
                            </div>
                            
                            
                        </div>
                    </div>
                    
                    <!-- Offline Mode Status -->
                    <div class="offline-status mt-3">
                        <div class="offline-badges">
                            <div id="offlineStatusBadge" class="offline-badge">
                                <i class="fas fa-wifi"></i> <span id="connectionStatus">Online</span>
                            </div>
                            <!-- <div id="offlineAvailabilityBadge" class="availability-badge" style="display: none;">
                                <i class="fas fa-download"></i> <span id="offlineAvailabilityText">Available Offline</span>
                            </div> -->
                        </div>
                        <div class="speed-control">
                            <button id="speedButton" class="speed-button">1.0x</button>
                            <div id="speedOptions" class="speed-options">
                                <div class="speed-option active" data-speed="1.0">1.0x</div>
                                <div class="speed-option" data-speed="1.25">1.25x</div>
                                <div class="speed-option" data-speed="1.5">1.5x</div>
                                <div class="speed-option" data-speed="1.75">1.75x</div>
                                <div class="speed-option" data-speed="2.0">2.0x</div>
                                <div class="speed-option" data-speed="0.75">0.75x</div>
                            </div>
                        </div>
                    </div>

                    
                    
                    <!-- Download Options -->
                    <div class="download-options mt-3" {% if request.GET.sample %}style="display: none;"{% endif %}>
                        <div class="btn-group dropdown w-100">
                            <button class="btn btn-success w-100 dropdown-toggle" type="button" id="downloadDropdownMenu" 
                                    data-bs-toggle="dropdown" data-bs-auto-close="true" data-bs-offset="0,5"
                                    aria-expanded="false">
                                <i class="fas fa-download me-2"></i> Download Options
                            </button>
                            <ul class="dropdown-menu dropdown-menu-dark w-100" aria-labelledby="downloadDropdownMenu"
                                data-bs-popper="static">
                                <li><button id="downloadCurrentChapter" class="dropdown-item">
                                    <i class="fas fa-file-audio me-2"></i> Current Chapter
                                </button></li>
                                <li><button id="downloadRemainingChapters" class="dropdown-item">
                                    <i class="fas fa-arrow-down me-2"></i> Remaining Chapters
                                </button></li>
                                <li><button id="downloadEntireAudiobook" class="dropdown-item">
                                    <i class="fas fa-book-open me-2"></i> Entire Audiobook
                                </button></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><button class="dropdown-item" type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#offlineManagementCollapse" aria-expanded="false">
                                    <i class="fas fa-cog me-2"></i> Manage Downloads
                                </button></li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Download Progress -->
                    <div id="downloadProgressContainer" class="download-progress-container mt-3" style="display: none;">
                        <h5 class="download-progress-title">
                            <i class="fas fa-arrow-circle-down"></i> 
                            <span id="downloadProgressTitle">Downloading...</span>
                        </h5>
                        
                        <div class="progress mb-2">
                            <div id="downloadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                        </div>
                        
                        <div class="d-flex justify-content-between small text-muted">
                            <div id="downloadProgressStatus">Preparing download...</div>
                            <div id="downloadProgressStats">0/0</div>
                        </div>
                    </div>
                    
                    <!-- Download Status Notification -->
                    <div id="downloadStatus" class="alert alert-success mt-2" style="display: none;">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="downloadStatusText">Download complete</span>
                        <button type="button" class="btn-close float-end" aria-label="Close" 
                                onclick="document.getElementById('downloadStatus').style.display='none'"></button>
                    </div>
                    
                    <!-- Offline Downloads Management -->
                    <div class="offline-management mt-3">
                        <div class="collapse mt-2" id="offlineManagementCollapse">
                            <div class="card card-body text-white">
                                <div class="d-flex justify-content-between mb-2">
                                    <div><strong>Storage Usage:</strong></div>
                                    <div id="storageUsage">Calculating...</div>
                                </div>
                                
                                <div><strong>Downloaded Chapters:</strong></div>
                                <div id="downloadedChaptersList" class="downloaded-chapters-list mt-2">
                                    <div class="text-center py-2 text-muted">
                                        <i class="fas fa-info-circle me-1"></i> No chapters downloaded yet
                                    </div>
                                </div>
                                
                                <button id="clearAllDownloads" class="btn btn-danger mt-2 w-100">
                                    <i class="fas fa-trash-alt me-1"></i> Clear All Downloads
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Autoplay Message -->
                    <div class="alert alert-info mt-3" id="autoplayMessage" style="cursor: pointer;" onclick="this.style.display='none';">
                        <i class="fas fa-info-circle me-2"></i>
                        Click the play button to start playback. Your previous listening position has been loaded.
                        <span class="close-message" id="closeAutoplayMessage"><i class="fas fa-times"></i></span>
                    </div>
                    
                    <!-- Error Message -->
                    <div id="errorMessage" class="error-message">
                        Error loading audio file. Please try another chapter.
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Chapters Panel -->
        <div class="chapters-panel">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Chapters</h4>
                </div>
                <div class="card-body p-0">
                    <div class="chapter-list text-white" id="chapterList">
                        <!-- Chapters will be populated via JavaScript -->
                        <div class="text-center p-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading chapters...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="mt-3 text-center">
        <a href="{% url 'work_detail' product.id %}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Product
        </a>
    </div>
</div>

<!-- Custom Tempus Loader (positioned at the end of the body for proper overlay) -->
<div id="tempusLoader" class="tempus-loader" style="display: none;">
    <img src="{% static 'works/images/tempusloader.gif' %}" alt="Loading..." class="tempus-loader-gif">
    <p class="loader-text">Preparing your literary journey...</p>
</div>

<!-- Overlay for blocking interactions during downloads -->
<div id="tempusOverlay" class="tempus-overlay" style="display: none;"></div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'works/js/offline_audio_manager.js' %}"></script>
<script src="{% static 'works/js/media_session_manager.js' %}"></script>
<script src="{% static 'works/js/download_loader.js' %}"></script>
<script src="{% static 'works/js/tempus_loader_extension.js' %}"></script>
<script src="{% static 'works/js/audio_player.js' %}"></script>
{% endblock %} 