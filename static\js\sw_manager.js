/**
 * Service Worker Manager
 * Handles communication with the service worker and provides UI for cache status
 */
class ServiceWorkerManager {
  constructor() {
    this.swRegistration = null;
    this.statusElement = null;
    this.cacheStatus = {
      audioFiles: 0,
      staticFiles: 0,
      htmlFiles: 0,
      storage: {
        used: '0 KB',
        total: '0 KB',
        percent: 0
      },
      version: 'unknown'
    };
    
    // Initialize
    this.init();
  }
  
  /**
   * Initialize the service worker manager
   */
  async init() {
    // Don't create status element anymore
    // this.createStatusElement();
    
    // Check if service worker is supported
    if ('serviceWorker' in navigator) {
      try {
        // Get service worker registration
        this.swRegistration = await navigator.serviceWorker.ready;
        console.log('Service Worker is ready:', this.swRegistration);
        
        // Set up message listener
        navigator.serviceWorker.addEventListener('message', this.handleMessage.bind(this));
        
        // Request cache status
        this.requestCacheStatus();
        
        // Add event listeners for UI controls
        // this.addEventListeners();
      } catch (error) {
        console.error('Service Worker initialization failed:', error);
      }
    } else {
      console.warn('Service Workers are not supported in this browser');
      this.updateStatusElement('Service Workers not supported');
    }
  }
  
  /**
   * Create status element in the DOM
   */
  createStatusElement() {
    // Don't create status element anymore
    return;
    
    /*
    // Check if element already exists
    this.statusElement = document.getElementById('sw-status');
    if (this.statusElement) return;
    
    // Create status element
    this.statusElement = document.createElement('div');
    this.statusElement.id = 'sw-status';
    this.statusElement.className = 'sw-status';
    this.statusElement.innerHTML = `
      <div class="sw-status-header">
        <span class="sw-status-title">Offline Status</span>
        <button class="sw-status-toggle" aria-label="Toggle offline status panel">
          <i class="fas fa-chevron-down"></i>
        </button>
      </div>
      <div class="sw-status-content">
        <div class="sw-status-info">
          <div class="sw-status-loading">Checking status...</div>
          <div class="sw-status-details" style="display: none;">
            <div class="sw-status-item">
              <span class="sw-status-label">Audio Files:</span>
              <span class="sw-status-value" id="sw-audio-files">0</span>
            </div>
            <div class="sw-status-item">
              <span class="sw-status-label">Static Files:</span>
              <span class="sw-status-value" id="sw-static-files">0</span>
            </div>
            <div class="sw-status-item">
              <span class="sw-status-label">HTML Pages:</span>
              <span class="sw-status-value" id="sw-html-files">0</span>
            </div>
            <div class="sw-status-item">
              <span class="sw-status-label">Storage:</span>
              <span class="sw-status-value" id="sw-storage-used">0 KB / 0 KB</span>
            </div>
            <div class="sw-status-storage-bar">
              <div class="sw-status-storage-progress" id="sw-storage-progress" style="width: 0%"></div>
            </div>
            <div class="sw-status-version">
              <span class="sw-status-label">Version:</span>
              <span class="sw-status-value" id="sw-version">unknown</span>
            </div>
          </div>
        </div>
        <div class="sw-status-actions">
          <button class="sw-status-button" id="sw-refresh-status">
            <i class="fas fa-sync-alt"></i> Refresh
          </button>
          <button class="sw-status-button" id="sw-clear-audio-cache">
            <i class="fas fa-trash-alt"></i> Clear Audio
          </button>
          <button class="sw-status-button" id="sw-clear-all-cache">
            <i class="fas fa-broom"></i> Clear All
          </button>
        </div>
      </div>
    `;
    
    // Add styles
    const style = document.createElement('style');
    style.textContent = `
      .sw-status {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 300px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        z-index: 9999;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        transition: transform 0.3s ease;
        transform: translateY(calc(100% - 40px));
      }
      
      .sw-status.expanded {
        transform: translateY(0);
      }
      
      .sw-status-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        background-color: #4a69bd;
        color: #fff;
        cursor: pointer;
      }
      
      .sw-status-title {
        font-weight: 600;
        font-size: 14px;
      }
      
      .sw-status-toggle {
        background: none;
        border: none;
        color: #fff;
        cursor: pointer;
        padding: 0;
        font-size: 12px;
        transition: transform 0.3s ease;
      }
      
      .sw-status.expanded .sw-status-toggle i {
        transform: rotate(180deg);
      }
      
      .sw-status-content {
        padding: 15px;
        max-height: 300px;
        overflow-y: auto;
      }
      
      .sw-status-loading {
        text-align: center;
        padding: 10px;
        color: #666;
        font-style: italic;
      }
      
      .sw-status-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 13px;
      }
      
      .sw-status-label {
        color: #666;
      }
      
      .sw-status-value {
        font-weight: 600;
        color: #333;
      }
      
      .sw-status-storage-bar {
        height: 6px;
        background-color: #eee;
        border-radius: 3px;
        margin: 10px 0;
        overflow: hidden;
      }
      
      .sw-status-storage-progress {
        height: 100%;
        background-color: #4a69bd;
        border-radius: 3px;
      }
      
      .sw-status-version {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #eee;
        font-size: 12px;
      }
      
      .sw-status-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
      }
      
      .sw-status-button {
        background-color: #f1f2f6;
        border: none;
        padding: 6px 10px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
        color: #333;
        transition: background-color 0.2s ease;
      }
      
      .sw-status-button:hover {
        background-color: #dfe4ea;
      }
      
      .sw-status-button i {
        margin-right: 4px;
        font-size: 10px;
      }
      
      @media (max-width: 576px) {
        .sw-status {
          width: calc(100% - 40px);
          max-width: 350px;
        }
      }
    `;
    document.head.appendChild(style);
    
    // Add to DOM
    document.body.appendChild(this.statusElement);
    
    // Add event listeners
    const header = this.statusElement.querySelector('.sw-status-header');
    if (header) {
      header.addEventListener('click', () => {
        this.statusElement.classList.toggle('expanded');
      });
    }
    
    // Add button event listeners
    const refreshButton = document.getElementById('sw-refresh-status');
    if (refreshButton) {
      refreshButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.requestCacheStatus();
      });
    }
    
    const clearAudioButton = document.getElementById('sw-clear-audio-cache');
    if (clearAudioButton) {
      clearAudioButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.clearCache('audio');
      });
    }
    
    const clearAllButton = document.getElementById('sw-clear-all-cache');
    if (clearAllButton) {
      clearAllButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.clearCache('all');
      });
    }
    */
  }
  
  /**
   * Add event listeners to UI elements
   */
  addEventListeners() {
    // Don't add event listeners since we're not showing the status anymore
    return;
    
    /*
    // Toggle status panel
    if (this.statusElement) {
      const header = this.statusElement.querySelector('.sw-status-header');
      if (header) {
        header.addEventListener('click', () => {
          this.statusElement.classList.toggle('expanded');
        });
      }
    }
    
    // Refresh status
    const refreshButton = document.getElementById('sw-refresh-status');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.requestCacheStatus();
      });
    }
    
    // Clear audio cache
    const clearAudioButton = document.getElementById('sw-clear-audio-cache');
    if (clearAudioButton) {
      clearAudioButton.addEventListener('click', () => {
        this.clearCache('audio');
      });
    }
    
    // Clear all cache
    const clearAllButton = document.getElementById('sw-clear-all-cache');
    if (clearAllButton) {
      clearAllButton.addEventListener('click', () => {
        this.clearCache('all');
      });
    }
    */
  }
  
  /**
   * Handle messages from service worker
   */
  handleMessage(event) {
    const message = event.data;
    
    if (!message || !message.type) return;
    
    console.log('Received message from Service Worker:', message);
    
    switch (message.type) {
      case 'CACHE_STATUS':
        this.updateCacheStatus(message);
        break;
        
      case 'CACHE_CLEARED':
        console.log(`Cache cleared: ${message.cacheType}`);
        this.requestCacheStatus();
        break;
        
      case 'CACHE_COMPLETE':
        console.log('Cache initialization complete:', message.urls);
        this.requestCacheStatus();
        break;
        
      case 'CACHE_ERROR':
        console.error('Cache error:', message.error);
        break;
        
      case 'SW_ERROR':
        console.error('Service Worker error:', message.message);
        break;
    }
  }
  
  /**
   * Request cache status from service worker
   */
  requestCacheStatus() {
    if (!this.swRegistration || !navigator.serviceWorker.controller) {
      console.warn('Service Worker not active yet');
      // Don't update status element
      // this.updateStatusElement('Service Worker not active');
      return;
    }
    
    // Don't update UI elements since we're not showing the status anymore
    /*
    // Show loading state
    const loadingElement = this.statusElement.querySelector('.sw-status-loading');
    const detailsElement = this.statusElement.querySelector('.sw-status-details');
    
    if (loadingElement && detailsElement) {
      loadingElement.style.display = 'block';
      detailsElement.style.display = 'none';
    }
    */
    
    // Send message to service worker
    navigator.serviceWorker.controller.postMessage({
      type: 'GET_CACHE_STATUS'
    });
  }
  
  /**
   * Clear cache
   * @param {string} cacheType - Type of cache to clear ('audio', 'all')
   */
  clearCache(cacheType) {
    if (!this.swRegistration || !navigator.serviceWorker.controller) {
      console.warn('Service Worker not active yet');
      return;
    }
    
    // Confirm with user
    const message = cacheType === 'all' 
      ? 'This will clear all cached files. You will need to be online to use the app. Continue?'
      : 'This will clear all cached audio files. You will need to be online to play audio. Continue?';
      
    if (!confirm(message)) return;
    
    // Send message to service worker
    navigator.serviceWorker.controller.postMessage({
      type: 'CLEAR_CACHE',
      cacheType: cacheType
    });
  }
  
  /**
   * Update cache status
   * @param {Object} status - Cache status object
   */
  updateCacheStatus(status) {
    this.cacheStatus = status;
    
    // Don't update UI elements since we're not showing the status anymore
    /*
    // Update UI elements
    const audioFilesElement = document.getElementById('sw-audio-files');
    const staticFilesElement = document.getElementById('sw-static-files');
    const htmlFilesElement = document.getElementById('sw-html-files');
    const storageUsedElement = document.getElementById('sw-storage-used');
    const storageProgressElement = document.getElementById('sw-storage-progress');
    const versionElement = document.getElementById('sw-version');
    
    if (audioFilesElement) audioFilesElement.textContent = status.audioFiles;
    if (staticFilesElement) staticFilesElement.textContent = status.staticFiles;
    if (htmlFilesElement) htmlFilesElement.textContent = status.htmlFiles;
    
    if (storageUsedElement) {
      storageUsedElement.textContent = `${status.storage.used} / ${status.storage.total}`;
    }
    
    if (storageProgressElement) {
      storageProgressElement.style.width = `${status.storage.percent}%`;
      
      // Change color based on usage
      if (status.storage.percent > 90) {
        storageProgressElement.style.backgroundColor = '#e74c3c';
      } else if (status.storage.percent > 70) {
        storageProgressElement.style.backgroundColor = '#f39c12';
      } else {
        storageProgressElement.style.backgroundColor = '#4a69bd';
      }
    }
    
    if (versionElement) versionElement.textContent = status.version;
    
    // Hide loading, show details
    const loadingElement = this.statusElement.querySelector('.sw-status-loading');
    const detailsElement = this.statusElement.querySelector('.sw-status-details');
    
    if (loadingElement && detailsElement) {
      loadingElement.style.display = 'none';
      detailsElement.style.display = 'block';
    }
    */
    
    // Just log the status to console for debugging
    console.log('Cache status updated:', status);
  }
  
  /**
   * Update status element with message
   * @param {string} message - Status message
   */
  updateStatusElement(message) {
    // Don't update UI elements since we're not showing the status anymore
    /*
    const loadingElement = this.statusElement.querySelector('.sw-status-loading');
    if (loadingElement) {
      loadingElement.textContent = message;
    }
    */
    
    // Just log the message to console for debugging
    console.log('Service Worker status:', message);
  }
  
  /**
   * Update online status indicator
   */
  updateOnlineStatus() {
    // Don't update online status indicator since we're not showing it anymore
    return;
    
    /*
    const offlineIndicator = document.getElementById('sw-offline-indicator');
    if (!offlineIndicator) return;
    
    if (navigator.onLine) {
      offlineIndicator.style.display = 'none';
    } else {
      offlineIndicator.style.display = 'block';
    }
    */
  }
  
  /**
   * Pre-cache specific resources
   * @param {Array} urls - Array of URLs to cache
   */
  preCacheResources(urls) {
    if (!this.swRegistration || !navigator.serviceWorker.controller || !urls || !urls.length) {
      return;
    }
    
    navigator.serviceWorker.controller.postMessage({
      type: 'INIT_CACHE',
      urls: urls
    });
  }
}

// Initialize service worker manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.swManager = new ServiceWorkerManager();
}); 