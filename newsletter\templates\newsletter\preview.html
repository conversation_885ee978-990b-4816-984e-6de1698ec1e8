{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Preview: {{ newsletter.subject }}{% endblock %}

{% block extrastyle %}
{{ block.super }}
<style>
    .preview-container {
        max-width: 800px;
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
    }
    .preview-header {
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    .preview-actions {
        margin: 20px 0;
    }
    .preview-frame {
        width: 100%;
        min-height: 600px;
        border: 1px solid #ddd;
    }
</style>
{% endblock %}

{% block content %}
<div class="preview-container">
    <div class="preview-header">
        <h1>Newsletter Preview</h1>
        <p><strong>Subject:</strong> {{ newsletter.subject }}</p>
        {% if newsletter.preview_text %}
        <p><strong>Preview Text:</strong> {{ newsletter.preview_text }}</p>
        {% endif %}
    </div>

    <div class="preview-actions">
        <a href="{% url 'admin:newsletter_newsletter_change' newsletter.pk %}" class="button">Edit Newsletter</a>
        {% if newsletter.status == 'draft' %}
        <form method="post" action="{% url 'admin:newsletter_newsletter_changelist' %}" style="display: inline;">
            {% csrf_token %}
            <input type="hidden" name="action" value="send_test_email">
            <input type="hidden" name="_selected_action" value="{{ newsletter.pk }}">
            <input type="submit" value="Send Test Email" class="button">
        </form>
        {% endif %}
    </div>

    <div class="preview-content">
        <iframe src="{% url 'newsletter:preview' newsletter.pk %}" class="preview-frame"></iframe>
    </div>
</div>
{% endblock %}
