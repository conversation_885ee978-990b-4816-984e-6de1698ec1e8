"""
Management command to process the email queue.
"""
import json
import time
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.core.mail import EmailMultiAlternatives
from email_manager.models import EmailConfiguration, EmailCounter, QueuedEmail

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Process queued emails respecting rate limits'

    def add_arguments(self, parser):
        parser.add_argument(
            '--limit',
            type=int,
            default=None,
            help='Maximum emails to send per hour (overrides configuration)'
        )
        parser.add_argument(
            '--batch',
            type=int,
            default=None,
            help='Number of emails to process in one batch (overrides configuration)'
        )
        parser.add_argument(
            '--delay',
            type=int,
            default=None,
            help='Seconds to wait between batches (overrides configuration)'
        )
        parser.add_argument(
            '--once',
            action='store_true',
            help='Process one batch and exit (for manual triggering)'
        )

    def handle(self, *args, **options):
        # Get configuration
        config = EmailConfiguration.get_config()
        
        # Use command line arguments if provided, otherwise use configuration
        limit = options['limit'] or config.hourly_email_limit
        batch_size = options['batch'] or config.queue_batch_size
        delay = options['delay'] or config.queue_batch_delay
        once = options['once']
        
        self.stdout.write(
            f"Starting email queue processing (limit: {limit}/hour, "
            f"batch: {batch_size}, delay: {delay}s)"
        )
        
        # Process queue once or continuously
        if once:
            self.process_batch(limit, batch_size)
        else:
            self.process_continuously(limit, batch_size, delay)
    
    def process_continuously(self, limit, batch_size, delay):
        """Process the queue continuously"""
        while True:
            processed = self.process_batch(limit, batch_size)
            
            if processed == 0:
                # No emails to process, wait longer
                self.stdout.write("No pending emails. Waiting...")
                time.sleep(60)  # Check again in a minute
            else:
                # Wait between batches
                self.stdout.write(f"Waiting {delay} seconds before next batch")
                time.sleep(delay)
    
    def process_batch(self, limit, batch_size):
        """Process a single batch of emails"""
        # Check if we can send more emails this hour
        current_count = EmailCounter.get_current_count()
        remaining = limit - current_count
        
        if remaining <= 0:
            self.stdout.write(
                f"Hourly limit reached ({current_count}/{limit}). "
                f"Waiting for next hour."
            )
            # Wait until the next hour
            now = timezone.now()
            next_hour = now.replace(hour=now.hour+1, minute=0, second=0, microsecond=0)
            wait_seconds = (next_hour - now).total_seconds()
            time.sleep(wait_seconds + 10)  # Add 10 seconds buffer
            return 0
        
        # Get pending emails, prioritized
        batch_size = min(batch_size, remaining)  # Don't exceed remaining limit
        pending_emails = QueuedEmail.objects.filter(
            status='pending'
        ).order_by('priority', 'created_at')[:batch_size]
        
        if not pending_emails:
            return 0
        
        self.stdout.write(f"Processing batch of {pending_emails.count()} emails")
        processed_count = 0
        
        # Process the batch
        for email in pending_emails:
            try:
                # Create email message
                message = EmailMultiAlternatives(
                    subject=email.subject,
                    body=email.text_content,
                    from_email=email.from_email,
                    to=[email.recipient]
                )
                message.attach_alternative(email.html_content, "text/html")
                
                # Add attachments if any
                if email.has_attachments and email.attachment_data:
                    try:
                        attachments = json.loads(email.attachment_data)
                        for attachment in attachments:
                            if 'path' in attachment and 'filename' in attachment:
                                message.attach_file(
                                    attachment['path'],
                                    attachment.get('mimetype', None)
                                )
                    except Exception as e:
                        logger.error(f"Error processing attachments: {str(e)}")
                
                # Send the email
                message.send(fail_silently=False)
                
                # Update email status
                email.status = 'sent'
                email.last_attempt = timezone.now()
                email.save()
                
                # Increment counter
                EmailCounter.increment()
                processed_count += 1
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Sent: {email.subject} to {email.recipient}"
                    )
                )
                
            except Exception as e:
                # Update failure information
                email.status = 'failed'
                email.error_message = str(e)
                email.retry_count += 1
                email.last_attempt = timezone.now()
                email.save()
                
                self.stdout.write(
                    self.style.ERROR(
                        f"Failed to send email to {email.recipient}: {str(e)}"
                    )
                )
        
        return processed_count
