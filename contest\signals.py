from django.db.models.signals import post_save
from django.dispatch import receiver
import logging

from .models import BookAccessRequest

logger = logging.getLogger(__name__)

@receiver(post_save, sender=BookAccessRequest)
def handle_book_access_request(sender, instance, created, **kwargs):
    """
    Handle book access request when it's approved and access is granted.
    No email is sent - users are notified through the UI instead.
    """
    # Log the access grant for monitoring purposes
    if instance.access_granted and instance.status == 'approved':
        logger.info(
            f"Book access granted to user {instance.user.username} "
            f"for {instance.book_part.name} ({instance.get_format_type_display()})"
        )
