# Generated by Django 5.1.5 on 2025-02-23 17:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('newsletter', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='newsletter',
            name='is_draft',
        ),
        migrations.AddField(
            model_name='emailtracking',
            name='click_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='click_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='error_message',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='open_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='preview_text',
            field=models.CharField(blank=True, help_text='Short summary that appears in email clients', max_length=200),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='scheduled_for',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='sent_to_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('scheduled', 'Scheduled'), ('sending', 'Sending'), ('sent', 'Sent'), ('error', 'Error')], default='draft', max_length=10),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='track_clicks',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='newsletter',
            name='track_opens',
            field=models.BooleanField(default=True),
        ),
        migrations.AlterField(
            model_name='newsletter',
            name='content',
            field=models.TextField(help_text='You can use {{ subscriber.name }} and {{ subscriber.email }} in your content.'),
        ),
    ]
