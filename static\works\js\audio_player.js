// First, let's add the Howler.js library via CDN at the beginning of the file
// We'll add this as a self-executing function to add How<PERSON>
(function loadHowler() {
    if (typeof Howl === 'undefined') {
        // Create a script tag and append to head
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/howler/2.2.3/howler.min.js';
        script.async = true;
        script.onload = function() {
            console.log('Howler.js loaded successfully');
            // Signal that Howler is ready
            document.dispatchEvent(new Event('howler-loaded'));
            
            // If there's already an audiobookPlayer instance, update it
            if (window.audiobookPlayer) {
                // Update the media session
                if (window.audiobookPlayer.mediaSessionManager) {
                    window.audiobookPlayer.mediaSessionManager.updateMetadata();
                    window.audiobookPlayer.mediaSessionManager.updatePositionState();
                } else if (typeof window.audiobookPlayer.setupMediaSessionForHowler === 'function') {
                    window.audiobookPlayer.setupMediaSessionForHowler();
                }
                
                // If the player has a current chapter, reinitialize with <PERSON><PERSON>
                if (window.audiobookPlayer.currentChapter && window.audiobookPlayer.currentPart) {
                    const wasPlaying = window.audiobookPlayer.isPlaying;
                    const currentTime = window.audiobookPlayer.audioElement ? 
                        window.audiobookPlayer.audioElement.currentTime : 0;
                    
                    // Reinitialize the chapter with Howler
                    window.audiobookPlayer.setupChapter(
                        window.audiobookPlayer.currentChapter, 
                        window.audiobookPlayer.currentPart,
                        currentTime
                    );
                    
                    // Resume playback if it was playing
                    if (wasPlaying) {
                        window.audiobookPlayer.playAudio();
                    }
                }
            }
        };
        script.onerror = function() {
            console.error('Failed to load Howler.js - falling back to native audio');
            document.dispatchEvent(new Event('howler-load-failed'));
        };
        document.head.appendChild(script);
    }
})();

// Add custom CSS for dropdown menus and adjust dropdown positioning
(function enhanceDropdowns() {
    // Remove any existing dropdown styles to avoid duplicates
    const existingStyles = document.getElementById('audioPlayerDropdownStyles');
    if (existingStyles) {
        existingStyles.parentNode.removeChild(existingStyles);
    }
    
    const styleElement = document.createElement('style');
    styleElement.id = 'audioPlayerDropdownStyles';
    styleElement.textContent = `
        /* Reset any constrained overflow on containers */
        #audioPlayerContainer, .player-container, .audio-player-wrapper, 
        .download-options, .dropdown, .collapse, .card {
            overflow: visible !important;
        }
        
        /* Ensure dropdown menus appear above other content */
        .dropdown-menu {
            z-index: 2000 !important;
            max-height: 400px;
            overflow-y: auto;
        }
        
        /* Force the dropdown to open upward when .dropup is used */
        .dropup .dropdown-menu[data-bs-popper] {
            top: auto !important;
            bottom: 100% !important;
            transform: translateY(-2px) !important;
        }
        
        /* Improve dropdown appearance */
        .download-options .dropdown-menu {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            border-radius: 4px;
        }
    `;
    document.head.appendChild(styleElement);
    
    // Wait for DOM to be loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get dropdown elements when DOM is ready
        const downloadDropdown = document.getElementById('downloadDropdownMenu');
        if (!downloadDropdown) return;
        
        const dropdownParent = downloadDropdown.closest('.dropdown, .btn-group');
        if (!dropdownParent) return;
        
        // Function to force dropdown direction based on available space
        function setDropdownDirection() {
            const buttonRect = downloadDropdown.getBoundingClientRect();
            const windowHeight = window.innerHeight;
            const spaceBelow = windowHeight - buttonRect.bottom;
            
            // Set direction based on available space
            // We use .dropup class to make it open upward
            if (spaceBelow < 300) {
                // Not enough space below, force it to open upward
                dropdownParent.classList.remove('dropdown');
                dropdownParent.classList.add('dropup');
                console.log('Setting dropdown to open upward');
            } else {
                // Enough space below, let it open downward
                dropdownParent.classList.remove('dropup');
                dropdownParent.classList.add('dropdown');
                console.log('Setting dropdown to open downward');
            }
        }
        
        // Add click event to the button to set direction before dropdown opens
        downloadDropdown.addEventListener('click', function(e) {
            setDropdownDirection();
        });
        
        // Handle window resize to adjust dropdown direction if it's open
        window.addEventListener('resize', function() {
            if (dropdownParent.classList.contains('show')) {
                setDropdownDirection();
            }
        });
        
        // Also add event listeners for Bootstrap's events
        downloadDropdown.addEventListener('show.bs.dropdown', function() {
            setDropdownDirection();
        });
        
        // Make sure to properly handle closing
        document.addEventListener('click', function(e) {
            // If click is outside dropdown and dropdown is open, force close it
            if (dropdownParent.classList.contains('show') && 
                !dropdownParent.contains(e.target)) {
                
                // Use Bootstrap's API to hide dropdown if available
                if (bootstrap && bootstrap.Dropdown) {
                    const dropdownInstance = bootstrap.Dropdown.getInstance(downloadDropdown);
                    if (dropdownInstance) {
                        dropdownInstance.hide();
                    }
                }
            }
        });
    });
})();

// Add CSS for player messages
(function addPlayerMessageStyles() {
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        .player-message {
            display: none;
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 14px;
            transition: all 0.3s ease;
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            z-index: 100;
            white-space: nowrap;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }
        
        .player-message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .player-message.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .player-message.warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }
        
        .player-message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        @media (max-width: 768px) {
            .player-message {
                bottom: auto;
                top: -45px;
                font-size: 12px;
                padding: 6px 10px;
            }
        }
    `;
    document.head.appendChild(styleElement);
})();

class AudiobookPlayer {
    constructor(metadataUrl, productId) {
        this.metadataUrl = metadataUrl;
        this.productId = productId;
        this.audioData = null;
        this.currentChapter = null;
        this.currentPart = null;
        this.audioElement = null; // We'll keep this for compatibility but use Howler for actual playback
        this.howl = null; // This will store the Howler instance
        this.isPlaying = false;
        this.playbackSpeed = 1.0;
        
        // Check if audioPlayerContainer exists
        const audioPlayerContainer = document.getElementById('audioPlayerContainer');
        this.isSampleMode = false;
        
        // Properly detect sample mode
        if (audioPlayerContainer) {
            // Check data attribute
            this.isSampleMode = audioPlayerContainer.dataset.sampleMode === 'true';
            
            // If not set via data attribute, check URL param as fallback
            if (!this.isSampleMode) {
                const urlParams = new URLSearchParams(window.location.search);
                this.isSampleMode = urlParams.get('sample') === 'true';
            }
            
            console.log('Sample mode detected:', this.isSampleMode);
        }
        
        // Storage keys
        this.storageKeyChapter = `audiobook_${this.productId}_chapter`;
        this.storageKeyPart = `audiobook_${this.productId}_part`;
        this.storageKeyPosition = `audiobook_${this.productId}_position`;
        this.storageKeySpeed = `audiobook_${this.productId}_speed`;
        this.storageKeyHideAutoplayMessage = 'audiobook_hide_autoplay_message';
        
        // Offline playback status
        this.isOffline = !navigator.onLine;
        this.offlineManager = window.offlineAudioManager;
        this.downloadQueue = [];
        this.isDownloading = false;
        
        // Media Session Manager
        this.mediaSessionManager = null;
        
        // Initialize the player
        this.init();
        
        // Set up offline mode listeners
        this.setupOfflineListeners();
    }
    
    /**
     * Initialize the player
     */
    async init() {
        try {
            // Add custom methods to offline manager if needed
            this.addGetChapterAudioMethod();
            
            // Fetch audiobook metadata
            await this.fetchMetadata();
            
            // Set up UI elements
            this.setupElements();
            
            // Set up event listeners
            this.setupEventListeners();
            
            // Enhance the progress bar interaction
            this.enhanceProgressInteraction();
            
            // Add skip forward/backward buttons
            this.addSkipButtons();
            
            // Different initialization paths for sample mode vs full audiobook
            if (this.isSampleMode) {
                console.log('Sample mode detected, initializing sample player');
                
                // For samples, we only need to populate chapters and play the sample
                this.populateChapters();
                this.loadSavedSpeed(); // Speed preference is still useful
                this.initMediaSessionManager();
                this.playSample();
                
                // Basic offline status update (online/offline only)
                this.updateOfflineStatusUI();
                
                // Hide download options for sample mode
                const downloadOptions = document.querySelector('.download-options');
                if (downloadOptions) {
                    downloadOptions.style.display = 'none';
                }
            } else {
                console.log('Regular audiobook mode, initializing full player');
            
            // Check if the chapters API endpoint exists
            const chaptersApiUrl = `/api/works/${this.productId}/chapters/`;
            const chaptersApiExists = await this.checkEndpointExists(chaptersApiUrl);
            
            // Only try to fetch chapters if the API endpoint exists
            if (!chaptersApiExists) {
                console.log(`Chapters API endpoint ${chaptersApiUrl} does not exist. Skipping chapters fetch.`);
            }
            
            // Populate chapters list
            this.populateChapters();
            
            // Load saved position
            this.loadSavedPosition();
            
            // Load saved speed
            this.loadSavedSpeed();
            
            // Initialize Media Session Manager
            this.initMediaSessionManager();
            
                // No longer attempting autoplay due to browser restrictions
                // Show autoplay message for manual play instead
                this.showAutoplayMessage();
            
            // Check if the current chapter is available offline
            this.checkCurrentChapterAvailability();
            
            // Check if offline manager is ready, if yes, update UI elements
            if (window.offlineAudioManager && window.offlineAudioManager.initialized) {
                this.updateDownloadedChaptersList();
                this.updateChapterDownloadIndicators();
                this.updateStorageUsage();
                } else if (window.offlineAudioManager) {
                // Listen for the offline-manager-ready event once
                const onOfflineManagerReady = () => {
                    this.updateDownloadedChaptersList();
                    this.updateChapterDownloadIndicators();
                    this.updateStorageUsage();
                    window.removeEventListener('offline-manager-ready', onOfflineManagerReady);
                };
                window.addEventListener('offline-manager-ready', onOfflineManagerReady);
                } else {
                    // Offline manager isn't available, update UI to show online-only mode
                    this.updateOfflineStatusUI();
                }
            }
        } catch (error) {
            console.error('Error initializing audiobook player:', error);
            this.showError('Failed to initialize audiobook player. Please try refreshing the page.');
        }
    }
    
    setupElements() {
        // Main elements
        this.audioElement = document.getElementById('audioElement');
        this.progressBar = document.getElementById('progressBar');
        this.progressContainer = document.getElementById('progressContainer');
        this.currentTimeDisplay = document.getElementById('currentTime');
        this.durationDisplay = document.getElementById('duration');
        this.playPauseButton = document.getElementById('playPauseButton');
        this.playPauseIcon = document.getElementById('playPauseIcon');
        this.prevButton = document.getElementById('prevButton');
        this.nextButton = document.getElementById('nextButton');
        this.speedButton = document.getElementById('speedButton');
        this.speedOptions = document.getElementById('speedOptions');
        this.chapterList = document.getElementById('chapterList') || document.createElement('div');
        this.currentChapterTitle = document.getElementById('currentChapterTitle');
        this.currentPartTitle = document.getElementById('currentPartTitle');
        this.currentChapterCover = document.getElementById('currentChapterCover');
        this.errorMessage = document.getElementById('errorMessage') || document.createElement('div');
        
        // Offline status elements
        this.offlineStatusBadge = document.getElementById('offlineStatusBadge');
        this.connectionStatus = document.getElementById('connectionStatus');
        this.offlineAvailabilityBadge = document.getElementById('offlineAvailabilityBadge');
        this.offlineAvailabilityText = document.getElementById('offlineAvailabilityText');
        
        // Download related elements
        this.downloadOptions = document.querySelector('.download-options');
        this.downloadCurrentChapterButton = document.getElementById('downloadCurrentChapter');
        this.downloadRemainingChaptersButton = document.getElementById('downloadRemainingChapters');
        this.downloadEntireAudiobookButton = document.getElementById('downloadEntireAudiobook');
        this.clearDownloadsButton = document.getElementById('clearDownloads');
        this.storageUsageDisplay = document.getElementById('storageUsage');
        this.downloadedChaptersList = document.getElementById('downloadedChaptersList');
        
        // Hide download options in sample mode
        if (this.isSampleMode && this.downloadOptions) {
            this.downloadOptions.style.display = 'none';
        }
        
        // Update offline status UI
        this.updateOfflineStatusUI();
    }
    
    setupEventListeners() {
        // If registerControlListeners exists now, use it
        if (typeof this.registerControlListeners === 'function') {
            this.registerControlListeners();
            this.setupOfflineListeners();
        } else {
            // Fallback to original implementation for compatibility
            // Audio element events
            this.audioElement.addEventListener('timeupdate', () => this.updateProgress());
            this.audioElement.addEventListener('loadedmetadata', () => this.updateDuration());
            this.audioElement.addEventListener('ended', () => this.playNextChapter());
            this.audioElement.addEventListener('error', (e) => this.handleAudioError(e));
            
            // Control elements
            this.playPauseButton.addEventListener('click', () => this.togglePlayPause());
            
            // Progress bar with enhanced mobile touch support
            this.progressContainer.addEventListener('click', (e) => this.setProgress(e));
            this.progressContainer.addEventListener('mousemove', (e) => this.updateProgressTouchPoint(e));
            this.progressContainer.addEventListener('touchmove', (e) => {
                if (e.touches.length > 0) {
                    const touch = e.touches[0];
                    const rect = this.progressContainer.getBoundingClientRect();
                    const offsetX = touch.clientX - rect.left;
                    
                    // Create a synthetic event with offsetX
                    const syntheticEvent = { offsetX };
                    this.updateProgressTouchPoint(syntheticEvent);
                }
            });
            this.progressContainer.addEventListener('touchend', (e) => {
                if (e.changedTouches.length > 0) {
                    const touch = e.changedTouches[0];
                    const rect = this.progressContainer.getBoundingClientRect();
                    const offsetX = touch.clientX - rect.left;
                    
                    // Create a synthetic event with offsetX
                    const syntheticEvent = { offsetX };
                    this.setProgress(syntheticEvent);
                }
            });
            
            // Track navigation
            this.prevButton.addEventListener('click', () => this.playPreviousChapter());
            this.nextButton.addEventListener('click', () => this.playNextChapter());
            
            // Speed control
            this.speedButton.addEventListener('click', () => this.toggleSpeedOptions());
            this.speedOptions.querySelectorAll('.speed-option').forEach(option => {
                option.addEventListener('click', () => {
                    const speed = parseFloat(option.dataset.speed);
                    this.setPlaybackSpeed(speed);
                    this.toggleSpeedOptions();
                });
            });
        }
        
        // Download button event listeners (moved outside of conditional for reliability)
        // Find download buttons by ID, as our properties might have been renamed
        const downloadCurrentChapter = document.getElementById('downloadCurrentChapter');
        const downloadRemainingChapters = document.getElementById('downloadRemainingChapters');
        const downloadEntireAudiobook = document.getElementById('downloadEntireAudiobook');
        const clearAllDownloads = document.getElementById('clearAllDownloads');
        
        if (downloadCurrentChapter) {
            downloadCurrentChapter.addEventListener('click', () => this.queueCurrentChapter());
        }
        
        if (downloadRemainingChapters) {
            downloadRemainingChapters.addEventListener('click', () => this.queueRemainingChapters());
        }
        
        if (downloadEntireAudiobook) {
            downloadEntireAudiobook.addEventListener('click', () => this.queueEntireAudiobook());
            }
            
            // Offline management
        if (clearAllDownloads) {
            clearAllDownloads.addEventListener('click', () => this.clearAllDownloadedChapters());
            }
            
            // Close autoplay message event
        const closeAutoplayMessage = document.getElementById('closeAutoplayMessage');
        if (closeAutoplayMessage) {
            closeAutoplayMessage.addEventListener('click', () => this.hideAutoplayMessagePermanently());
            }
            
            // Close speed options when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.speed-control') && this.speedOptions && this.speedOptions.classList.contains('show')) {
                    this.speedOptions.classList.remove('show');
                }
            });
        
        // Set up keyboard shortcuts
        this.setupKeyboardControls();
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
                // When page becomes visible again, check if audio was playing
                if (this.wasPlayingBeforeHidden) {
                    this.playWithRetry();
                    this.wasPlayingBeforeHidden = false;
                }
            } else if (document.visibilityState === 'hidden') {
                // Store state when page becomes hidden
                this.wasPlayingBeforeHidden = !this.audioElement.paused;
            }
        });

        // Listen for network status changes
        window.addEventListener('online', () => {
            console.log('🌐 Network connection restored');
            this.updateOfflineStatusUI();
            
            // If user went offline and back online, try to restore playback
            if (this.wasPlayingBeforeOffline) {
                this.playWithRetry();
                this.wasPlayingBeforeOffline = false;
            }
        });

        window.addEventListener('offline', () => {
            console.log('📴 Network connection lost');
            this.updateOfflineStatusUI();
            
            // Remember if audio was playing when going offline
            this.wasPlayingBeforeOffline = !this.audioElement.paused;
        });
        
        // New event handler for page load completion
        window.addEventListener('load', () => {
            // Wait a short time after load to ensure all resources are available
            setTimeout(() => {
                // If there was a playback error on initial load, try one more time
                if (this.hadPlaybackError && this.currentChapter) {
                    console.log('🔄 Retrying playback after page load completion');
                    this.playChapter(this.currentChapter, this.currentPart);
                    this.hadPlaybackError = false;
                }
            }, 1000);
        });
        
        // Listen for download complete events
        window.addEventListener('audioDownloadComplete', (event) => {
            console.log('Download complete event received:', event.detail);
            
            // Make sure the download progress is updated to 100%
            this.updateDownloadProgress({
                progress: 100,
                message: 'Download complete!',
                ...event.detail
            });
            
            // Also update the chapter download indicators to show the new offline status
            this.updateChapterDownloadIndicators();
        });
        
        // Make autoplay message dismissible by clicking anywhere on it
        const autoplayMessage = document.getElementById('autoplayMessage');
        if (autoplayMessage) {
            autoplayMessage.style.cursor = 'pointer';
            autoplayMessage.addEventListener('click', function() {
                this.style.display = 'none';
            });
        }
    }
    
    setupOfflineListeners() {
        // Check initial network status
        this.isOffline = !navigator.onLine;
        this.updateOfflineStatusUI();

        // Set up event listeners for online/offline events
        window.addEventListener('online', () => {
            console.log('Device is now online');
            this.isOffline = false;
            this.updateOfflineStatusUI();
            
            // Show a message
            this.showMessage('You are now online', 'info');
            
            // If we had an error due to being offline, try loading current chapter again
            if (this.errorMessage.style.display === 'block' && 
                this.errorMessage.textContent.includes('offline')) {
                this.hideError();
                if (this.currentChapter && this.currentPart) {
                    this.setupChapter(this.currentChapter, this.currentPart);
                }
            }
        });

        window.addEventListener('offline', () => {
            console.log('Device is now offline');
            this.isOffline = true;
            this.updateOfflineStatusUI();
            
            // Show a message
            this.showMessage('You are now offline', 'warning');
            
            // Check if current chapter is available offline
            if (this.currentChapter && this.currentPart) {
                this.checkCurrentChapterAvailability();
            }
        });
        
        // Also check connectivity every 30 seconds, as the online/offline events
        // aren't always reliable across all browsers
        setInterval(() => {
            const wasOffline = this.isOffline;
            this.isOffline = !navigator.onLine;
            
            // If state changed, manually trigger the appropriate event
            if (wasOffline !== this.isOffline) {
                console.log(`Network status changed to ${this.isOffline ? 'offline' : 'online'}`);
                const event = new Event(this.isOffline ? 'offline' : 'online');
                window.dispatchEvent(event);
            }
        }, 30000);
        
        // Listen for offline manager events
        window.addEventListener('offline-manager-ready', () => {
            console.log('Offline manager is ready');
            this.updateOfflineStatusUI();
            this.updateDownloadedChaptersList();
            this.updateChapterDownloadIndicators();
            this.updateStorageUsage(); // Update storage display when manager is ready
        });
        
        // Add event listener for chapter sizes update
        window.addEventListener('chapter-sizes-updated', (event) => {
            console.log('Chapter sizes updated event received:', event.detail);
            if (event.detail && event.detail.fileSizes) {
                // Update chapter sizes in the UI
                const chapterItems = document.querySelectorAll('.downloaded-chapter-item');
                chapterItems.forEach(item => {
                    const chapterId = item.querySelector('.remove-btn')?.getAttribute('data-id');
                    if (chapterId && event.detail.fileSizes[chapterId]) {
                        const size = event.detail.fileSizes[chapterId];
                        const sizeElement = item.querySelector('.chapter-size');
                        if (sizeElement) {
                            sizeElement.textContent = this.formatBytes(size);
                        }
                    }
                });
            }
        });
        
        // Download progress events
        window.addEventListener('audioDownloadProgress', (event) => {
            const detail = event.detail;
            console.log('Download progress:', detail);
            
            // Update progress UI
            this.updateDownloadProgress({
                progress: detail.progress,
                metadata: {
                    title: detail.title || `Chapter ${detail.chapterNumber}`,
                    partTitle: detail.partTitle
                },
                current: detail.current,
                total: detail.total,
                status: 'downloading',
                message: detail.message
            });
        });
        
        // Download complete event
        window.addEventListener('audioDownloadComplete', (event) => {
            const detail = event.detail;
            console.log('Download complete:', detail);
            
            // Update UI to show 100% and completed status
            this.updateDownloadProgress({
                progress: 100,
                metadata: {
                    title: detail.title || `Chapter ${detail.chapterNumber}`,
                    partTitle: detail.partTitle
                },
                current: detail.current,
                total: detail.total,
                status: 'complete',
                message: 'Download complete'
            });
            
            // Update lists and indicators with a slight delay to ensure DB updates complete
            setTimeout(() => {
                this.updateDownloadedChaptersList();
                this.updateChapterDownloadIndicators();
                this.updateStorageUsage();
            }, 500);
        });
        
        // Download progress events specifically for completed chapters
        window.addEventListener('chapter-download-completed', (event) => {
            const detail = event.detail;
            console.log('Chapter download completed event:', detail);
            
            // Update indicators immediately for this specific chapter
            setTimeout(() => {
                this.updateChapterDownloadIndicators();
            }, 200);
        });
        
        // Download error event
        window.addEventListener('audioDownloadError', (event) => {
            const detail = event.detail;
            console.error('Download error:', detail);
            
            // Show error in UI
            this.updateDownloadProgress({
                progress: 0,
                metadata: {
                    title: detail.title || `Chapter ${detail.chapterNumber}`,
                    partTitle: detail.partTitle
                },
                status: 'error',
                message: detail.message || 'Download failed'
            });
        });
    }
    
    updateOfflineStatusUI() {
        // Find UI elements
        const offlineStatusElement = document.getElementById('offlineStatusBadge');
        const connectionStatusElement = document.getElementById('connectionStatus');
        
        if (!offlineStatusElement || !connectionStatusElement) {
            console.warn('Offline status elements not found, skipping update');
            return;
        }
        
        // Update connection status
        if (navigator.onLine) {
            connectionStatusElement.textContent = 'Online';
            offlineStatusElement.classList.remove('offline');
            offlineStatusElement.classList.add('online');
        } else {
            connectionStatusElement.textContent = 'Offline';
            offlineStatusElement.classList.remove('online');
            offlineStatusElement.classList.add('offline');
        }
        
        // In sample mode, we don't need to check offline availability
        if (this.isSampleMode) {
            // Hide availability badge
            const availabilityBadge = document.getElementById('offlineAvailabilityBadge');
            if (availabilityBadge) {
                availabilityBadge.style.display = 'none';
            }
            return;
        }
        
        // Check if we have the offline manager
        if (!this.offlineManager) {
            // No offline manager, hide availability badge
            const availabilityBadge = document.getElementById('offlineAvailabilityBadge');
            if (availabilityBadge) {
                availabilityBadge.style.display = 'none';
            }
            return;
        }
        
        // If we're here, offline manager exists, so update availability status
        this.checkCurrentChapterAvailability();
    }
    
    checkCurrentChapterAvailability() {
        // Make sure we have elements and chapters before checking
        const availabilityBadge = document.getElementById('offlineAvailabilityBadge');
        const availabilityText = document.getElementById('offlineAvailabilityText');
        
        if (!availabilityBadge || !availabilityText) {
            console.warn('Offline availability elements not found');
            return;
        }
        
        // If no chapter is loaded or offline manager isn't available, hide badge
        if (!this.currentChapter || !this.currentPart || !this.offlineManager) {
            availabilityBadge.style.display = 'none';
            return;
        }
        
        // In sample mode, we don't check offline availability
        if (this.isSampleMode) {
            availabilityBadge.style.display = 'none';
            return;
        }
        
        // Check if current chapter is available offline
        try {
            // Different offlineManager implementations might have different methods
            // for checking chapter availability
            
            // Create chapter metadata
            const chapterId = this.createChapterMetadata(this.currentChapter, this.currentPart).id;
            
            // Method 1: Using isChapterAvailable (newer API)
            if (typeof this.offlineManager.isChapterAvailable === 'function') {
                this.offlineManager.isChapterAvailable(chapterId)
                    .then(isAvailable => {
                        updateAvailabilityBadge(isAvailable);
                    })
                    .catch(error => {
                        console.error('Error checking chapter availability:', error);
                        availabilityBadge.style.display = 'none';
                    });
            }
            // Method 2: Using isChapterAvailableOffline (older API)
            else if (typeof this.offlineManager.isChapterAvailableOffline === 'function') {
                this.offlineManager.isChapterAvailableOffline(
                    this.productId,
                    this.currentPart.part_number,
                    this.currentChapter.number
                ).then(isAvailable => {
                    updateAvailabilityBadge(isAvailable);
                        }).catch(error => {
                    console.error('Error checking chapter availability:', error);
                    availabilityBadge.style.display = 'none';
                });
            }
            // No suitable method found
            else {
                console.warn('No method available to check chapter availability');
                availabilityBadge.style.display = 'none';
            }
            
            // Helper function to update the badge
            const updateAvailabilityBadge = (isAvailable) => {
                if (isAvailable) {
                    availabilityBadge.style.display = 'inline-flex';
                    availabilityBadge.classList.add('available');
                    availabilityBadge.classList.remove('unavailable');
                    availabilityText.textContent = 'Available Offline';
                } else {
                    availabilityBadge.style.display = 'inline-flex';
                    availabilityBadge.classList.add('unavailable');
                    availabilityBadge.classList.remove('available');
                    availabilityText.textContent = 'Not Available Offline';
                }
            };
            
        } catch (error) {
            console.error('Error in checkCurrentChapterAvailability:', error);
            availabilityBadge.style.display = 'none';
        }
    }
    
    /**
     * Update the download progress UI
     */
    updateDownloadProgress(detail) {
        // Find download progress container if not already stored as property
        const downloadProgressContainer = this.downloadProgressContainer || document.getElementById('downloadProgressContainer');
        const downloadProgressTitle = this.downloadProgressTitle || document.getElementById('downloadProgressTitle');
        const downloadProgressBar = this.downloadProgressBar || document.getElementById('downloadProgressBar');
        const downloadProgressStatus = this.downloadProgressStatus || document.getElementById('downloadProgressStatus');
        const downloadProgressStats = this.downloadProgressStats || document.getElementById('downloadProgressStats');
        
        // Make sure we found all required elements
        if (!downloadProgressContainer || !downloadProgressBar) {
            console.warn('Download progress elements not found, skipping update');
            return;
        }
        
        // Hide the overlay once we start showing download progress
        const tempusOverlay = document.getElementById('tempusOverlay');
        if (tempusOverlay) {
            tempusOverlay.style.display = 'none';
        }
        
        // If we have a download progress container, update its UI
            // Make sure the container is visible
        downloadProgressContainer.style.display = 'block';
            
            // Update the title with the specific chapter info if available
            if (detail.metadata && detail.metadata.title) {
                const title = detail.metadata.title;
                const partTitle = detail.metadata.partTitle || '';
                let displayTitle = title;
                
                if (partTitle) {
                    displayTitle = `${partTitle} - ${title}`;
                }
                
            downloadProgressTitle.textContent = `Downloading: ${displayTitle}`;
            } else if (detail.title) {
            downloadProgressTitle.textContent = `Downloading: ${detail.title}`;
            }
            
            // Update the progress bar
            const progress = detail.progress || 0;
        downloadProgressBar.style.width = `${progress}%`;
        downloadProgressBar.textContent = `${progress}%`;
        downloadProgressBar.setAttribute('aria-valuenow', progress);
            
            // Update progress status
            if (detail.message) {
            downloadProgressStatus.textContent = detail.message;
            } else if (progress === 100) {
            downloadProgressStatus.textContent = 'Download complete';
            } else if (progress === 0) {
            downloadProgressStatus.textContent = 'Starting download...';
            } else {
            downloadProgressStatus.textContent = 'Downloading...';
            }
            
            // Update stats if we have a current and total count
            if (detail.current !== undefined && detail.total !== undefined) {
            downloadProgressStats.textContent = `${detail.current}/${detail.total}`;
            }
            
        // Auto-hide the progress container when download is complete (at 100%)
            if (progress === 100) {
            console.log('Download complete, hiding progress bar after delay');
            
            // Make sure the 100% is visible briefly
                setTimeout(() => {
                    // Fade out the download progress container
                downloadProgressContainer.style.transition = 'opacity 1s ease-out';
                downloadProgressContainer.style.opacity = '0';
                    
                    // After fade out, hide it and reset opacity
                    setTimeout(() => {
                    downloadProgressContainer.style.display = 'none';
                    downloadProgressContainer.style.opacity = '1';
                    downloadProgressContainer.style.transition = '';
                        
                        // Reset progress to 0 for next download
                    downloadProgressBar.style.width = '0%';
                    downloadProgressBar.textContent = '0%';
                    downloadProgressBar.setAttribute('aria-valuenow', 0);
                    downloadProgressTitle.textContent = 'Downloading...';
                    downloadProgressStatus.textContent = 'Preparing download...';
                    downloadProgressStats.textContent = '0/0';
                    
                    // Show success message
                    this.showMessage('Download complete', 'success');
                    }, 1000);
            }, 500);
        }
        
        // If we have a download status element, update it too
        this.updateStatusMessage(detail);
    }
    
    /**
     * Update download status message
     * @param {Object} detail - Download event details
     */
    updateStatusMessage(detail) {
        if (!this.downloadStatus) return;
        
        // Update the download status message
        let message = '';
        
        if (detail.status === 'queued') {
            if (detail.total > 1) {
                message = `Queued ${detail.total} chapters for download`;
            } else {
                message = `Queued "${detail.title}" for download`;
            }
        } else if (detail.status === 'downloading') {
            if (detail.total > 1) {
                message = `Downloading ${detail.current}/${detail.total}: ${detail.title}`;
            } else {
                message = `Downloading: ${detail.title}`;
            }
        } else if (detail.status === 'complete') {
            if (detail.total > 1) {
                message = `Downloaded ${detail.total} chapters successfully`;
            } else {
                message = `Downloaded "${detail.title}" successfully`;
            }
        } else if (detail.status === 'error') {
            message = `Error: ${detail.message}`;
        }
        
        if (message) {
            this.showDownloadStatus(message, detail.status === 'error' ? 'error' : 'success');
        }
    }
    
    /**
     * Update the list of downloaded chapters in the UI
     */
    async updateDownloadedChaptersList() {
        if (!this.downloadedChaptersList || !window.offlineAudioManager) {
            return;
        }
        
        try {
            // Get downloaded chapters from offline manager
            const downloadedChapters = await window.offlineAudioManager.getDownloadedChapters(this.productId);
            
            // Clear the list
            this.downloadedChaptersList.innerHTML = '';
            
            if (downloadedChapters.length === 0) {
                this.downloadedChaptersList.innerHTML = `
                    <div class="text-center py-2 text-muted">
                        <i class="fas fa-info-circle me-1"></i> No chapters downloaded yet
                    </div>
                `;
                return;
            }
            
            // Sort by part number and then chapter number
            downloadedChapters.sort((a, b) => {
                if (a.partNumber !== b.partNumber) {
                    return a.partNumber - b.partNumber;
                }
                return a.chapterNumber - b.chapterNumber;
            });
            
            // Group by part
            const chaptersByPart = {};
            downloadedChapters.forEach(chapter => {
                if (!chaptersByPart[chapter.partNumber]) {
                    chaptersByPart[chapter.partNumber] = [];
                }
                chaptersByPart[chapter.partNumber].push(chapter);
            });
            
            // Create HTML for each part and its chapters
            Object.keys(chaptersByPart).sort((a, b) => parseInt(a) - parseInt(b)).forEach(partNumber => {
                const chapters = chaptersByPart[partNumber];
                const partTitle = chapters[0].partTitle || `Part ${partNumber}`;
                
                // Create part header
                const partHeader = document.createElement('div');
                partHeader.className = 'downloaded-part-header';
                partHeader.innerHTML = `<strong>${partTitle}</strong>`;
                this.downloadedChaptersList.appendChild(partHeader);
                
                // Add chapters
                chapters.forEach(chapter => {
                    const size = this.formatBytes(chapter.size || 0);
                    // Create a unique ID that can be used both with the old and new API
                    const chapterId = chapter.id || `${chapter.audiobookId}_${chapter.partNumber}_${chapter.chapterNumber}`;
                    const formattedTitle = `Chapter ${chapter.chapterNumber}: ${chapter.title}`;
                    
                    const chapterItem = document.createElement('div');
                    chapterItem.className = 'downloaded-chapter-item';
                    chapterItem.innerHTML = `
                        <div class="chapter-info">
                            <div class="chapter-title">${formattedTitle}</div>
                            <div class="chapter-meta">
                                <span class="chapter-size">${size}</span>
                            </div>
                        </div>
                        <button class="remove-btn" data-id="${chapterId}" aria-label="Remove chapter">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    
                    this.downloadedChaptersList.appendChild(chapterItem);
                });
            });
            
            // Add event listeners to all remove buttons AFTER they're added to the DOM
            this.downloadedChaptersList.querySelectorAll('.remove-btn').forEach(btn => {
                btn.addEventListener('click', (event) => {
                    // Stop event propagation to prevent other handlers
                    event.stopPropagation();
                    
                    const chapterId = btn.getAttribute('data-id');
                    if (chapterId) {
                        this.removeDownloadedChapter(chapterId);
                    }
                });
            });
            
            // Update storage usage information
            this.updateStorageUsage();
        } catch (error) {
            console.error('Error updating downloaded chapters list:', error);
            this.downloadedChaptersList.innerHTML = `
                <div class="text-center py-2 text-danger">
                    <i class="fas fa-exclamation-circle me-1"></i> Error loading downloaded chapters
                </div>
            `;
        }
    }
    
    /**
     * Format bytes to human-readable size
     * @param {number} bytes - The size in bytes
     * @param {number} [decimals=2] - Number of decimal places to show
     * @returns {string} - Formatted size string (e.g., "2.50 MB")
     */
    formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }
    
    updateStorageUsage() {
        if (!this.storageUsageDisplay || !window.offlineAudioManager) {
            return;
        }
        
        // Show calculating message
        this.storageUsageDisplay.textContent = 'Calculating...';
        
        // Use a better approach to calculate storage usage
        const calculateStorageUsage = async () => {
            try {
                // Get the size directly from OfflineAudioManager's getStorageUsage
                const bytes = await window.offlineAudioManager.getStorageUsage();
                
                // If storage is 0 or very small, don't bother showing decimals
                let formattedSize;
                if (bytes < 1024) {
                    formattedSize = `${bytes} B`;
                } else {
                    formattedSize = this.formatBytes(bytes);
                }
                
                this.storageUsageDisplay.textContent = formattedSize;
                
                console.log(`Storage usage calculated: ${formattedSize} (${bytes} bytes)`);
                
                // Also update individual chapter sizes
                const chapters = await window.offlineAudioManager.getDownloadedChapters(this.productId);
                
                // Update the UI to show no chapters if none are available
                if (chapters.length === 0 && this.downloadedChaptersList) {
                    this.downloadedChaptersList.innerHTML = `
                        <div class="text-center py-2 text-muted">
                            <i class="fas fa-info-circle me-1"></i> No chapters downloaded yet
                        </div>
                    `;
                }
                
                const chapterItems = document.querySelectorAll('.downloaded-chapter-item');
                
                chapterItems.forEach(item => {
                    const chapterId = item.querySelector('.remove-btn')?.getAttribute('data-id');
                    if (chapterId) {
                        const chapter = chapters.find(c => c.id === chapterId);
                        if (chapter && chapter.size) {
                            const sizeElement = item.querySelector('.chapter-size');
                            if (sizeElement) {
                                sizeElement.textContent = this.formatBytes(chapter.size);
                            }
                        }
                    }
                });
            } catch (error) {
                console.error('Error calculating storage usage:', error);
                this.storageUsageDisplay.textContent = 'Error calculating';
            }
        };
        
        // Execute the calculation
        calculateStorageUsage();
    }
    
    /**
     * Queue the currently playing chapter for download
     */
    queueCurrentChapter() {
        console.log('🔽 Queuing current chapter for download');
        if (!this.currentChapter || !this.currentPart) {
            console.error('No chapter is currently playing');
            this.showDownloadStatus('No chapter is currently playing', 'error');
            return;
        }
        
        try {
            // Show loading overlay when download starts
            const tempusOverlay = document.getElementById('tempusOverlay');
            if (tempusOverlay) {
                tempusOverlay.style.display = 'block';
            }
            
            console.log('Current chapter details:', {
                title: this.currentChapter.title,
                number: this.currentChapter.number,
                file: this.currentChapter.file,
                part: {
                    title: this.currentPart.title,
                    number: this.currentPart.part_number,
                    folder: this.currentPart.folder
                }
            });
            
            const metadata = this.createChapterMetadata(this.currentChapter, this.currentPart);
            console.log('Chapter metadata for download:', metadata);
            
            // Skip the fetch URL check due to potential CORS/CSP issues in production
            // We'll rely on the offline_audio_manager's error handling instead
            if (window.offlineAudioManager) {
                window.offlineAudioManager.queueChapterDownload(metadata)
                    .then(() => {
                        console.log(`Queued chapter ${metadata.title} for download`);
                        this.showDownloadStatus(`Queued "${metadata.title}" for download`, 'success');
                        this.updateDownloadedChaptersList();
                        this.updateChapterDownloadIndicators();
                        this.updateStorageUsage(); // Update storage usage
                        
                        // Hide the overlay immediately when download is queued
                        if (tempusOverlay) {
                            tempusOverlay.style.display = 'none';
                        }
                    })
                    .catch(error => {
                        console.error('Error queuing chapter for download:', error);
                        this.showDownloadStatus(`Error: ${error.message}`, 'error');
                        
                        // Hide overlay on error
                        if (tempusOverlay) {
                            tempusOverlay.style.display = 'none';
                        }
                    });
                
                // Add a listener for download start
                const downloadStartListener = (event) => {
                    if (event.detail.id === metadata.id && event.detail.progress > 0) {
                        // Once download actually starts, hide the overlay
                        if (tempusOverlay) {
                            tempusOverlay.style.display = 'none';
                        }
                        // Remove this listener after it fires once
                        window.removeEventListener('audioDownloadProgress', downloadStartListener);
                    }
                };
                
                // Listen for download progress events
                window.addEventListener('audioDownloadProgress', downloadStartListener);
            } else {
                console.error('Offline audio manager not initialized');
                this.showDownloadStatus('Download system not ready. Please try again.', 'error');
                
                // Hide overlay if offline manager isn't available
                if (tempusOverlay) {
                    tempusOverlay.style.display = 'none';
                }
            }
        } catch (error) {
            console.error('Error in queueCurrentChapter:', error);
            this.showDownloadStatus(`Error: ${error.message}`, 'error');
            
            // Hide overlay on error
            const tempusOverlay = document.getElementById('tempusOverlay');
            if (tempusOverlay) {
                tempusOverlay.style.display = 'none';
            }
        }
    }
    
    queueRemainingChapters() {
        if (!this.audioData || !this.currentChapter || !this.currentPart) {
            return;
        }
        
        // Show loading overlay
        const tempusOverlay = document.getElementById('tempusOverlay');
        if (tempusOverlay) {
            tempusOverlay.style.display = 'block';
        }
        
        const chaptersToDownload = [];
        let foundCurrentChapter = false;
        
        // Process all parts
        this.audioData.parts.forEach(part => {
            // If we've already processed the current part or later parts
            if (part.part_number >= this.currentPart.part_number) {
                part.chapters.forEach(chapter => {
                    // If we're in the current part, only add chapters after the current one
                    if (part.part_number === this.currentPart.part_number) {
                        // Include current chapter too - this ensures it gets marked
                        if (chapter.number >= this.currentChapter.number) {
                            foundCurrentChapter = true;
                            chaptersToDownload.push(this.createChapterMetadata(chapter, part));
                        }
                    } else {
                        // For later parts, add all chapters
                        chaptersToDownload.push(this.createChapterMetadata(chapter, part));
                    }
                });
            }
        });
        
        // Queue all chapters
        if (chaptersToDownload.length > 0) {
            this.downloadQueue = chaptersToDownload.map(metadata => ({ metadata, completed: false }));
            
            // Show progress in UI
            const downloadProgressContainer = document.getElementById('downloadProgressContainer');
            const downloadProgressTitle = document.getElementById('downloadProgressTitle');
            const downloadProgressStatus = document.getElementById('downloadProgressStatus');
            const downloadProgressStats = document.getElementById('downloadProgressStats');
            
            if (downloadProgressContainer) downloadProgressContainer.style.display = 'block';
            if (downloadProgressTitle) downloadProgressTitle.textContent = `Downloading: ${chaptersToDownload.length} chapters`;
            if (downloadProgressStatus) downloadProgressStatus.textContent = 'Preparing downloads...';
            if (downloadProgressStats) downloadProgressStats.textContent = `0/${chaptersToDownload.length}`;
            
            // Use correct reference to offlineAudioManager
            if (window.offlineAudioManager) {
                window.offlineAudioManager.queueMultipleChapters(chaptersToDownload)
                    .then(() => {
                        console.log(`Queued ${chaptersToDownload.length} chapters for download`);
                        this.showDownloadStatus(`Queued ${chaptersToDownload.length} chapters for download`, 'success');
                        this.updateDownloadedChaptersList();
                        this.updateStorageUsage(); // Update storage usage
                        
                        // Ensure chapter indicators are updated correctly
                        setTimeout(() => {
                            this.updateChapterDownloadIndicators();
                        }, 1000);
                        
                        // Hide the overlay as soon as downloads are queued
                        if (tempusOverlay) {
                            tempusOverlay.style.display = 'none';
                        }
                })
                .catch(error => {
                        console.error('Error queuing chapters for download:', error);
                        this.showDownloadStatus(`Error: ${error.message}`, 'error');
                        
                        // Hide overlay on error
                        if (tempusOverlay) {
                            tempusOverlay.style.display = 'none';
                        }
                });
        } else {
                console.error('Offline audio manager not initialized');
                this.showDownloadStatus('Download system not ready', 'error');
                
                // Hide overlay if manager not available
                if (tempusOverlay) {
                    tempusOverlay.style.display = 'none';
                }
            }
        } else {
            console.log('No chapters to download');
            this.showDownloadStatus('No chapters to download', 'warning');
            
            // Hide overlay if no chapters to download
            if (tempusOverlay) {
                tempusOverlay.style.display = 'none';
            }
        }
    }
    
    queueEntireAudiobook() {
        if (!this.audioData) {
            return;
        }
        
        // Show loading overlay
        const tempusOverlay = document.getElementById('tempusOverlay');
        if (tempusOverlay) {
            tempusOverlay.style.display = 'block';
        }
        
        const chaptersToDownload = [];
        
        // Process all parts and chapters
        this.audioData.parts.forEach(part => {
            part.chapters.forEach(chapter => {
                chaptersToDownload.push(this.createChapterMetadata(chapter, part));
            });
        });
        
        // Queue all chapters
        if (chaptersToDownload.length > 0) {
            this.downloadQueue = chaptersToDownload.map(metadata => ({ metadata, completed: false }));
            
            // Find progress elements or create them if needed
            const downloadProgressContainer = document.getElementById('downloadProgressContainer');
            const downloadProgressTitle = document.getElementById('downloadProgressTitle');
            const downloadProgressStatus = document.getElementById('downloadProgressStatus');
            const downloadProgressStats = document.getElementById('downloadProgressStats');
            
            if (downloadProgressContainer && downloadProgressTitle && downloadProgressStatus && downloadProgressStats) {
                downloadProgressTitle.textContent = `Downloading: ${chaptersToDownload.length} chapters`;
                downloadProgressContainer.style.display = 'block';
                downloadProgressStatus.textContent = 'Preparing downloads...';
                downloadProgressStats.textContent = `0/${chaptersToDownload.length}`;
            } else {
                console.warn('Download progress UI elements not found');
            }
            
            // Make sure we have a reference to the offline manager
            const offlineManager = window.offlineAudioManager;
            if (!offlineManager) {
                console.error('Offline audio manager not available');
                this.showDownloadStatus('Download system not ready', 'error');
                
                // Hide overlay if no offline manager
                if (tempusOverlay) {
                    tempusOverlay.style.display = 'none';
                }
                return;
            }
            
            // Queue the chapters for download
            offlineManager.queueMultipleChapters(chaptersToDownload)
                .then(results => {
                    const successCount = results.filter(success => success).length;
                    this.showDownloadStatus(`Queued ${successCount} chapters for download`, 'success');
                    
                    // Hide overlay when done
                    if (tempusOverlay) {
                        tempusOverlay.style.display = 'none';
                    }
                    
                    // Update the UI
                    this.updateDownloadedChaptersList();
                    this.updateChapterDownloadIndicators();
                    this.updateStorageUsage(); // Update storage usage
                })
                .catch(error => {
                    console.error('Error queueing chapters:', error);
                    this.showDownloadStatus(`Error queueing chapters: ${error.message}`, 'danger');
                    
                    // Hide overlay on error
                    if (tempusOverlay) {
                        tempusOverlay.style.display = 'none';
                    }
                });
        } else {
            this.showDownloadStatus('No chapters to download', 'info');
            
            // Hide overlay if no chapters to download
            if (tempusOverlay) {
                tempusOverlay.style.display = 'none';
            }
        }
    }
    
    /**
     * Remove a downloaded chapter
     * @param {string} chapterId - ID of the chapter to remove
     */
    removeDownloadedChapter(chapterId) {
        if (!window.offlineAudioManager) {
            console.error('Offline audio manager not initialized');
            return;
        }
        
        try {
            // Get the correct method name from offlineAudioManager
            const removeMethod = typeof window.offlineAudioManager.removeDownloadedChapter === 'function' 
                ? window.offlineAudioManager.removeDownloadedChapter.bind(window.offlineAudioManager)
                : window.offlineAudioManager.removeChapter?.bind(window.offlineAudioManager);
            
            if (!removeMethod) {
                console.error('No valid remove method found in offlineAudioManager');
                this.showDownloadStatus('Cannot remove chapter: method not found', 'error');
                return;
            }
            
            // Parse the chapterId to get audiobookId, partNumber, and chapterNumber
            const parts = chapterId.split('_');
            const audiobookId = parts[0];
            const partNumber = parseInt(parts[1]);
            const chapterNumber = parseInt(parts[2]);
            
            // Call the appropriate method based on how many parameters it expects
            const promise = parts.length === 3 
                ? removeMethod(audiobookId, partNumber, chapterNumber) 
                : removeMethod(chapterId);
            
            promise.then(success => {
                    if (success) {
                        console.log(`Removed chapter with ID: ${chapterId}`);
                        
                        // Update UI elements
                        this.updateDownloadedChaptersList();
                        this.updateChapterDownloadIndicators();
                        this.updateStorageUsage();
                        this.checkCurrentChapterAvailability();
                        
                        // Show status message
                        this.showDownloadStatus('Chapter removed from offline storage', 'success');
                    } else {
                        console.error(`Failed to remove chapter with ID: ${chapterId}`);
                        this.showDownloadStatus('Failed to remove chapter', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error removing chapter:', error);
                    this.showDownloadStatus(`Error: ${error.message}`, 'error');
                });
        } catch (error) {
            console.error('Error in removeDownloadedChapter:', error);
            this.showDownloadStatus(`Error: ${error.message}`, 'error');
        }
    }
    
    /**
     * Clear all downloaded chapters
     */
    clearAllDownloadedChapters() {
        console.log('Clearing all downloaded chapters');
        
        // Show confirmation dialog
        if (!confirm('Are you sure you want to clear all downloads? This action cannot be undone.')) {
            console.log('Clear downloads cancelled by user');
            return;
        }
        
        // Show loading overlay
        const tempusOverlay = document.getElementById('tempusOverlay');
        if (tempusOverlay) {
            tempusOverlay.style.display = 'block';
        }
        
        if (!window.offlineAudioManager) {
            console.error('Offline audio manager not initialized');
            this.showMessage('Cannot clear downloads - system not ready', 'error');
            if (tempusOverlay) tempusOverlay.style.display = 'none';
            return;
        }
        
        window.offlineAudioManager.removeAllDownloadedChapters(this.productId)
            .then(success => {
                if (success) {
                    console.log('Successfully cleared all downloaded chapters');
                    this.showMessage('All downloads cleared successfully', 'success');
                    
                    // Reset storage display immediately to avoid confusion
                    if (this.storageUsageDisplay) {
                        this.storageUsageDisplay.textContent = '0 B';
                    }
                    
                    // Update the downloaded chapters list
                    this.updateDownloadedChaptersList();
                    
                    // Update chapter download indicators
                    this.updateChapterDownloadIndicators();
                    
                    // Force a complete storage recalculation
                    setTimeout(() => {
                        this.updateStorageUsage();
                        
                        // Check if storage is still showing after clear and force a cache cleanup
                        setTimeout(() => {
                            if (this.storageUsageDisplay && 
                                this.storageUsageDisplay.textContent !== '0 B' && 
                                this.storageUsageDisplay.textContent !== 'Calculating...') {
                                console.log('Storage still showing usage after clear, forcing cache cleanup');
                                this.forceCacheCleanup();
                            }
                        }, 1000);
                    }, 500);
                } else {
                    console.error('Failed to clear downloaded chapters');
                    this.showMessage('Error clearing downloads', 'error');
                }
                
                // Hide the overlay
                if (tempusOverlay) {
                    tempusOverlay.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error clearing downloads:', error);
                this.showMessage('Error clearing downloads', 'error');
                if (tempusOverlay) tempusOverlay.style.display = 'none';
            });
    }
    
    // Add a method to force cache cleanup for orphaned files
    forceCacheCleanup() {
        if (!('caches' in window)) {
            return;
        }
        
        caches.open('audio-cache-v1').then(cache => {
            cache.keys().then(requests => {
                console.log(`Found ${requests.length} items in cache, checking for orphans`);
                
                // Get the list of valid URLs from the database
                if (window.offlineAudioManager && window.offlineAudioManager.initialized) {
                    window.offlineAudioManager.getDownloadedChapters(this.productId)
                        .then(chapters => {
                            const validUrls = chapters.map(chapter => chapter.url);
                            console.log(`Found ${validUrls.length} valid URLs in database`);
                            
                            // Delete any cache entries not in the valid URLs list
                            const deletePromises = requests.map(request => {
                                const url = request.url;
                                if (!validUrls.includes(url)) {
                                    console.log(`Deleting orphaned cache entry: ${url}`);
                                    return cache.delete(request);
                                }
                                return Promise.resolve(false);
                            });
                            
                            // Wait for all deletions to complete
                            Promise.all(deletePromises).then(() => {
                                console.log('Cache cleanup completed');
                                // Update storage usage one more time
                                this.updateStorageUsage();
                            });
                        });
                }
            });
        }).catch(err => {
            console.error('Error accessing cache for cleanup:', err);
        });
    }
    
    createChapterMetadata(chapter, part) {
        if (!chapter || !part) {
            console.warn('Missing chapter or part data for metadata creation');
            return { id: 'unknown', title: 'Unknown', url: '' };
        }
        
        // For sample mode, create a special ID format
        if (this.isSampleMode) {
            return {
                id: `sample-${this.productId}`,
                audiobookId: this.productId,
                title: chapter.title || 'Sample',
                url: `${this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'))}/${part.folder || ''}/${chapter.file}`
            };
        }
        
        // For normal chapters
        const chapterId = `${this.productId}-${part.part_number}-${chapter.number}`;
        
        // Construct the audio URL exactly as in setupChapter method
        const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
        
        // Determine the correct folder path for the audio file
        let audioPath;
        if (part.folder) {
            // If part has a folder property, use it
            audioPath = `${part.folder}/${chapter.file}`;
        } else {
            // Try to generate the folder name from the part title
            const partFolder = `part-${part.part_number}-${this.sanitizeFolderName(part.title)}`;
            audioPath = `${partFolder}/${chapter.file}`;
        }
        
        const audioUrl = `${baseUrl}/${audioPath}`;
        console.log(`Generated URL for download: ${audioUrl}`);
        
        return {
            id: chapterId,
            audiobookId: this.productId,
            title: chapter.title,
            partNumber: part.part_number,
            chapterNumber: chapter.number,
            url: audioUrl,
            // Add part title for better display in download UI
            partTitle: part.title || `Part ${part.part_number}`
        };
    }
    
    populateChapters() {
        if (!this.chapterList) {
            console.warn('Chapter list element not found');
            return;
        }
        
        this.chapterList.innerHTML = '';
        
        // If we're in sample mode, just display the sample
        if (this.isSampleMode) {
            const sampleTrack = this.getSampleTrack();
            if (sampleTrack) {
                const sampleDiv = document.createElement('div');
                sampleDiv.className = 'chapter-item sample-item';
                
                // Create cover image element
                const coverImg = document.createElement('img');
                const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
                coverImg.src = sampleTrack.cover ? `${baseUrl}/${sampleTrack.cover}` : `/static/images/default-cover.jpg`;
                coverImg.alt = "Sample Preview";
                coverImg.className = 'chapter-cover';
                
                // Create chapter info container
                const infoDiv = document.createElement('div');
                infoDiv.className = 'chapter-info';
                
                // Create title element
                const titleSpan = document.createElement('div');
                titleSpan.className = 'chapter-title';
                titleSpan.textContent = sampleTrack.title || (this.audioData ? this.audioData.title + " (Sample)" : "Sample Track");
                
                // Assemble the sample item
                infoDiv.appendChild(titleSpan);
                sampleDiv.appendChild(coverImg);
                sampleDiv.appendChild(infoDiv);
                
                // Add click event
                sampleDiv.addEventListener('click', () => this.playSample());
                
                this.chapterList.appendChild(sampleDiv);
                console.log('Populated chapter list with sample track');
                return;
            } else {
                console.warn('No sample track available');
                return;
            }
        }
        
        // For full audiobooks, check if parts exist
        if (!this.audioData || !this.audioData.parts || !Array.isArray(this.audioData.parts)) {
            console.warn('No parts array found in audiobook data');
            return;
        }
        
        // Regular chapter population for full audiobook
        this.audioData.parts.forEach(part => {
            const partTitle = document.createElement('h5');
            partTitle.className = 'mt-3 mb-2 px-3';
            partTitle.textContent = `Part ${part.part_number}: ${part.title}`;
            this.chapterList.appendChild(partTitle);
            
            part.chapters.forEach(chapter => {
                const chapterDiv = document.createElement('div');
                chapterDiv.className = 'chapter-item';
                chapterDiv.dataset.chapterNumber = chapter.number;
                chapterDiv.dataset.partNumber = part.part_number;
                
                // Create cover image element
                const coverImg = document.createElement('img');
                const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
                coverImg.src = chapter.cover ? `${baseUrl}/${chapter.cover}` : `${baseUrl}/default-cover.jpg`;
                coverImg.alt = chapter.title;
                coverImg.className = 'chapter-cover';
                
                // Create chapter info container
                const infoDiv = document.createElement('div');
                infoDiv.className = 'chapter-info';
                
                // Create title and number elements
                const titleSpan = document.createElement('div');
                titleSpan.className = 'chapter-title';
                titleSpan.textContent = chapter.title;
                
                const numberSpan = document.createElement('div');
                numberSpan.className = 'chapter-number';
                numberSpan.textContent = `Chapter ${chapter.number}`;
                
                // Assemble the chapter item
                infoDiv.appendChild(titleSpan);
                infoDiv.appendChild(numberSpan);
                chapterDiv.appendChild(coverImg);
                chapterDiv.appendChild(infoDiv);
                
                // Add click event
                chapterDiv.addEventListener('click', () => this.playChapter(chapter, part));
                
                this.chapterList.appendChild(chapterDiv);
            });
        });
        
        // Update download indicators after populating the list
        this.updateChapterDownloadIndicators();
    }
    
    loadSavedPosition() {
        // Skip loading position in sample mode
        if (this.isSampleMode) {
            console.log('Sample mode detected, skipping saved position loading');
            return false;
        }
        
        const savedChapterNumber = parseInt(localStorage.getItem(this.storageKeyChapter));
        const savedPartNumber = parseInt(localStorage.getItem(this.storageKeyPart));
        const savedPosition = parseFloat(localStorage.getItem(this.storageKeyPosition));
        
        // Log the saved position data for debugging
        console.log('Saved position data:', { 
            chapter: savedChapterNumber, 
            part: savedPartNumber, 
            position: savedPosition,
            raw: {
                chapter: localStorage.getItem(this.storageKeyChapter),
                part: localStorage.getItem(this.storageKeyPart),
                position: localStorage.getItem(this.storageKeyPosition)
            }
        });
        
        // Check if we have valid saved data and parts exist
        if (!isNaN(savedChapterNumber) && !isNaN(savedPartNumber) && !isNaN(savedPosition) && 
            this.audioData && this.audioData.parts && Array.isArray(this.audioData.parts)) {
            
            // Find the saved part and chapter
            const part = this.audioData.parts.find(p => p.part_number === savedPartNumber);
            if (part) {
                const chapter = part.chapters.find(c => c.number === savedChapterNumber);
                if (chapter) {
                    console.log('Loading saved position:', chapter.title, 'at', savedPosition.toFixed(2), 'seconds');
                    
                    // Set up the audio without playing it
                    this.setupChapter(chapter, part, savedPosition || 0);
                    return true;
                } else {
                    console.log(`Chapter ${savedChapterNumber} not found in part ${savedPartNumber}`);
                }
            } else {
                console.log(`Part ${savedPartNumber} not found`);
            }
        }
        
        console.log('No valid saved position found, starting with first chapter');
        
        // If no saved position or couldn't find saved chapter, start with first chapter
        if (this.audioData && this.audioData.parts && Array.isArray(this.audioData.parts) && this.audioData.parts.length > 0) {
            const firstPart = this.audioData.parts[0];
            if (firstPart.chapters && firstPart.chapters.length > 0) {
                // Find the opening credits (chapter 0) or the first chapter
                const openingCredits = firstPart.chapters.find(c => c.number === 0);
                const firstChapter = openingCredits || firstPart.chapters[0];
                
                console.log('Auto-loading first chapter:', firstChapter.title);
                this.setupChapter(firstChapter, firstPart);
                return false;
            }
        }
        
        return false;
    }
    
    loadSavedSpeed() {
        const savedSpeed = parseFloat(localStorage.getItem(this.storageKeySpeed));
        if (savedSpeed) {
            this.setPlaybackSpeed(savedSpeed);
        }
    }
    
    savePlaybackPosition() {
        if (!this.currentChapter || !this.currentPart) return;
        
        // Get current time from the appropriate source
        let currentTime = 0;
        if (this.howl) {
            currentTime = this.howl.seek() || 0;
        } else if (this.audioElement) {
            currentTime = this.audioElement.currentTime;
        }
        
        // Only save if we have a valid position
        if (currentTime > 0 && isFinite(currentTime)) {
            console.log(`Saving position: Chapter ${this.currentChapter.number}, Part ${this.currentPart.part_number}, Time ${currentTime.toFixed(2)}`);
            localStorage.setItem(this.storageKeyChapter, this.currentChapter.number);
            localStorage.setItem(this.storageKeyPart, this.currentPart.part_number);
            localStorage.setItem(this.storageKeyPosition, currentTime);
        }
    }
    
    setupChapter(chapter, part, startPosition = 0) {
        if (!chapter || !part) return;
        
        this.currentChapter = chapter;
        this.currentPart = part;
        
        // Update media session metadata for the new chapter
        if (this.mediaSessionManager) {
            this.mediaSessionManager.updateMetadata();
        }
        
        // Clear any previous errors
        this.hideError();
        
        // Update active chapter in the list
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
            if (parseInt(item.dataset.chapterNumber) === chapter.number && 
                parseInt(item.dataset.partNumber) === part.part_number) {
                item.classList.add('active');
                // Scroll to the active chapter
                item.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
        
        // Update UI to show current chapter
        this.updateChapterInfo();
        
        // Construct the audio URL
        const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
        
        // Determine the correct folder path for the audio file
        let audioPath;
        if (part.folder) {
            // If part has a folder property, use it
            audioPath = `${part.folder}/${chapter.file}`;
        } else {
            // Try to generate the folder name from the part title
            const partFolder = `part-${part.part_number}-${this.sanitizeFolderName(part.title)}`;
            audioPath = `${partFolder}/${chapter.file}`;
        }
        
        const audioUrl = `${baseUrl}/${audioPath}`;
        console.log(`Loading audio from: ${audioUrl}`);
        
        // Store the URL for potential recovery
        this.currentAudioUrl = audioUrl;
        
        // Stop any existing howl instance
        if (this.howl) {
            this.howl.stop();
            this.howl.unload();
            this.howl = null;
        }
        
        // Check if we're in offline mode - prioritize loading from cache immediately
        if (this.isOffline) {
            console.log('Device is offline, loading directly from cache...');
            
            // Tell user they're listening in offline mode
            this.showMessage('Playing in offline mode', 'info');
            
            // Check if chapter is available offline and update UI
            this.checkCurrentChapterAvailability();
            
            // Try to load directly from IndexedDB or cache
            this.loadFromIndexedDBIfAvailable(audioUrl, startPosition)
                .then(success => {
                    if (!success) {
                        // If not found in IndexedDB, try Firefox specific cache loading
                        console.log('Not found in IndexedDB, trying browser cache...');
                        this.loadAudioFromCacheForFirefox(audioUrl, startPosition);
                    }
                })
                .catch(error => {
                    console.error('Error loading from offline storage:', error);
                    this.showError('Error loading offline audio. Please try another chapter.');
                });
            
            // Add a refresh notice if it's the first offline play
            if (!this.hasShownOfflineRefreshNotice) {
                setTimeout(() => {
                    this.showMessage('If audio doesn\'t play, try refreshing the page', 'warning');
                    this.hasShownOfflineRefreshNotice = true;
                }, 3000);
            }
            
            return; // Exit early, we're handling offline mode separately
        }
        
        // Online mode - use normal Howl playback
            try {
                console.log("Creating Howl instance for:", audioUrl);
                this.howl = new Howl({
                    src: [audioUrl],
                    html5: true, // Use HTML5 Audio for better streaming support
                    preload: true,
                    autoplay: false,
                    rate: this.playbackSpeed,
                    onload: () => {
                        console.log('Howl: Audio loaded successfully');
                        if (startPosition > 0) {
                            this.howl.seek(startPosition);
                        }
                        // Update the duration display
                        this.updateDuration();
                    },
                    onloaderror: (id, error) => {
                        console.error('Howl: Error loading audio:', error);
                    
                    // If we're offline now (might have gone offline after setup started)
                    if (!navigator.onLine) {
                        console.log('Network appears to be offline now, trying cached version...');
                        this.isOffline = true;
                        this.updateOfflineStatusUI();
                            this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
                        return;
                    }
                    
                    // Online fallbacks
                            if (!part.folder) {
                                // Try a simpler fallback path structure
                                console.log('Audio load error, trying fallback path...');
                                const fallbackFolder = `part-${part.part_number}-the-origin`;
                                const fallbackUrl = `${baseUrl}/${fallbackFolder}/${chapter.file}`;
                                
                                console.log(`Attempting to load from fallback path: ${fallbackUrl}`);
                                
                                // Store the fallback path for future use
                                part.folder = fallbackFolder;
                                this.currentAudioUrl = fallbackUrl;
                                
                                // Try with new Howl instance
                                this.howl.unload();
                                this.howl = new Howl({
                                    src: [fallbackUrl],
                                    html5: true,
                                    preload: true,
                                    autoplay: false,
                                    rate: this.playbackSpeed,
                                    onloaderror: () => {
                                // As a last resort, try loading from cache
                                console.log('Fallback failed, checking if available in cache...');
                                this.loadFromIndexedDBIfAvailable(fallbackUrl, startPosition);
                                    }
                                });
                            } else {
                        // Try loading from cache as fallback
                        console.log('Standard path failed, checking if available in cache...');
                        this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
                        }
                    },
                    onplay: () => {
                        this.isPlaying = true;
                        this.playPauseIcon.className = 'fas fa-pause';
                        
                        // Start progress updating
                        this.startProgressUpdater();
                        
                        // Notify Media Session Manager
                        if (this.mediaSessionManager) {
                            this.mediaSessionManager.handlePlaybackStateChange(true);
                        }
                    },
                    onpause: () => {
                        this.isPlaying = false;
                        this.playPauseIcon.className = 'fas fa-play';
                        
                        // Stop progress updating
                        this.stopProgressUpdater();
                        
                        // Notify Media Session Manager
                        if (this.mediaSessionManager) {
                            this.mediaSessionManager.handlePlaybackStateChange(false);
                        }
                    },
                    onstop: () => {
                        this.isPlaying = false;
                        this.playPauseIcon.className = 'fas fa-play';
                        
                        // Stop progress updating
                        this.stopProgressUpdater();
                        
                        // Notify Media Session Manager
                        if (this.mediaSessionManager) {
                            this.mediaSessionManager.handlePlaybackStateChange(false);
                        }
                    },
                    onend: () => {
                        console.log("Audio playback ended, playing next chapter automatically");
                        this.isPlaying = false;
                        this.playPauseIcon.className = 'fas fa-play';
                        
                        // Automatically play the next chapter when current one ends
                        this.playNextChapter();
                    },
                    onseek: () => {
                        // Update progress when seeking
                        this.updateProgress();
                        
                        // Notify Media Session Manager
                        if (this.mediaSessionManager) {
                            this.mediaSessionManager.updatePositionState();
                        }
                    }
                });
            } catch (error) {
                console.error("Error initializing Howler:", error);
            this.fallbackToNativeAudio(audioUrl, startPosition);
        }
        
        // Update UI to show ready state
        this.isPlaying = false;
        this.playPauseIcon.className = 'fas fa-play';
        
        // Set playback speed from saved preference
        this.setPlaybackSpeed(this.playbackSpeed);
        
        // Update chapter cover if available
        if (chapter.cover) {
            this.currentChapterCover.src = `${baseUrl}/${chapter.cover}`;
            this.currentChapterCover.style.display = 'block';
        } else if (part.cover) {
            this.currentChapterCover.src = `${baseUrl}/${part.cover}`;
            this.currentChapterCover.style.display = 'block';
        } else {
            // Use a default cover
            this.currentChapterCover.src = '/static/images/default-cover.jpg';
            this.currentChapterCover.style.display = 'block';
        }
        
        // Add fade-in animation to cover
        this.currentChapterCover.classList.remove('fade-in');
        void this.currentChapterCover.offsetWidth; // Trigger reflow
        this.currentChapterCover.classList.add('fade-in');
    }
    
    // Fall back to native audio if Howler fails
    fallbackToNativeAudio(audioUrl, startPosition = 0) {
        console.log("Falling back to native Audio element");
        // Legacy code: Set up the native audio element as before
        this.audioElement.src = audioUrl;
        this.audioElement.currentTime = startPosition;
        
        // Handle errors loading the audio file
        this.audioElement.onerror = () => {
            console.log('Audio load error for URL:', audioUrl);
            
            // Try the fallback path first
            if (!this.currentPart.folder) {
                console.log('Audio load error, trying fallback path...');
                
                // Try a simpler fallback path structure
                const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
                const fallbackFolder = `part-${this.currentPart.part_number}-the-origin`;
                const fallbackUrl = `${baseUrl}/${fallbackFolder}/${this.currentChapter.file}`;
                
                console.log(`Attempting to load from fallback path: ${fallbackUrl}`);
                
                // Store the fallback path for future use
                this.currentPart.folder = fallbackFolder;
                this.currentAudioUrl = fallbackUrl;
                
                // Try the fallback path
                this.audioElement.src = fallbackUrl;
                this.audioElement.currentTime = startPosition;
                
                // We'll let the onerror handler fire again if this also fails
                return;
            }
            
            // If we're offline, try loading from cache directly
            if (this.isOffline) {
                const isFirefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
                if (isFirefox) {
                    this.loadAudioFromCacheForFirefox(audioUrl, startPosition);
                    return;
                }
                
                // For other browsers, check if we can find it in the cache
                this.loadAudioFromCacheDirectly(audioUrl, startPosition);
                return;
            }
            
            // If we're already using a fallback or we're offline, show error
            this.handleAudioError();
            
            // If we're offline, recommend downloading for offline use
            if (this.isOffline) {
                this.showError('This chapter is not available offline. Please connect to the internet and download it first.');
            }
        };
    }
    
    /**
     * Load audio from IndexedDB if available
     * @returns {Promise<boolean>} - Promise resolving to true if audio was loaded successfully
     */
    async loadFromIndexedDBIfAvailable(audioUrl, startPosition = 0) {
        console.log("Attempting to load from IndexedDB:", audioUrl);
        
        try {
            // Check if offline manager is available
        if (!window.offlineAudioManager) {
                console.log("Offline audio manager not initialized");
                return false;
            }
            
            // Get the metadata from the URL to query IndexedDB
            const urlParts = audioUrl.split('/');
            const filename = urlParts[urlParts.length - 1];
            const folderPart = urlParts[urlParts.length - 2]; // e.g. "part-1-the-origin"
            
            // Extract part and chapter numbers from the filename and folder
            const chapterMatch = filename.match(/(\d+)/);
            const partMatch = folderPart.match(/part-(\d+)/);
            
            const chapterNumber = chapterMatch ? parseInt(chapterMatch[1], 10) : null;
            const partNumber = partMatch ? parseInt(partMatch[1], 10) : null;
            
            if (!chapterNumber || !partNumber) {
                console.warn("Could not extract chapter or part number from URL");
                return false;
            }
            
            console.log(`Extracted part ${partNumber}, chapter ${chapterNumber} from URL`);
            
            // Try to get the audio from the offline manager
            const audioData = await window.offlineAudioManager.getChapterAudio(
                this.productId,
                partNumber, 
                chapterNumber
            );
            
                if (!audioData || !audioData.blob) {
                console.log("Audio not available in IndexedDB");
                return false;
            }
            
            console.log("Found audio in IndexedDB!", audioData);
            
            // Process the blob for playback
            return await this.processBlobForPlayback(audioData.blob, audioUrl, startPosition);
        } catch (error) {
            console.error("Error loading from IndexedDB:", error);
            return false;
        }
    }
    
    /**
     * Process audio blob for playback with Howl
     * @returns {Promise<boolean>} - Promise resolving to true if successful
     */
    async processBlobForPlayback(blob, audioUrl, startPosition = 0) {
        console.log("Processing blob for playback, size:", blob.size, "bytes");
        
        try {
            // Clean up existing Howl instance if it exists
            if (this.howl) {
                this.howl.unload();
                this.howl = null;
            }
            
            // Get file extension from URL
            const fileExtension = audioUrl.split('.').pop().toLowerCase();
            
            // Determine content type based on file extension
            let contentType;
            switch (fileExtension) {
                case 'mp3':
                    contentType = 'audio/mpeg';
                    break;
                case 'wav':
                    contentType = 'audio/wav';
                    break;
                case 'ogg':
                    contentType = 'audio/ogg';
                    break;
                case 'm4a':
                case 'm4b':
                    contentType = 'audio/mp4';
                    break;
                default:
                    contentType = 'audio/mpeg';
            }
            
            // Create a properly typed blob
        const typedBlob = new Blob([blob], { type: contentType });
        
            // Create object URL
        const blobUrl = URL.createObjectURL(typedBlob);
            console.log("Created blob URL:", blobUrl);
            
            return new Promise((resolve, reject) => {
                try {
                    // Create a new Howl instance with the blob URL
                    console.log("Creating Howl instance from blob URL");
            this.howl = new Howl({
                src: [blobUrl],
                html5: true,
                        format: [fileExtension], // Explicitly specify format
                preload: true,
                autoplay: false,
                rate: this.playbackSpeed,
                onload: () => {
                            console.log("Offline audio loaded successfully with Howler");
                            
                            // Set the position if needed
                    if (startPosition > 0) {
                        this.howl.seek(startPosition);
                    }
                            
                            // Update duration
                    this.updateDuration();
                    
                            // Notify UI that we're using offline mode
                            this.showMessage('Playing from offline storage', 'success');
                            
                            resolve(true);
                        },
                        onloaderror: (id, err) => {
                            console.error("Error loading offline audio with Howler:", err);
                            
                            // Try a different approach as fallback
                            console.log("Trying direct audio element approach as fallback");
                            URL.revokeObjectURL(blobUrl);
                            
                            // If Howler fails, try with native audio element
                            this.fallbackToDirectAudioElement(URL.createObjectURL(blob), startPosition)
                                .then(() => resolve(true))
                                .catch(err => {
                                    console.error("Native audio element fallback also failed:", err);
                                    this.showError('Error playing offline audio. Please try another chapter.');
                                    resolve(false);
                                });
                        },
                        onplayerror: (id, err) => {
                            console.error("Error playing offline audio with Howler:", err);
                            this.showError('Error playing offline audio. Please try refreshing the page.');
                            resolve(false);
                },
                onend: () => {
                    console.log("Audio playback ended from offline storage");
                    
                    // Clean up blob URL
                    URL.revokeObjectURL(blobUrl);
                    
                    // Auto-play next chapter
                    // First reset playing state so playNextChapter knows to auto-play
                    this.isPlaying = true;  
                    this.playNextChapter();
                },
                onplay: () => {
                    this.isPlaying = true;
                    this.playPauseIcon.className = 'fas fa-pause';
                    
                    // Start progress updating
                    this.startProgressUpdater();
                    
                    // Notify Media Session Manager
                    if (this.mediaSessionManager) {
                        this.mediaSessionManager.handlePlaybackStateChange(true);
                    }
                },
                onpause: () => {
                    this.isPlaying = false;
                    this.playPauseIcon.className = 'fas fa-play';
                    
                    // Stop progress updating
                    this.stopProgressUpdater();
                    
                    // Notify Media Session Manager
                    if (this.mediaSessionManager) {
                        this.mediaSessionManager.handlePlaybackStateChange(false);
                    }
                }
            });
        } catch (error) {
                    console.error("Error creating Howl instance for offline audio:", error);
                    
                    // Try with native audio element as fallback
                    this.fallbackToDirectAudioElement(URL.createObjectURL(blob), startPosition)
                        .then(() => resolve(true))
                        .catch(err => {
                            console.error("Native audio element fallback also failed:", err);
                            this.showError('Error playing offline audio. Please try another chapter.');
                            resolve(false);
                        });
                }
            });
        } catch (error) {
            console.error("Error in processBlobForPlayback:", error);
            this.showError('Error processing audio file. Please try another chapter.');
            return false;
        }
    }
    
    // Show a notification suggesting to refresh the page for offline mode
    showRefreshSuggestion() {
        // Only show once per session
        if (this.hasShownRefreshSuggestion) return;
        this.hasShownRefreshSuggestion = true;
        
        // Create a persistent notification element if it doesn't exist
        let notification = document.getElementById('offlineRefreshNotification');
        if (!notification) {
            notification = document.createElement('div');
            notification.id = 'offlineRefreshNotification';
            notification.className = 'offline-refresh-notification';
            
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-sync-alt"></i>
                    <div class="message">
                        <strong>Playing in offline mode</strong>
                        <p>If audio doesn't play, try refreshing the page (F5)</p>
                    </div>
                    <button id="closeRefreshNotification">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // Add to the top of the player container
            const playerContainer = document.getElementById('audioPlayerContainer');
            if (playerContainer) {
                playerContainer.insertAdjacentElement('afterbegin', notification);
                
                // Add styles for the notification
                const style = document.createElement('style');
                style.textContent = `
                    .offline-refresh-notification {
                        background-color: #ffc107;
                        color: #212529;
                        padding: 10px 15px;
                        margin-bottom: 15px;
                        border-radius: 4px;
                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        width: 100%;
                        animation: fadeIn 0.5s;
                    }
                    
                    .notification-content {
                        display: flex;
                        align-items: center;
                    }
                    
                    .notification-content i {
                        font-size: 20px;
                        margin-right: 15px;
                    }
                    
                    .notification-content .message {
                        flex: 1;
                    }
                    
                    .notification-content .message p {
                        margin: 5px 0 0 0;
                        font-size: 14px;
                    }
                    
                    .notification-content button {
                        background: none;
                        border: none;
                        color: #212529;
                        cursor: pointer;
                        padding: 5px;
                    }
                    
                    @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(-10px); }
                        to { opacity: 1; transform: translateY(0); }
                    }
                `;
                document.head.appendChild(style);
                
                // Add close button handler
                const closeButton = document.getElementById('closeRefreshNotification');
                if (closeButton) {
                    closeButton.addEventListener('click', () => {
                        notification.style.display = 'none';
                    });
                }
            }
        } else {
            // Just make sure it's visible
            notification.style.display = 'block';
        }
    }
    
    // Show a message to the user (success, info, warning, error)
    showMessage(message, type = 'info') {
        // Avoid showing messages if error is already shown
        if (this.errorMessage.style.display === 'block') {
            return;
        }
        
        // Create or reuse message element
        let messageElement = document.getElementById('playerMessage');
        if (!messageElement) {
            messageElement = document.createElement('div');
            messageElement.id = 'playerMessage';
            messageElement.classList.add('player-message');
            
            // Add to DOM near the play controls
            const controlsContainer = document.querySelector('.audio-controls');
            if (controlsContainer) {
                controlsContainer.appendChild(messageElement);
            } else {
                // Fallback to append near error message
                this.errorMessage.parentNode.appendChild(messageElement);
            }
        }
        
        // Set message content and style
        messageElement.textContent = message;
        messageElement.className = `player-message ${type}`;
        messageElement.style.display = 'block';
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            messageElement.style.display = 'none';
        }, 3000);
    }
    
    /**
     * Method to handle Firefox-specific caching issues
     * @returns {Promise<boolean>} Whether the audio was successfully loaded
     */
    async loadAudioFromCacheForFirefox(audioUrl, startPosition = 0) {
        console.log("🦊 Firefox-specific cache loading for:", audioUrl);
        
        try {
            // First try our improved IndexedDB approach
        if (window.offlineAudioManager) {
                const success = await this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
                if (success) {
                    console.log("Successfully loaded audio from IndexedDB");
                    return true;
                }
            }
            
            // If we get here, try our improved direct cache approach
            return await this.loadFromCache(audioUrl, startPosition);
        } catch (error) {
            console.error("Error in Firefox cache loading:", error);
            this.showError('Unable to load audio in offline mode. Please try another chapter or reconnect to the internet.');
            return false;
        }
    }
    
    async loadFromCache(audioUrl, startPosition = 0) {
        try {
            console.log('Trying to load audio from cache:', audioUrl);
            
            // See if we have a cached blob
            let audioBlob = null;
            
            // First try to get from cache with improved error handling
            if (window.offlineAudioManager) {
                audioBlob = await window.offlineAudioManager.retrieveBlobFromCache(audioUrl);
            }
            
            // If blob exists, process it
            if (audioBlob) {
                console.log('Successfully loaded from cache, size:', audioBlob.size);
                return await this.processBlobForPlayback(audioBlob, audioUrl, startPosition);
                } else {
                console.log('Cache miss, will try IndexedDB');
                return await this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
            }
        } catch (error) {
            console.warn('Error loading from cache:', error.message);
            return await this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
        }
    }
    
    // Load audio from browser cache
    loadFromCache(audioUrl, startPosition = 0) {
        console.log("Attempting to load from browser cache:", audioUrl);
        
        if ('caches' in window) {
            caches.open('audio-cache-v1').then(cache => {
                cache.match(audioUrl).then(response => {
                            if (response) {
                                response.blob().then(blob => {
                            console.log("Found in cache, processing blob:", blob);
                            this.processBlobForPlayback(blob, audioUrl, startPosition);
                                }).catch(error => {
                            console.warn("Error processing blob from cache:", error);
                            this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
                                });
                            } else {
                        console.log("Not found in cache, trying IndexedDB");
                        this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
                            }
                        }).catch(error => {
                    console.warn("Error matching from cache:", error);
                    this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
                        });
                }).catch(error => {
                console.warn("Error opening cache:", error);
                this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
            });
        } else {
            console.log("Cache API not available, trying IndexedDB");
            this.loadFromIndexedDBIfAvailable(audioUrl, startPosition);
        }
    }
    
    // Determine content type more accurately
    determineContentType(filename, blob) {
        // Try to use the blob's type if available
        if (blob.type && blob.type.includes('audio')) {
            console.log("Using blob's content type:", blob.type);
            return blob.type;
        }
        
        // Otherwise determine by file extension
        let contentType = 'audio/mpeg'; // Default to MP3
        
        if (filename.endsWith('.mp3')) {
            contentType = 'audio/mpeg';
        } else if (filename.endsWith('.m4a')) {
            contentType = 'audio/mp4';
        } else if (filename.endsWith('.ogg')) {
            contentType = 'audio/ogg';
        } else if (filename.endsWith('.wav')) {
            contentType = 'audio/wav';
        } else if (filename.endsWith('.flac')) {
            contentType = 'audio/flac';
        } else if (filename.endsWith('.aac')) {
            contentType = 'audio/aac';
        }
        
        return contentType;
    }
    
    // Fallback to native audio element when Howler fails with cached audio
    fallbackToDirectAudioElement(blobUrl, startPosition = 0) {
        console.log("Using direct Audio element for offline playback");
        
        // Create a new audio element for better compatibility
        const tempAudio = new Audio();
        tempAudio.src = blobUrl;
        tempAudio.preload = 'auto';
        
        // Add event listeners
        tempAudio.addEventListener('loadedmetadata', () => {
            console.log("Offline audio loaded with native Audio element");
            
            // Replace our existing audio element's source
            this.audioElement.src = blobUrl;
            this.audioElement.currentTime = startPosition;
            
            // Set up play and error handling
            this.audioElement.onplay = () => {
                this.isPlaying = true;
                this.playPauseIcon.className = 'fas fa-pause';
                this.startProgressUpdater();
            };
            
            this.audioElement.onpause = () => {
                this.isPlaying = false;
                this.playPauseIcon.className = 'fas fa-play';
                this.stopProgressUpdater();
            };
            
            this.audioElement.onended = () => {
                URL.revokeObjectURL(blobUrl);
                this.playNextChapter();
            };
            
            // Clean up the temporary audio element
            tempAudio.src = '';
            
            // Try to play if we were playing before
            if (this.isPlaying) {
                this.audioElement.play().catch(error => {
                    console.error("Error auto-playing cached audio:", error);
                });
            }
        });
        
        tempAudio.addEventListener('error', (e) => {
            console.error("Error loading cached audio with fallback:", e);
            URL.revokeObjectURL(blobUrl);
            this.handleAudioError();
            this.showError('Could not play the offline audio file. Please try again or reconnect to the internet.');
        });
    }
    
    // Updated methods to use Howler instead of native Audio
    
    playAudio() {
        console.log("Play audio called");
        
        // Set playing state immediately
        this.isPlaying = true;
        this.playPauseIcon.className = 'fas fa-pause';
        
        if (this.howl) {
            // Start the Howler playback
            this.howl.play();
            
            // Make sure progress updater is running
            this.startProgressUpdater();
            
            console.log("Started playback with Howler");
        } else if (this.audioElement) {
            // Legacy fallback with native Audio element
            this.audioElement.play().then(() => {
                console.log("Started playback with native audio");
                this.hideError();
                this.hideAutoplayMessage();
                // Start progress updater for native audio too
                this.startProgressUpdater();
            }).catch(error => {
                console.log('Autoplay prevented:', error);
                this.isPlaying = false;
                this.playPauseIcon.className = 'fas fa-play';
                
                if (error.name === 'NotAllowedError' && this.autoplayMessage) {
                    const hideAutoplayMessage = localStorage.getItem(this.storageKeyHideAutoplayMessage);
                    if (hideAutoplayMessage !== 'true') {
                        this.autoplayMessage.style.display = 'block';
                    }
                } else if (error.name !== 'NotAllowedError') {
                    this.handleAudioError();
                }
            });
        }
        
        // Force icon update after a small delay to ensure it's correct
        setTimeout(() => {
            if (this.isPlaying) {
                this.playPauseIcon.className = 'fas fa-pause';
            }
        }, 200);
        
        // Notify Media Session Manager of playback state change
        if (this.mediaSessionManager) {
            this.mediaSessionManager.handlePlaybackStateChange(true);
        }
    }
    
    pauseAudio() {
        console.log("Pause audio called");
        
        // Set paused state immediately
        this.isPlaying = false;
        this.playPauseIcon.className = 'fas fa-play';
        
        if (this.howl) {
            this.howl.pause();
            console.log("Paused Howler playback");
        } else if (this.audioElement) {
            this.audioElement.pause();
            console.log("Paused native audio");
        }
        
        // Stop the progress updater
        this.stopProgressUpdater();
        
        // Notify Media Session Manager of playback state change
        if (this.mediaSessionManager) {
            this.mediaSessionManager.handlePlaybackStateChange(false);
        }
    }
    
    // Updated togglePlayPause to ensure icon updates properly
    togglePlayPause() {
        console.log("Toggle play/pause called");
        
        if (this.howl) {
            if (this.howl.playing()) {
                console.log("Howl is playing, pausing...");
                this.howl.pause();
                this.isPlaying = false;
                this.playPauseIcon.className = 'fas fa-play';
            } else {
                console.log("Howl is paused, playing...");
                this.howl.play();
                this.isPlaying = true;
                this.playPauseIcon.className = 'fas fa-pause';
                
                // Ensure progress updater is running
                this.startProgressUpdater();
            }
        } else if (this.audioElement) {
            // Legacy fallback
            if (this.audioElement.paused) {
                console.log("Audio element is paused, playing...");
                this.playWithRetry().then(success => {
                    if (success) {
                        this.isPlaying = true;
                        this.playPauseIcon.className = 'fas fa-pause';
                    }
                });
            } else {
                console.log("Audio element is playing, pausing...");
                this.audioElement.pause();
                this.isPlaying = false;
                this.playPauseIcon.className = 'fas fa-play';
            }
        }
        
        // Force update the icon after a small delay
        setTimeout(() => {
            if (this.isPlaying) {
                this.playPauseIcon.className = 'fas fa-pause';
            } else {
                this.playPauseIcon.className = 'fas fa-play';
            }
        }, 200);
    }
    
    // Update progress method that works with both Howler and native Audio
    updateProgress() {
        let currentTime = 0;
        let duration = 0;
        
        if (this.howl && this.howl.playing()) {
            // Use the Howler instance for progress updates when it's playing
            currentTime = this.howl.seek() || 0;
            duration = this.howl.duration() || 0;
            
            // REMOVING THIS SECTION TO PREVENT INFINITE RECURSION
            // Make sure the progress updater is running
            // if (!this.progressInterval) {
            //    this.startProgressUpdater();
            // }
        } else if (this.audioElement) {
            // Fallback to the native audio element
            currentTime = this.audioElement.currentTime;
            duration = this.audioElement.duration;
        }
        
        if (!duration || isNaN(duration) || duration === Infinity) {
            return;
        }
        
        const percentage = (currentTime / duration) * 100;
        
        // Only update UI if values are valid
        if (isFinite(percentage) && percentage >= 0 && percentage <= 100) {
            this.progressBar.style.width = `${percentage}%`;
            this.currentTimeDisplay.textContent = this.formatTime(currentTime);
            this.durationDisplay.textContent = this.formatTime(duration);
            
            // Update the progress position custom property for the touch indicator
            if (window.innerWidth <= 768) {
                this.progressContainer.style.setProperty('--progress-position', `${percentage}%`);
            }
            
            // Update the MediaSession position state if available
            if ('mediaSession' in navigator && window.MediaMetadata) {
                try {
                    navigator.mediaSession.setPositionState({
                        duration: duration,
                        playbackRate: this.playbackSpeed,
                        position: currentTime
                    });
                } catch (e) {
                    // Ignore errors with media session API
                    console.log('Media Session API error:', e);
                }
            }
            
            // Save position every few updates while playing
            if (this.progressUpdateCount === undefined) {
                this.progressUpdateCount = 0;
            }
            
            this.progressUpdateCount++;
            if (this.progressUpdateCount >= 5) { // Save every 5 seconds
                this.savePlaybackPosition();
                this.progressUpdateCount = 0;
            }
        }
    }
    
    // Progress updater function for Howler
    startProgressUpdater() {
        this.stopProgressUpdater(); // Clear any existing interval
        
        // Force an immediate update
        this.updateProgress();
        
        this.progressInterval = setInterval(() => {
            this.updateProgress();
        }, 1000);
        
        console.log("Progress updater started");
    }
    
    stopProgressUpdater() {
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
            console.log("Progress updater stopped");
        }
    }
    
    setProgress(event) {
        const width = this.progressContainer.clientWidth;
        const clickX = event.offsetX;
        let duration = 0;
        
        if (this.howl) {
            duration = this.howl.duration() || 0;
        } else if (this.audioElement) {
            duration = this.audioElement.duration || 0;
        }
        
        if (isNaN(duration) || !isFinite(duration)) {
            return;
        }
        
        // Update the visual touch point position
        this.updateProgressTouchPoint(event);
        
        // Calculate the seek position
        const seekPosition = (clickX / width) * duration;
        
        // Perform the seek operation
        if (this.howl) {
            this.howl.seek(seekPosition);
        } else if (this.audioElement) {
            this.audioElement.currentTime = seekPosition;
        }
        
        // Update the UI
        this.updateProgress();
        
        // Notify Media Session Manager
        if (this.mediaSessionManager) {
            this.mediaSessionManager.updatePositionState();
        }
    }
    
    setPlaybackSpeed(speed) {
        this.playbackSpeed = speed;
        
        if (this.howl) {
            this.howl.rate(speed);
        } else if (this.audioElement) {
            this.audioElement.playbackRate = speed;
        }
        
        this.speedButton.textContent = `${speed}x`;
        
        // Update active speed option
        document.querySelectorAll('.speed-option').forEach(option => {
            option.classList.remove('active');
            if (parseFloat(option.dataset.speed) === speed) {
                option.classList.add('active');
            }
        });
        
        // Save speed preference
        localStorage.setItem(this.storageKeySpeed, speed);
    }
    
    handleAudioError() {
        this.showError('Error loading audio file. Please try another chapter.');
        this.pauseAudio();
    }
    
    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
    }
    
    hideError() {
        if (this.errorMessage) {
            this.errorMessage.style.display = 'none';
        }
    }
    
    // Add this helper method to get the sample track data
    getSampleTrack() {
        if (!this.audioData) {
            console.warn('No audioData available');
            return null;
        }
        
        // Support both formats: sample and sample_track
        return this.audioData.sample || this.audioData.sample_track;
    }
    
    // Update the playSample method
    playSample() {
        // Hide any previous error
        this.hideError();
        
        // Get the sample track
        const sampleTrack = this.getSampleTrack();
        
        if (!sampleTrack) {
            this.showError('No sample track available.');
            return;
        }
        
        console.log('Playing sample track:', sampleTrack);
        
        // Update UI
        document.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
            if (item.classList.contains('sample-item')) {
                item.classList.add('active');
            }
        });
        
        // Update current info
        if (this.currentChapterTitle) {
            this.currentChapterTitle.textContent = sampleTrack.title || (this.audioData ? this.audioData.title + " (Sample)" : "Sample Track");
        }
        if (this.currentPartTitle) {
        this.currentPartTitle.textContent = "Sample Track";
        }
        
        // Since we're playing a sample, we don't have a real chapter/part structure
        // We'll create temporary objects to store what we need
        this.currentChapter = {
            title: sampleTrack.title || "Sample",
            file: sampleTrack.file,
            number: 0
        };
        
        this.currentPart = {
            title: "Sample",
            part_number: 0,
            folder: sampleTrack.folder || ""
        };
        
        // Construct the audio URL
        const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
        
        // Get the correct sample file path
        let audioUrl;
        if (sampleTrack.folder) {
            // If sample track has a folder path
            audioUrl = `${baseUrl}/${sampleTrack.folder}/${sampleTrack.file}`;
        } else {
            // Samples are typically in the root directory
            audioUrl = `${baseUrl}/${sampleTrack.file}`;
        }
        
        console.log(`Loading sample from: ${audioUrl}`);
        
        // Check if audio element exists
        if (!this.audioElement) {
            console.error('Audio element not found');
            this.showError('Audio player not ready. Please try refreshing the page.');
            return;
        }
        
        // Set the audio source directly
        this.audioElement.src = audioUrl;
        this.audioElement.currentTime = 0;
        
        // Handle error
        this.audioElement.onerror = () => {
            console.error('Error loading sample audio');
            this.showError('Error loading sample audio. Please try refreshing the page.');
        };
        
        // Update cover if available and element exists
        if (this.currentChapterCover) {
            let coverUrl = '/static/images/default-cover.jpg'; // Default fallback
            
        if (sampleTrack.cover) {
                coverUrl = `${baseUrl}/${sampleTrack.cover}`;
            } else if (this.audioData && this.audioData.cover) {
                coverUrl = `${baseUrl}/${this.audioData.cover}`;
            }
            
            this.currentChapterCover.src = coverUrl;
            this.currentChapterCover.style.display = 'block';
        
        // Add fade-in animation to cover
        this.currentChapterCover.classList.remove('fade-in');
        void this.currentChapterCover.offsetWidth; // Trigger reflow
        this.currentChapterCover.classList.add('fade-in');
        }
        
        // Set initial state
        this.isPlaying = false;
        if (this.playPauseIcon) {
        this.playPauseIcon.className = 'fas fa-play';
        }
        
        // Set up media session for sample
        if ('mediaSession' in navigator) {
            navigator.mediaSession.metadata = new MediaMetadata({
                title: sampleTrack.title || 'Sample Track',
                artist: this.audioData ? this.audioData.author || 'Unknown' : 'Unknown',
                album: this.audioData ? this.audioData.title + ' (Sample)' : 'Sample Track',
                artwork: [{ src: this.currentChapterCover ? this.currentChapterCover.src : '/static/images/default-cover.jpg' }]
            });
            
            // Set up media session actions
            navigator.mediaSession.setActionHandler('play', () => this.playAudio());
            navigator.mediaSession.setActionHandler('pause', () => this.pauseAudio());
            navigator.mediaSession.setActionHandler('seekbackward', () => this.skipBackward());
            navigator.mediaSession.setActionHandler('seekforward', () => this.skipForward());
        }
        
        // Attempt to play the sample automatically if allowed
        const playPromise = this.audioElement.play();
        if (playPromise !== undefined) {
            playPromise
                .then(() => {
                    this.isPlaying = true;
                    if (this.playPauseIcon) {
                        this.playPauseIcon.className = 'fas fa-pause';
                    }
                    this.startProgressUpdater();
                })
                .catch(error => {
                    // Auto-play was prevented, user needs to click play
                    console.warn('Auto-play prevented:', error);
                    this.isPlaying = false;
                    if (this.playPauseIcon) {
                        this.playPauseIcon.className = 'fas fa-play';
                    }
                });
        }
    }
    
    // Add new method for autoplay
    attemptAutoplay() {
        // This method is kept for backward compatibility
        // but now only shows a message without attempting to autoplay
        console.log('Autoplay disabled due to browser restrictions');
                
                // Ensure the play button shows correctly
                this.isPlaying = false;
                this.playPauseIcon.className = 'fas fa-play';
                
        // Show the autoplay message
        this.showAutoplayMessage();
    }
    
    checkAutoplayMessagePreference() {
        const hideAutoplayMessage = localStorage.getItem(this.storageKeyHideAutoplayMessage);
        if (hideAutoplayMessage === 'true') {
            this.hideAutoplayMessage();
        }
    }
    
    hideAutoplayMessage() {
        if (this.autoplayMessage) {
            this.autoplayMessage.style.display = 'none';
        }
    }
    
    hideAutoplayMessagePermanently() {
        localStorage.setItem(this.storageKeyHideAutoplayMessage, 'true');
        this.hideAutoplayMessage();
    }
    
    /**
     * Download the current chapter audio file
     */
    downloadCurrentChapter() {
        // Redirects to the offline storage system
        this.queueCurrentChapter();
    }
    
    /**
     * Show download status message
     * @param {string} message - Message to show
     * @param {string} type - Message type (success, error, warning, info)
     */
    showDownloadStatus(message, type = 'success') {
        // Safety check
        const statusElement = document.getElementById('downloadStatus');
        const statusTextElement = document.getElementById('downloadStatusText');
        
        if (!statusElement || !statusTextElement) {
            console.error('Download status elements not found in DOM');
            return;
        }
        
        // Update the message
        statusTextElement.textContent = message;
        
        // Set the appropriate style based on type
        statusElement.className = `alert alert-${type} mt-2`;
        
        // Show the status element
        statusElement.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 5000);
    }
    
    /**
     * Sanitize a filename to remove invalid characters
     * @param {string} name - Filename to sanitize
     * @returns {string} - Sanitized filename
     */
    sanitizeFileName(name) {
        // Replace invalid characters with underscores
        return name.replace(/[/\\?%*:|"<>]/g, '_');
    }
    
    /**
     * Sanitize a folder name for URL path
     * @param {string} name - Folder name to sanitize
     * @returns {string} - Sanitized folder name
     */
    sanitizeFolderName(name) {
        if (!name) return '';
        
        // Convert to lowercase, replace spaces with hyphens, and remove invalid URL characters
        return name
            .toLowerCase()
            .replace(/\s+/g, '-')
            .replace(/[^a-z0-9-]/g, '')
            .replace(/-+/g, '-');
    }

    // Add new method for audio playback with retry
    async playWithRetry(maxRetries = 3) {
        // First cleanup any existing playback attempt
        if (this.currentPlayPromise) {
            try {
                await this.currentPlayPromise;
            } catch (e) {
                // Ignore any errors from previous play attempt
            }
        }
        
        // Reset the retry counter
        this.playRetryCount = 0;
        
        // Try to play
        const tryPlay = async () => {
            try {
                // Try to play and store the promise
                this.currentPlayPromise = this.audioElement.play();
                await this.currentPlayPromise;
                
                // Success! Update UI
                this.playPauseIcon.className = 'fas fa-pause';
                console.log('✅ Audio playback started successfully');
                return true;
            } catch (error) {
                this.playRetryCount++;
                
                // Log the error
                console.error(`❌ Playback attempt ${this.playRetryCount}/${maxRetries} failed:`, error);
                
                // If we still have retries left and it's a user activation error
                if (this.playRetryCount < maxRetries && 
                    (error.name === 'NotAllowedError' || error.message.includes('user activation'))) {
                    
                    console.log(`🔄 Retrying playback (${this.playRetryCount}/${maxRetries})`);
                    
                    // Try a different approach - sometimes changing the source helps
                    if (this.playRetryCount >= 2) {
                        // On later retries, try loading from cache if available
                        const audioUrl = this.audioElement.src;
                        
                        // First check if this is a blob URL
                        if (audioUrl.startsWith('blob:')) {
                            // If it's already a blob, try refreshing from cache
                            const cachedUrl = this.currentAudioUrl || this.getChapterUrl(this.currentPart, this.currentChapter);
                            
                            // Try using a different loading method based on browser
                            if (this.isFirefox) {
                                await this.loadAudioFromCacheForFirefox(cachedUrl);
                            } else {
                                await this.loadAudioFromCacheDirectly(cachedUrl);
                            }
                        }
                    }
                }
                
                // Wait a moment and try again
                await new Promise(resolve => setTimeout(resolve, 500));
                return await tryPlay();
            }
            
            // If we're out of retries or it's a different error, handle it
            this.handlePlaybackError(error);
            return false;
        };
        
        return await tryPlay();
    }

    // Add new method to handle playback errors
    handlePlaybackError(error) {
        console.error('Playback error:', error);
        
        // Update UI to show play button again
        this.playPauseIcon.className = 'fas fa-play';
        
        // Set flag for retry after page load if needed
        this.hadPlaybackError = true;
        
        // Show an appropriate message based on the error
        if (error.name === 'NotAllowedError') {
            // This is likely an autoplay policy issue
            this.showMessage('Tap play to start audio (autoplay blocked by browser)');
        } else if (navigator.onLine === false) {
            // We're offline
            this.checkCurrentChapterAvailability();
        } else {
            // Some other error
            this.showError('Playback error: ' + error.message);
        }
    }

    // Add this method before setupEventListeners
    registerControlListeners() {
        // Audio element events
        this.audioElement.addEventListener('timeupdate', () => this.updateProgress());
        this.audioElement.addEventListener('loadedmetadata', () => this.updateDuration());
        this.audioElement.addEventListener('ended', () => this.playNextChapter());
        this.audioElement.addEventListener('error', (e) => this.handleAudioError(e));
        
        // Control elements
        this.playPauseButton.addEventListener('click', () => this.togglePlayPause());
        
        // Progress bar with enhanced mobile touch support
        this.progressContainer.addEventListener('click', (e) => this.setProgress(e));
        this.progressContainer.addEventListener('mousemove', (e) => this.updateProgressTouchPoint(e));
        this.progressContainer.addEventListener('touchmove', (e) => {
            if (e.touches.length > 0) {
                const touch = e.touches[0];
                const rect = this.progressContainer.getBoundingClientRect();
                const offsetX = touch.clientX - rect.left;
                
                // Create a synthetic event with offsetX
                const syntheticEvent = { offsetX };
                this.updateProgressTouchPoint(syntheticEvent);
            }
        });
        this.progressContainer.addEventListener('touchend', (e) => {
            if (e.changedTouches.length > 0) {
                const touch = e.changedTouches[0];
                const rect = this.progressContainer.getBoundingClientRect();
                const offsetX = touch.clientX - rect.left;
                
                // Create a synthetic event with offsetX
                const syntheticEvent = { offsetX };
                this.setProgress(syntheticEvent);
            }
        });
        
        // Track navigation
        this.prevButton.addEventListener('click', () => this.playPreviousChapter());
        this.nextButton.addEventListener('click', () => this.playNextChapter());
        
        // Speed control
        this.speedButton.addEventListener('click', () => this.toggleSpeedOptions());
        this.speedOptions.querySelectorAll('.speed-option').forEach(option => {
            option.addEventListener('click', () => {
                const speed = parseFloat(option.dataset.speed);
                this.setPlaybackSpeed(speed);
                this.toggleSpeedOptions();
            });
        });
        
        // Download button event listeners
        if (this.downloadButton) {
            this.downloadButton.addEventListener('click', () => this.downloadCurrentChapter());
        }
        
        // Enhanced download buttons
        if (this.downloadCurrentChapterBtn) {
            this.downloadCurrentChapterBtn.addEventListener('click', () => this.queueCurrentChapter());
        }
        
        if (this.downloadRemainingChaptersBtn) {
            this.downloadRemainingChaptersBtn.addEventListener('click', () => this.queueRemainingChapters());
        }
        
        if (this.downloadEntireAudiobookBtn) {
            this.downloadEntireAudiobookBtn.addEventListener('click', () => this.queueEntireAudiobook());
        }
        
        // Offline management
        if (this.clearAllDownloads) {
            this.clearAllDownloads.addEventListener('click', () => this.clearAllDownloadedChapters());
        }
        
        // Close autoplay message event
        if (this.closeAutoplayMessage) {
            this.closeAutoplayMessage.addEventListener('click', () => this.hideAutoplayMessagePermanently());
        }
        
        // Close speed options when clicking outside
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.speed-control') && this.speedOptions && this.speedOptions.classList.contains('show')) {
                this.speedOptions.classList.remove('show');
            }
        });
        
        this.setupOfflineListeners();
    }

    // Add this function to update the progress bar touch position
    updateProgressTouchPoint(event) {
        if (window.innerWidth <= 768) {
            const container = this.progressContainer;
            const percentage = (event.offsetX / container.clientWidth) * 100;
            container.style.setProperty('--progress-position', `${percentage}%`);
        }
    }

    // Add this method to mark downloaded chapters in the chapter list
    async updateChapterDownloadIndicators() {
        if (!window.offlineAudioManager || !this.audioData) {
            return;
        }
        
        try {
            // Get all downloaded chapters
            const downloadedChapters = await window.offlineAudioManager.getDownloadedChapters(this.productId);
            console.log('Downloaded chapters:', downloadedChapters.length, downloadedChapters);
            
            // Create a lookup map for quick checking
            const downloadedMap = {};
            downloadedChapters.forEach(chapter => {
                const key = `${chapter.partNumber}_${chapter.chapterNumber}`;
                downloadedMap[key] = true;
            });
            
            let downloadedCount = 0;
            
            // Update all chapter items in the list
            document.querySelectorAll('.chapter-item').forEach(chapterItem => {
                const partNumber = parseInt(chapterItem.dataset.partNumber);
                const chapterNumber = parseInt(chapterItem.dataset.chapterNumber);
                const key = `${partNumber}_${chapterNumber}`;
                
                // Find or create the offline indicator
                let indicator = chapterItem.querySelector('.offline-indicator');
                
                if (downloadedMap[key]) {
                    downloadedCount++;
                    // Chapter is downloaded, show indicator
                    if (!indicator) {
                        indicator = document.createElement('div');
                        indicator.className = 'offline-indicator';
                        indicator.innerHTML = '<i class="fas fa-check-circle"></i>';
                        indicator.title = 'Available offline';
                        chapterItem.appendChild(indicator);
                    }
                } else {
                    // Chapter is not downloaded, remove indicator if it exists
                    if (indicator) {
                        indicator.remove();
                    }
                }
            });
            
            console.log(`Updated indicators: ${downloadedCount} chapters marked as downloaded`);
        } catch (error) {
            console.error('Error updating chapter download indicators:', error);
        }
    }

    /**
     * Fetch audiobook metadata from server
     * @returns {Promise} - Promise that resolves when metadata is fetched
     */
    async fetchMetadata() {
        try {
            const response = await fetch(this.metadataUrl);
            if (!response.ok) {
                throw new Error(`HTTP error: ${response.status}`);
            }
            this.audioData = await response.json();
            console.log('Audiobook metadata loaded:', this.audioData.title);
            return this.audioData;
        } catch (error) {
            console.error('Error fetching audiobook metadata:', error);
            throw error;
        }
    }

    /**
     * Check if an API endpoint exists
     * @param {string} url - The URL to check
     * @returns {Promise<boolean>} - Promise that resolves to true if the endpoint exists
     */
    async checkEndpointExists(url) {
        try {
            // Use HEAD request to check if endpoint exists without downloading content
            const response = await fetch(url, { method: 'HEAD' });
            return response.ok;
        } catch (error) {
            console.log(`Endpoint ${url} not available:`, error);
            return false;
        }
    }

    // Now let's implement the chapter navigation methods with Howler support
    playChapter(chapter, part, startPosition = 0) {
        // Stop any existing howl instance
        if (this.howl) {
            this.howl.stop();
            this.howl.unload();
            this.howl = null;
        }
        
        // Setup the chapter
        this.setupChapter(chapter, part, startPosition);
        
        // Force a small delay before playing to ensure setup is complete
        setTimeout(() => {
            // Actually play the audio
            this.playAudio();
            
            // Force update the UI
            this.isPlaying = true;
            this.playPauseIcon.className = 'fas fa-pause';
            
            console.log("Auto-playing chapter:", chapter.title);
        }, 100);
    }
    
    playNextChapter() {
        if (!this.currentChapter || !this.currentPart || !this.audioData) return;
        
        let foundCurrentChapter = false;
        let nextChapter = null;
        let nextPart = this.currentPart;
        
        // Force autoplay by setting wasPlaying to true
        const wasPlaying = true;
        
        // Look for the next chapter in the current part
        for (let i = 0; i < this.currentPart.chapters.length; i++) {
            if (foundCurrentChapter) {
                nextChapter = this.currentPart.chapters[i];
                break;
            }
            if (this.currentPart.chapters[i].number === this.currentChapter.number) {
                foundCurrentChapter = true;
            }
        }
        
        // If no next chapter in current part, look in the next part
        if (!nextChapter) {
            for (let i = 0; i < this.audioData.parts.length; i++) {
                if (this.audioData.parts[i].part_number === this.currentPart.part_number + 1) {
                    nextPart = this.audioData.parts[i];
                    if (nextPart.chapters.length > 0) {
                        nextChapter = nextPart.chapters[0];
                    }
                    break;
                }
            }
        }
        
        // Play the next chapter if found
        if (nextChapter) {
            console.log(`Auto-playing next chapter: ${nextChapter.title}`);
            // Always use playChapter to auto-play
            this.playChapter(nextChapter, nextPart);
        } else {
            console.log('End of audiobook reached');
            this.isPlaying = false;
            this.playPauseIcon.className = 'fas fa-play';
            this.showMessage('End of audiobook reached', 'info');
        }
    }
    
    playPreviousChapter() {
        if (!this.currentChapter || !this.currentPart || !this.audioData) return;
        
        // If current time is more than 3 seconds, just restart the current chapter
        const currentTime = this.howl ? this.howl.seek() : (this.audioElement ? this.audioElement.currentTime : 0);
        
        if (currentTime > 3) {
            if (this.howl) {
                this.howl.seek(0);
            } else if (this.audioElement) {
                this.audioElement.currentTime = 0;
            }
            return;
        }
        
        let previousChapter = null;
        let previousPart = this.currentPart;
        
        // Look for the previous chapter in the current part
        for (let i = 0; i < this.currentPart.chapters.length; i++) {
            if (this.currentPart.chapters[i].number === this.currentChapter.number && i > 0) {
                previousChapter = this.currentPart.chapters[i - 1];
                break;
            }
        }
        
        // If no previous chapter in current part, look in the previous part
        if (!previousChapter) {
            for (let i = 0; i < this.audioData.parts.length; i++) {
                if (this.audioData.parts[i].part_number === this.currentPart.part_number - 1) {
                    previousPart = this.audioData.parts[i];
                    if (previousPart.chapters.length > 0) {
                        previousChapter = previousPart.chapters[previousPart.chapters.length - 1];
                    }
                    break;
                }
            }
        }
        
        // Play the previous chapter if found
        if (previousChapter) {
            this.playChapter(previousChapter, previousPart);
        }
    }
    
    // Add this method at the end to ensure we have it
    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
    }
    
    // Make sure the toggle speed options method is present
    toggleSpeedOptions() {
        this.speedOptions.classList.toggle('show');
    }
    
    // Make sure the chapter info update method is present
    updateChapterInfo() {
        if (!this.currentChapter || !this.currentPart) return;
        
        // Update chapter and part titles
        this.currentChapterTitle.textContent = this.currentChapter.title;
        this.currentPartTitle.textContent = `Part ${this.currentPart.part_number}: ${this.currentPart.title}`;
        
        // Update cover image if available
        const baseUrl = this.metadataUrl.substring(0, this.metadataUrl.lastIndexOf('/'));
        
        if (this.currentChapter.cover) {
            // If chapter has its own cover
            this.currentChapterCover.src = `${baseUrl}/${this.currentChapter.cover}`;
            this.currentChapterCover.style.display = 'block';
        } else if (this.currentPart.cover) {
            // If part has a cover
            this.currentChapterCover.src = `${baseUrl}/${this.currentPart.cover}`;
            this.currentChapterCover.style.display = 'block';
        } else {
            // Use default cover if available
            const defaultCover = document.querySelector('#defaultCover');
            if (defaultCover) {
                this.currentChapterCover.src = defaultCover.src;
            } else {
                this.currentChapterCover.src = '/static/images/default-cover.jpg';
            }
            this.currentChapterCover.style.display = 'block';
        }
        
        // Add fade-in animation to cover
        this.currentChapterCover.classList.remove('fade-in');
        void this.currentChapterCover.offsetWidth; // Trigger reflow
        this.currentChapterCover.classList.add('fade-in');
    }

    // Remove the setupElements method since it likely already exists
    // Instead, add an additional method to handle Howler-specific media session setup
    setupMediaSessionForHowler() {
        // Skip if MediaSession API is not supported or audioData is null
        if (!('mediaSession' in navigator) || !window.MediaMetadata || !this.audioData) {
            console.log('MediaSession API not supported or audioData not loaded yet');
            return;
        }

        // Set metadata for the currently playing audio
        navigator.mediaSession.metadata = new MediaMetadata({
            title: this.audioData.title || 'Unknown Title',
            artist: this.audioData.author || 'Unknown Author',
            album: this.currentChapter ? this.currentChapter.title : 'Unknown Chapter',
            artwork: [
                { src: this.audioData.cover || '/static/images/default-cover.jpg', sizes: '512x512', type: 'image/jpeg' }
            ]
        });
        
        // Set action handlers for Howler
        navigator.mediaSession.setActionHandler('play', () => {
            if (this.howl) {
                this.howl.play();
            } else {
                this.playAudio();
            }
        });
        
        navigator.mediaSession.setActionHandler('pause', () => {
            if (this.howl) {
                this.howl.pause();
            } else {
                this.pauseAudio();
            }
        });
        
        navigator.mediaSession.setActionHandler('previoustrack', () => this.playPreviousChapter());
        navigator.mediaSession.setActionHandler('nexttrack', () => this.playNextChapter());
        
        // Set seek to handlers if supported
        try {
            navigator.mediaSession.setActionHandler('seekto', (details) => {
                if (this.howl) {
                    this.howl.seek(details.seekTime);
                } else if (this.audioElement) {
                    if (details.fastSeek && 'fastSeek' in this.audioElement) {
                        this.audioElement.fastSeek(details.seekTime);
                        return;
                    }
                    this.audioElement.currentTime = details.seekTime;
                }
            });
        } catch (error) {
            console.log('Seekto action not supported');
        }
    }

    /**
     * Set up media session API for better integration with system controls
     */
    setupMediaSession() {
        if ('mediaSession' in navigator && window.MediaMetadata) {
            navigator.mediaSession.metadata = new MediaMetadata({
                title: this.audioData.title || 'Audiobook',
                artist: this.audioData.author || 'Unknown Author',
                album: this.audioData.title || 'Audiobook',
                artwork: [
                    { src: this.audioData.cover || '', sizes: '512x512', type: 'image/jpeg' }
                ]
            });
            
            // Set action handlers
            navigator.mediaSession.setActionHandler('play', () => this.playAudio());
            navigator.mediaSession.setActionHandler('pause', () => this.pauseAudio());
            navigator.mediaSession.setActionHandler('previoustrack', () => this.playPreviousChapter());
            navigator.mediaSession.setActionHandler('nexttrack', () => this.playNextChapter());
            
            // Set seek to handlers if supported
            try {
                navigator.mediaSession.setActionHandler('seekto', (details) => {
                    if (details.fastSeek && 'fastSeek' in this.audioElement) {
                        this.audioElement.fastSeek(details.seekTime);
                        return;
                    }
                    this.audioElement.currentTime = details.seekTime;
                });
            } catch (error) {
                console.log('Seekto action not supported');
            }
        }
    }

    // Load audio from cache directly for browsers other than Firefox
    loadAudioFromCacheDirectly(audioUrl, startPosition = 0) {
        console.log("Attempting to load audio directly from cache:", audioUrl);
        
        // First try to find exact match
        fetch(audioUrl, { cache: 'only-if-cached', mode: 'same-origin' })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.blob();
            })
            .then(blob => {
                // Set appropriate content type
                const contentType = this.determineContentType(audioUrl, blob);
                const typedBlob = new Blob([blob], { type: contentType });
                
                // Create blob URL and play
                const blobUrl = URL.createObjectURL(typedBlob);
                console.log("Created blob URL for direct cache:", blobUrl);
                
                // Try with Howler first
                if (typeof Howl !== 'undefined') {
                    try {
                        if (this.howl) {
                            this.howl.unload();
                        }
                        
                        this.howl = new Howl({
                            src: [blobUrl],
                            html5: true,
                            format: ['mp3'],
                            preload: true,
                            autoplay: false,
                            rate: this.playbackSpeed,
                            onload: () => {
                                console.log("Direct cache: Audio loaded successfully with Howler");
                                if (startPosition > 0) {
                                    this.howl.seek(startPosition);
                                }
                                this.updateDuration();
                                this.showMessage("Playing from cache", "success");
                            },
                            onloaderror: () => {
                                console.error("Direct cache: Error loading with Howler");
                                URL.revokeObjectURL(blobUrl);
                                this.fallbackToDirectAudioElement(URL.createObjectURL(blob), startPosition);
                            }
                        });
                    } catch (error) {
                        console.error("Error creating Howl for direct cache:", error);
                        URL.revokeObjectURL(blobUrl);
                        this.fallbackToDirectAudioElement(URL.createObjectURL(blob), startPosition);
                    }
                } else {
                    this.fallbackToDirectAudioElement(blobUrl, startPosition);
                }
            })
            .catch(error => {
                console.error("Error loading from cache directly:", error);
                
                // Try CacheStorage API instead
                if ('caches' in window) {
                    console.log("Trying CacheStorage API");
                    this.loadFromCache(audioUrl, startPosition);
                } else {
                    console.error("Cache API not supported");
                    this.handleAudioError();
                    this.showError('Unable to play offline. Please reconnect to the internet.');
                }
            });
    }

    // Update duration display
    updateDuration() {
        let duration = 0;
        
        if (this.howl) {
            duration = this.howl.duration() || 0;
        } else if (this.audioElement) {
            duration = this.audioElement.duration || 0;
        }
        
        if (isFinite(duration) && duration > 0) {
            this.durationDisplay.textContent = this.formatTime(duration);
        }
    }

    // Add 10-second skip backward function
    skipBackward(seconds = 10) {
        console.log(`Skipping backward ${seconds} seconds`);
        let currentTime = 0;
        
        if (this.howl) {
            currentTime = Math.max(0, (this.howl.seek() || 0) - seconds);
            this.howl.seek(currentTime);
        } else if (this.audioElement) {
            currentTime = Math.max(0, this.audioElement.currentTime - seconds);
            this.audioElement.currentTime = currentTime;
        }
        
        // Update progress immediately
        this.updateProgress();
        
        // Show a brief message
        this.showMessage(`-${seconds}s`, 'info');
    }
    
    // Skip forward with better error handling
    skipForward(seconds = 10) {
        console.log(`Skipping forward ${seconds} seconds`);
        let currentTime = 0;
        let duration = 0;
        
        try {
            if (this.howl) {
                currentTime = this.howl.seek() || 0;
                duration = this.howl.duration() || 0;
                
                // Don't skip past the end
                if (duration > 0) {
                    const newPosition = Math.min(duration - 0.5, currentTime + seconds);
                    console.log(`Seeking to ${newPosition.toFixed(2)} of ${duration.toFixed(2)}`);
                    
                    // If we're near the end, just go to next chapter
                    if (duration - newPosition < 0.5) {
                        console.log("Near end of file, playing next chapter");
                        this.playNextChapter();
                        return;
                    }
                    
                    this.howl.seek(newPosition);
                } else {
                    console.log("Invalid duration, can't skip forward");
                }
            } else if (this.audioElement) {
                currentTime = this.audioElement.currentTime;
                duration = this.audioElement.duration || 0;
                
                // Don't skip past the end
                if (duration > 0) {
                    const newPosition = Math.min(duration - 0.5, currentTime + seconds);
                    console.log(`Seeking to ${newPosition.toFixed(2)} of ${duration.toFixed(2)}`);
                    
                    // If we're near the end, just go to next chapter
                    if (duration - newPosition < 0.5) {
                        console.log("Near end of file, playing next chapter");
                        this.playNextChapter();
                        return;
                    }
                    
                    this.audioElement.currentTime = newPosition;
                } else {
                    console.log("Invalid duration, can't skip forward");
                }
            }
            
            // Update progress immediately
            this.updateProgress();
            
            // Show a brief message
            this.showMessage(`+${seconds}s`, 'info');
        } catch (error) {
            console.error("Error while skipping forward:", error);
            // Try to recover - just go to next chapter
            this.playNextChapter();
        }
    }
    
    // Enhanced error handling for Howler
    handleHowlerError(errorCode, message) {
        console.error(`Howler error ${errorCode}: ${message}`);
        
        switch (errorCode) {
            case 1: // Decoding error
                this.showMessage("Audio format error", "error");
                break;
            case 2: // Network error
                this.showMessage("Network error", "error");
                if (this.isOffline) {
                    this.showRefreshSuggestion();
                }
                break;
            case 3: // File not found
                this.showMessage("Audio file not found", "error");
                break;
            case 4: // Format not supported
                this.showMessage("Audio format not supported", "error");
                break;
            default:
                this.showMessage("Audio playback error", "error");
        }
        
        // Update UI to reflect errored state
        this.isPlaying = false;
        this.playPauseIcon.className = 'fas fa-play';
    }
    
    // Method to add skip buttons to the player UI if they don't exist
    addSkipButtons() {
        console.log("Adding skip buttons to player UI");
        
        // First check if we already added the buttons
        if (document.getElementById('skipBackwardButton') || document.getElementById('skipForwardButton')) {
            console.log("Skip buttons already exist");
            return;
        }
        
        // First try to find by selector
        let controlsContainer = document.querySelector('.audio-controls');
        
        // If not found, try a different approach
        if (!controlsContainer) {
            // Look for play/pause button's parent
            const playPauseButton = document.getElementById('playPauseButton');
            if (playPauseButton) {
                controlsContainer = playPauseButton.parentElement;
                console.log("Found controls container via play button's parent");
            }
        }
        
        // If still not found, look by position
        if (!controlsContainer) {
            const playerContainer = document.getElementById('audioPlayerContainer');
            if (playerContainer) {
                // Try to find any container that might contain controls
                const possibleContainers = playerContainer.querySelectorAll('div');
                for (const container of possibleContainers) {
                    if (container.querySelector('button')) {
                        controlsContainer = container;
                        console.log("Found controls container by finding button container");
                        break;
                    }
                }
            }
        }
        
        // If we still can't find it, log error and exit
        if (!controlsContainer) {
            console.error("Cannot find audio controls container - skip buttons not added");
            return;
        }
        
        // Get reference to play/pause button
        const playPauseButton = document.getElementById('playPauseButton');
        if (!playPauseButton) {
            console.error("Cannot find play/pause button");
            return;
        }
        
        // Create skip backward button with better icon display
        const skipBackwardButton = document.createElement('button');
        skipBackwardButton.id = 'skipBackwardButton';
        skipBackwardButton.className = 'control-button skip-button';
        skipBackwardButton.setAttribute('aria-label', 'Skip backward 10 seconds');
        skipBackwardButton.setAttribute('data-action', 'skip-backward');
        skipBackwardButton.innerHTML = '<i class="fas fa-undo"></i><span>10s</span>';
        
        // Create skip forward button with better icon display
        const skipForwardButton = document.createElement('button');
        skipForwardButton.id = 'skipForwardButton';
        skipForwardButton.className = 'control-button skip-button';
        skipForwardButton.setAttribute('aria-label', 'Skip forward 10 seconds');
        skipForwardButton.setAttribute('data-action', 'skip-forward');
        skipForwardButton.innerHTML = '<i class="fas fa-redo"></i><span>10s</span>';
        
        // Insert in DOM - inject near play/pause button
        playPauseButton.insertAdjacentElement('beforebegin', skipBackwardButton);
        playPauseButton.insertAdjacentElement('afterend', skipForwardButton);
        
        console.log("Skip buttons added to player: ", {
            backward: skipBackwardButton,
            forward: skipForwardButton
        });
        
        // Add CSS for the skip buttons
        const styleElement = document.createElement('style');
        styleElement.id = 'skipButtonStyles';
        styleElement.textContent = `
            .skip-button {
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                gap: 2px;
                padding: 8px 12px;
                margin: 0 5px;
                border-radius: 4px;
                background-color: rgba(255, 255, 255, 0.1);
                color: var(--text-color, white);
                border: 1px solid rgba(255, 255, 255, 0.2);
                cursor: pointer;
                transition: all 0.2s ease;
            }
            
            .skip-button:hover {
                background-color: var(--primary-color, #28a745);
                color: white;
            }
            
            .skip-button i {
                font-size: 14px;
            }
            
            .skip-button span {
                font-size: 11px;
                font-weight: bold;
                margin-left: 2px;
            }
            
            @media (max-width: 768px) {
                .skip-button {
                    padding: 6px 8px;
                    margin: 0 3px;
                    min-width: 36px;
                    height: 36px;
                }
                
                .skip-button i {
                    font-size: 12px;
                }
                
                .skip-button span {
                    font-size: 9px;
                }
                
                /* Fix for mobile layout */
                .main-controls {
                    display: flex;
                    justify-content: space-around;
                    width: 100%;
                    gap: 5px;
                }
            }
            
            /* Ensure buttons don't overlap on very small screens */
            @media (max-width: 375px) {
                .skip-button {
                    padding: 4px 6px;
                    margin: 0 2px;
                }
                
                .skip-button span {
                    display: none;
                }
            }
        `;
        document.head.appendChild(styleElement);
        
        // Add event listeners to the buttons
        skipBackwardButton.addEventListener('click', () => {
            console.log("Skip backward button clicked");
            this.skipBackward(10);
        });
        
        skipForwardButton.addEventListener('click', () => {
            console.log("Skip forward button clicked");
            this.skipForward(10);
        });
        
        console.log('Skip buttons successfully added to player UI');
    }

    // Add keyboard controls for audio playback
    setupKeyboardControls() {
        document.addEventListener('keydown', (e) => {
            // Only handle keyboard shortcuts if the audio player is in focus
            // or when no input element is focused
            const activeElement = document.activeElement;
            const isInputFocused = activeElement && (
                activeElement.tagName === 'INPUT' ||
                activeElement.tagName === 'TEXTAREA' ||
                activeElement.isContentEditable
            );
            
            if (isInputFocused) return;
            
            // Handle keyboard shortcuts
            switch (e.key) {
                case ' ': // Space bar
                    e.preventDefault(); // Prevent page scroll
                    this.togglePlayPause();
                    break;
                    
                case 'ArrowLeft': // Left arrow for skip backward
                    e.preventDefault();
                    if (e.shiftKey) {
                        // Shift+Left = previous chapter
                        this.playPreviousChapter();
                    } else {
                        // Left arrow = skip back 10 seconds
                        this.skipBackward(10);
                    }
                    break;
                    
                case 'ArrowRight': // Right arrow for skip forward
                    e.preventDefault();
                    if (e.shiftKey) {
                        // Shift+Right = next chapter
                        this.playNextChapter();
                    } else {
                        // Right arrow = skip forward 10 seconds
                        this.skipForward(10);
                    }
                    break;
                    
                case 'ArrowUp': // Up arrow for volume up (if supported)
                    e.preventDefault();
                    if (this.howl) {
                        const volume = Math.min(1, (this.howl.volume() || 1) + 0.1);
                        this.howl.volume(volume);
                        this.showMessage(`Volume: ${Math.round(volume * 100)}%`, 'info');
                    } else if (this.audioElement) {
                        this.audioElement.volume = Math.min(1, this.audioElement.volume + 0.1);
                        this.showMessage(`Volume: ${Math.round(this.audioElement.volume * 100)}%`, 'info');
                    }
                    break;
                    
                case 'ArrowDown': // Down arrow for volume down
                    e.preventDefault();
                    if (this.howl) {
                        const volume = Math.max(0, (this.howl.volume() || 1) - 0.1);
                        this.howl.volume(volume);
                        this.showMessage(`Volume: ${Math.round(volume * 100)}%`, 'info');
                    } else if (this.audioElement) {
                        this.audioElement.volume = Math.max(0, this.audioElement.volume - 0.1);
                        this.showMessage(`Volume: ${Math.round(this.audioElement.volume * 100)}%`, 'info');
                    }
                    break;
                    
                case '1': case '2': case '3': case '4': case '5': 
                    // Number keys 1-5 for playback speed
                    const speedMap = {
                        '1': 0.75,
                        '2': 1.0,
                        '3': 1.25,
                        '4': 1.5,
                        '5': 2.0
                    };
                    if (speedMap[e.key]) {
                        this.setPlaybackSpeed(speedMap[e.key]);
                    }
                    break;
            }
        });
    }

    // Enhanced setProgress for better user feedback
    setProgress(event) {
        const width = this.progressContainer.clientWidth;
        const clickX = event.offsetX;
        let duration = 0;
        
        if (this.howl) {
            duration = this.howl.duration() || 0;
        } else if (this.audioElement) {
            duration = this.audioElement.duration || 0;
        }
        
        if (isNaN(duration) || !isFinite(duration)) {
            return;
        }
        
        // Update the visual touch point position
        this.updateProgressTouchPoint(event);
        
        // Calculate the seek position
        const seekPosition = (clickX / width) * duration;
        
        // Perform the seek operation
        if (this.howl) {
            this.howl.seek(seekPosition);
        } else if (this.audioElement) {
            this.audioElement.currentTime = seekPosition;
        }
        
        // Update the UI
        this.updateProgress();
        
        // Notify Media Session Manager
        if (this.mediaSessionManager) {
            this.mediaSessionManager.updatePositionState();
        }
    }
    
    // Update the progress bar touch point and show preview
    updateProgressTouchPoint(event) {
        const container = this.progressContainer;
        const width = container.clientWidth;
        const offsetX = event.offsetX;
        const percentage = (offsetX / width) * 100;
        
        // Update the visual indicator
        container.style.setProperty('--progress-position', `${percentage}%`);
        
        // Calculate and show the time preview
        let duration = 0;
        if (this.howl) {
            duration = this.howl.duration() || 0;
        } else if (this.audioElement) {
            duration = this.audioElement.duration || 0;
        }
        
        if (duration > 0) {
            const previewTime = (offsetX / width) * duration;
            this.showProgressPreview(previewTime, percentage);
        }
    }
    
    // Show a preview of the time position when hovering/dragging
    showProgressPreview(time, percentage) {
        // Create or get the preview element
        let preview = document.getElementById('timePreview');
        if (!preview) {
            preview = document.createElement('div');
            preview.id = 'timePreview';
            preview.className = 'time-preview';
            this.progressContainer.appendChild(preview);
            
            // Add styles for the preview
            const style = document.createElement('style');
            style.textContent = `
                .time-preview {
                    position: absolute;
                    bottom: 100%;
                    background-color: rgba(0, 0, 0, 0.7);
                    color: white;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-size: 12px;
                    transform: translateX(-50%);
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.2s;
                    z-index: 10;
                }
                
                .progress-container:hover .time-preview {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Update the preview content and position
        preview.textContent = this.formatTime(time);
        preview.style.left = `${percentage}%`;
        preview.style.opacity = '1';
        
        // Hide the preview after a short delay
        clearTimeout(this.previewTimeout);
        this.previewTimeout = setTimeout(() => {
            preview.style.opacity = '0';
        }, 2000);
    }
    
    // Enhance the progress container with event listeners
    enhanceProgressInteraction() {
        if (!this.progressContainer) return;
        
        // Already enhanced
        if (this.progressContainer.dataset.enhanced === 'true') return;
        
        console.log("Enhancing progress container interaction");
        
        // Add mousemove event to show preview
        this.progressContainer.addEventListener('mousemove', (e) => {
            this.updateProgressTouchPoint(e);
        });
        
        // Add mouseenter to make sure preview shows
        this.progressContainer.addEventListener('mouseenter', (e) => {
            const preview = document.getElementById('timePreview');
            if (preview) preview.style.opacity = '1';
        });
        
        // Add mouseleave to hide preview
        this.progressContainer.addEventListener('mouseleave', () => {
            const preview = document.getElementById('timePreview');
            if (preview) preview.style.opacity = '0';
        });
        
        // Mark as enhanced to prevent duplicate event listeners
        this.progressContainer.dataset.enhanced = 'true';
    }

    // Add a custom implementation of getChapterAudio
    addGetChapterAudioMethod() {
        // Check if we have an offline manager but not the getChapterAudio method
        if (window.offlineAudioManager && 
            !window.offlineAudioManager.getChapterAudio) {
            
            console.log("Adding custom getChapterAudio method to offlineAudioManager");
            
            // Add our own implementation of getChapterAudio
            window.offlineAudioManager.getChapterAudio = async (audiobookId, partNumber, chapterNumber) => {
                console.log(`Custom getChapterAudio: getting chapter ${chapterNumber} from part ${partNumber}`);
                
                try {
                    // Check if the chapter is available first
                    const isAvailable = await window.offlineAudioManager.isChapterAvailableOffline(
                        audiobookId, partNumber, chapterNumber
                    );
                    
                    if (!isAvailable) {
                        console.log("Chapter not available in offline storage");
                        return null;
                    }
                    
                    // Get the cached data from IndexedDB
                    // First try with a specific key format that matches your DB schema
                    const db = await window.indexedDB.open("offline-audio-db", 1);
                    
                    return new Promise((resolve, reject) => {
                        db.onsuccess = (event) => {
                            const database = event.target.result;
                            const transaction = database.transaction("audio-files", "readonly");
                            const store = transaction.objectStore("audio-files");
                            
                            // Try different key formats that might be used
                            const keyFormats = [
                                `${audiobookId}_${partNumber}_${chapterNumber}`,
                                `${audiobookId}-${partNumber}-${chapterNumber}`,
                                JSON.stringify({audiobookId, partNumber, chapterNumber})
                            ];
                            
                            let found = false;
                            
                            // Try each key format
                            const tryNextKey = (index) => {
                                if (index >= keyFormats.length) {
                                    // If we've tried all formats and not found anything,
                                    // try getting all records and searching
                                    const getAllRequest = store.getAll();
                                    
                                    getAllRequest.onsuccess = () => {
                                        const allRecords = getAllRequest.result;
                                        console.log(`Found ${allRecords.length} records in IndexedDB`);
                                        
                                        // Find a matching record
                                        const matchingRecord = allRecords.find(record => {
                                            return (record.audiobookId == audiobookId || 
                                                   record.audiobook_id == audiobookId) && 
                                                   (record.partNumber == partNumber || 
                                                   record.part_number == partNumber) && 
                                                   (record.chapterNumber == chapterNumber || 
                                                   record.chapter_number == chapterNumber);
                                        });
                                        
                                        if (matchingRecord) {
                                            console.log("Found matching record via full scan");
                                            resolve({blob: matchingRecord.blob || matchingRecord.data});
                                        } else {
                                            console.log("No matching record found via any method");
                                            resolve(null);
                                        }
                                    };
                                    
                                    getAllRequest.onerror = (err) => {
                                        console.error("Error getting all records:", err);
                                        reject(err);
                                    };
                                    
                                    return;
                                }
                                
                                const key = keyFormats[index];
                                console.log(`Trying key format: ${key}`);
                                
                                const request = store.get(key);
                                
                                request.onsuccess = (event) => {
                                    const result = event.target.result;
                                    if (result) {
                                        console.log(`Found record with key: ${key}`);
                                        found = true;
                                        resolve({blob: result.blob || result.data});
                                    } else {
                                        tryNextKey(index + 1);
                                    }
                                };
                                
                                request.onerror = (event) => {
                                    console.log(`Error with key ${key}:`, event.target.error);
                                    tryNextKey(index + 1);
                                };
                            };
                            
                            // Start with the first key format
                            tryNextKey(0);
                            
                            transaction.oncomplete = () => {
                                database.close();
                                if (!found) {
                                    resolve(null);
                                }
                            };
                            
                            transaction.onerror = (err) => {
                                database.close();
                                reject(err);
                            };
                        };
                        
                        db.onerror = (event) => {
                            console.error("Error opening IndexedDB:", event.target.error);
                            reject(event.target.error);
                        };
                    });
                    
                } catch (error) {
                    console.error("Error in custom getChapterAudio:", error);
                    return null;
                }
            };
        }
    }

    // Initialize the Media Session Manager
    initMediaSessionManager() {
        if (window.MediaSessionManager) {
            this.mediaSessionManager = new MediaSessionManager(this);
        } else {
            console.log('MediaSessionManager not available, falling back to native API');
            if ('mediaSession' in navigator) {
                this.setupMediaSession();
            }
        }
        
        // Add event listener for clicking on "Manage Downloads" to update storage usage
        const manageDownloadsButton = document.querySelector('[data-bs-target="#offlineManagementCollapse"]');
        if (manageDownloadsButton) {
            manageDownloadsButton.addEventListener('click', () => {
                console.log('Manage Downloads clicked, updating storage usage');
                this.updateStorageUsage();
            });
        }
        
        // Add event listener for showing the dropdown panel to update storage
        document.addEventListener('shown.bs.collapse', (event) => {
            if (event.target.id === 'offlineManagementCollapse' && this.updateStorageUsage) {
                console.log('Download management panel opened, updating storage usage');
                this.updateStorageUsage();
            }
        });
    }

    /**
     * Get the total duration of the current audio
     */
    getDuration() {
        // If using Howler
        if (this.howl) {
            return this.howl.duration();
        }
        
        // If using native audio element
        if (this.audioElement) {
            return this.audioElement.duration;
        }
        
        // If we have chapter data, sum up all chapter durations
        if (this.audioData && this.audioData.chapters && this.audioData.chapters.length > 0) {
            return this.audioData.chapters.reduce((total, chapter) => {
                return total + (chapter.duration || 0);
            }, 0);
        }
        
        return 0;
    }

    // Show the autoplay message without attempting actual autoplay
    showAutoplayMessage() {
        // Only show message if we have a chapter loaded and it's not disabled in preferences
        if (this.currentChapter && this.currentPart && this.autoplayMessage) {
            const hideAutoplayMessage = localStorage.getItem(this.storageKeyHideAutoplayMessage);
            if (hideAutoplayMessage !== 'true') {
                this.autoplayMessage.style.display = 'block';
            }
        }
    }
}

// Initialize the player when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  // Check if we're on the audio player page
  const audioPlayerContainer = document.getElementById('audioPlayerContainer');
  if (!audioPlayerContainer) {
    console.log('Not on audio player page, player initialization skipped');
    return; // Exit early if not on audio player page
  }
  
  // Get the metadata URL and product ID from the container
  const metadataUrl = audioPlayerContainer.dataset.metadataUrl;
  const productId = audioPlayerContainer.dataset.productId;
  
  // Initialize the player
  window.audiobookPlayer = new AudiobookPlayer(metadataUrl, productId);
  
  // Add skip buttons if needed
  if (window.audiobookPlayer) {
    window.audiobookPlayer.addSkipButtons();
  }
}); 

// Make autoplay message dismissible by clicking anywhere on it
const autoplayMessage = document.getElementById('autoplayMessage');
if (autoplayMessage) {
    autoplayMessage.style.cursor = 'pointer';
    autoplayMessage.addEventListener('click', function() {
        this.style.display = 'none';
    });
}

// Add event listener for clicking on "Manage Downloads" to update storage usage
const manageDownloadsButton = document.querySelector('[data-bs-target="#offlineManagementCollapse"]');
if (manageDownloadsButton) {
    manageDownloadsButton.addEventListener('click', () => {
        console.log('Manage Downloads clicked, updating storage usage');
        if (window.audiobookPlayer && typeof window.audiobookPlayer.updateStorageUsage === 'function') {
            window.audiobookPlayer.updateStorageUsage();
        }
    });
}

// Add event listener for showing the dropdown panel to update storage
document.addEventListener('shown.bs.collapse', (event) => {
    if (event.target.id === 'offlineManagementCollapse') {
        console.log('Download management panel opened, updating storage usage');
        if (window.audiobookPlayer && typeof window.audiobookPlayer.updateStorageUsage === 'function') {
            window.audiobookPlayer.updateStorageUsage();
        }
    }
});