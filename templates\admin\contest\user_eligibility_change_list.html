{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrastyle %}
  {{ block.super }}
  <link rel="stylesheet" type="text/css" href="{% static "admin/css/changelists.css" %}">
  <style>
    .eligibility-stats {
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;
      padding: 10px;
      background-color: #f8f8f8;
      border-radius: 5px;
    }
    .stat-box {
      text-align: center;
      padding: 10px;
      border-radius: 5px;
      background-color: white;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      min-width: 150px;
    }
    .stat-number {
      font-size: 24px;
      font-weight: bold;
      color: #417690;
    }
    .stat-label {
      font-size: 14px;
      color: #666;
    }
    .user-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    .user-table th {
      background-color: #417690;
      color: white;
      padding: 10px;
      text-align: left;
    }
    .user-table td {
      padding: 8px;
      border-bottom: 1px solid #ddd;
    }
    .user-table tr:nth-child(even) {
      background-color: #f2f2f2;
    }
    .eligible {
      color: green;
      font-weight: bold;
    }
    .not-eligible {
      color: red;
    }
    .section-title {
      margin-top: 30px;
      padding: 10px;
      background-color: #417690;
      color: white;
      border-radius: 5px;
    }
  </style>
{% endblock %}

{% block content %}
<div id="content-main">
  <div class="module" id="changelist">
    <div class="eligibility-stats">
      <div class="stat-box">
        <div class="stat-number">{{ total_users }}</div>
        <div class="stat-label">Total Users</div>
      </div>
      <div class="stat-box">
        <div class="stat-number">{{ total_eligible }}</div>
        <div class="stat-label">Eligible Users</div>
      </div>
      <div class="stat-box">
        <div class="stat-number">{{ total_ineligible }}</div>
        <div class="stat-label">Ineligible Users</div>
      </div>
    </div>

    <h2 class="section-title">Eligible Users ({{ total_eligible }})</h2>
    <table class="user-table">
      <thead>
        <tr>
          <th>Username</th>
          <th>Email</th>
          <th>Likes Given</th>
          <th>Submissions</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {% for user in eligible_users %}
        <tr>
          <td>{{ user.username }}</td>
          <td>{{ user.email }}</td>
          <td>{{ user.like_count }}</td>
          <td>{{ user.submission_count }}</td>
          <td class="eligible">Eligible</td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="5">No eligible users found.</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>

    <h2 class="section-title">Ineligible Users ({{ total_ineligible }})</h2>
    <table class="user-table">
      <thead>
        <tr>
          <th>Username</th>
          <th>Email</th>
          <th>Likes Given</th>
          <th>Submissions</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
        {% for user in ineligible_users %}
        <tr>
          <td>{{ user.username }}</td>
          <td>{{ user.email }}</td>
          <td>{{ user.like_count }}</td>
          <td>{{ user.submission_count }}</td>
          <td class="not-eligible">Needs {{ 3|add:-user.like_count }} more likes</td>
        </tr>
        {% empty %}
        <tr>
          <td colspan="5">No ineligible users found.</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>
{% endblock %}
