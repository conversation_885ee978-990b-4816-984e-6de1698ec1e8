/**
 * Media Session Manager
 * 
 * A dedicated module to handle Media Session API integration with Howler.js
 * This provides better control over media notifications and system media controls
 */
class MediaSessionManager {
    /**
     * Initialize the Media Session Manager
     * @param {Object} player - Reference to the audio player instance
     */
    constructor(player) {
        this.player = player;
        this.isSupported = 'mediaSession' in navigator && window.MediaMetadata;
        this.lastPositionUpdate = 0;
        this.positionUpdateInterval = null;
        
        // Initialize if supported
        if (this.isSupported) {
            this.setupActionHandlers();
            this.startPositionUpdates();
        } else {
            console.log('Media Session API not supported in this browser');
        }
    }
    
    /**
     * Set up action handlers for media controls
     */
    setupActionHandlers() {
        if (!this.isSupported) return;
        
        try {
            // Play action
            navigator.mediaSession.setActionHandler('play', () => {
                console.log('Media Session: Play action');
                if (this.player.howl) {
                    this.player.howl.play();
                } else if (this.player.audioElement) {
                    this.player.audioElement.play();
                }
            });
        } catch (error) {
            console.log('Media Session: Play action not supported', error);
        }
        
        try {
            // Pause action
            navigator.mediaSession.setActionHandler('pause', () => {
                console.log('Media Session: Pause action');
                if (this.player.howl) {
                    this.player.howl.pause();
                } else if (this.player.audioElement) {
                    this.player.audioElement.pause();
                }
            });
        } catch (error) {
            console.log('Media Session: Pause action not supported', error);
        }
        
        try {
            // Previous track action
            navigator.mediaSession.setActionHandler('previoustrack', () => {
                console.log('Media Session: Previous track action');
                this.player.playPreviousChapter();
            });
        } catch (error) {
            console.log('Media Session: Previous track action not supported', error);
        }
        
        try {
            // Next track action
            navigator.mediaSession.setActionHandler('nexttrack', () => {
                console.log('Media Session: Next track action');
                this.player.playNextChapter();
            });
        } catch (error) {
            console.log('Media Session: Next track action not supported', error);
        }
        
        try {
            // Seek backward action
            navigator.mediaSession.setActionHandler('seekbackward', (details) => {
                console.log('Media Session: Seek backward action');
                const skipTime = details.seekOffset || 10;
                this.player.skipBackward(skipTime);
            });
        } catch (error) {
            console.log('Media Session: Seek backward action not supported', error);
        }
        
        try {
            // Seek forward action
            navigator.mediaSession.setActionHandler('seekforward', (details) => {
                console.log('Media Session: Seek forward action');
                const skipTime = details.seekOffset || 10;
                this.player.skipForward(skipTime);
            });
        } catch (error) {
            console.log('Media Session: Seek forward action not supported', error);
        }
        
        try {
            // Seek to action
            navigator.mediaSession.setActionHandler('seekto', (details) => {
                console.log('Media Session: Seek to action', details.seekTime);
                if (this.player.howl) {
                    this.player.howl.seek(details.seekTime);
                } else if (this.player.audioElement) {
                    if (details.fastSeek && 'fastSeek' in this.player.audioElement) {
                        this.player.audioElement.fastSeek(details.seekTime);
                    } else {
                        this.player.audioElement.currentTime = details.seekTime;
                    }
                }
                
                // Force an immediate position update
                this.updatePositionState();
            });
        } catch (error) {
            console.log('Media Session: Seek to action not supported', error);
        }
    }
    
    /**
     * Update media metadata with current chapter information
     */
    updateMetadata() {
        if (!this.isSupported) return;
        
        try {
            const chapterTitle = this.player.currentChapter ? 
                `Chapter ${this.player.currentChapter.number}: ${this.player.currentChapter.title}` : 
                'Unknown Chapter';
            
            const partTitle = this.player.currentPart ? 
                `Part ${this.player.currentPart.part_number}: ${this.player.currentPart.title}` : 
                'Unknown Part';
            
            const audiobookTitle = this.player.audioData ? 
                this.player.audioData.title : 
                'Audiobook';
            
            const author = this.player.audioData ? 
                this.player.audioData.author : 
                'Unknown Author';
            
            const coverUrl = this.player.audioData && this.player.audioData.cover ? 
                this.player.audioData.cover : 
                '';
            
            // Create artwork array with multiple sizes if available
            const artwork = [];
            if (coverUrl) {
                // Add multiple sizes for better device compatibility
                artwork.push({ src: coverUrl, sizes: '96x96', type: 'image/jpeg' });
                artwork.push({ src: coverUrl, sizes: '128x128', type: 'image/jpeg' });
                artwork.push({ src: coverUrl, sizes: '192x192', type: 'image/jpeg' });
                artwork.push({ src: coverUrl, sizes: '256x256', type: 'image/jpeg' });
                artwork.push({ src: coverUrl, sizes: '384x384', type: 'image/jpeg' });
                artwork.push({ src: coverUrl, sizes: '512x512', type: 'image/jpeg' });
            }
            
            // Set the metadata
            navigator.mediaSession.metadata = new MediaMetadata({
                title: chapterTitle,
                artist: author,
                album: `${audiobookTitle} - ${partTitle}`,
                artwork: artwork
            });
            
            // Update position state after metadata change
            this.updatePositionState();
        } catch (error) {
            console.error('Error updating media session metadata:', error);
        }
    }
    
    /**
     * Update position state with current playback information
     */
    updatePositionState() {
        if (!this.isSupported) return;
        
        try {
            let currentTime = 0;
            let duration = 0;
            
            // Get current time and duration
            if (this.player.howl) {
                currentTime = this.player.howl.seek() || 0;
                duration = this.player.howl.duration() || 0;
            } else if (this.player.audioElement) {
                currentTime = this.player.audioElement.currentTime;
                duration = this.player.audioElement.duration;
            }
            
            // Only update if we have valid values
            if (isFinite(duration) && duration > 0 && isFinite(currentTime)) {
                // Ensure currentTime doesn't exceed duration
                currentTime = Math.min(currentTime, duration);
                
                // Use integer values to avoid floating point issues
                const position = Math.floor(currentTime);
                const totalDuration = Math.floor(duration);
                
                // Set the position state
                navigator.mediaSession.setPositionState({
                    duration: totalDuration,
                    position: position,
                    playbackRate: this.player.playbackSpeed || 1.0
                });
                
                // Log position updates occasionally (every 5 seconds)
                const now = Date.now();
                if (now - this.lastPositionUpdate > 5000) {
                    console.log(`Media Session: Position updated to ${position}/${totalDuration}`);
                    this.lastPositionUpdate = now;
                }
            }
        } catch (error) {
            console.error('Error updating media session position state:', error);
        }
    }
    
    /**
     * Start regular position state updates
     */
    startPositionUpdates() {
        // Clear any existing interval
        this.stopPositionUpdates();
        
        // Update position state every 250ms (4 times per second)
        this.positionUpdateInterval = setInterval(() => {
            this.updatePositionState();
        }, 250);
    }
    
    /**
     * Stop position state updates
     */
    stopPositionUpdates() {
        if (this.positionUpdateInterval) {
            clearInterval(this.positionUpdateInterval);
            this.positionUpdateInterval = null;
        }
    }
    
    /**
     * Handle playback state changes
     * @param {boolean} isPlaying - Whether audio is currently playing
     */
    handlePlaybackStateChange(isPlaying) {
        if (!this.isSupported) return;
        
        if (isPlaying) {
            // Make sure position updates are running when playing
            this.startPositionUpdates();
        } else {
            // When paused, update position one last time then stop updates
            this.updatePositionState();
        }
    }
    
    /**
     * Clean up resources when no longer needed
     */
    destroy() {
        this.stopPositionUpdates();
        
        // Clear action handlers if supported
        if (this.isSupported) {
            try {
                navigator.mediaSession.setActionHandler('play', null);
                navigator.mediaSession.setActionHandler('pause', null);
                navigator.mediaSession.setActionHandler('previoustrack', null);
                navigator.mediaSession.setActionHandler('nexttrack', null);
                navigator.mediaSession.setActionHandler('seekbackward', null);
                navigator.mediaSession.setActionHandler('seekforward', null);
                navigator.mediaSession.setActionHandler('seekto', null);
            } catch (error) {
                console.warn('Error clearing media session handlers:', error);
            }
        }
    }
}

// Export the class for use in other modules
window.MediaSessionManager = MediaSessionManager; 