{% extends "base.html" %}
{% load static %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'profiles/css/profile.css' %}">
    <style>
        .library-item {
            transition: transform 0.2s ease-in-out;
            height: 100%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .library-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .image-container {
            height: 300px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #000;
            border-top-left-radius: 0.25rem;
            border-top-right-radius: 0.25rem;
        }
        .content-image {
            width: auto;
            height: 100%;
            object-fit: cover;
        }
        .content-card {
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        .content-card-body {
            flex: 1;
            padding: 1.25rem;
        }
        .content-actions {
            margin-top: auto;
            display: flex;
            gap: 0.5rem;
        }
        .section-title {
            border-bottom: 2px solid #f8f9fa;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .empty-library {
            padding: 40px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin: 20px 0;
            color: #000;
        }
        .card-footer {
            background-color: white;
            border-top: 0;
            padding: 1rem 1.25rem;
        }
    </style>
{% endblock %}

{% block content %}
<div class="overlay"></div>
<div class="container">
    <div class="row">
        <div class="col">
            <hr>
            <h2 class="logo-font mb-4 text-center">My Library</h2>
            <hr>
        </div>
    </div>
    
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'profile' %}">My Profile</a></li>
                    <li class="breadcrumb-item active text-white" aria-current="page">My Library</li>
                </ol>
            </nav>
        </div>
    </div>
    
    {% if not ebooks and not audiobooks and not other_content %}
        <div class="empty-library">
            <i class="fas fa-book-open fa-3x mb-3"></i>
            <h3>Your library is empty</h3>
            <p>You haven't purchased any digital content yet.</p>
            <a href="{% url 'works' %}" class="btn btn-primary">Browse Works</a>
        </div>
    {% endif %}
    
    {% if ebooks %}
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title">
                <i class="fas fa-book me-2"></i>E-Books
            </h3>
        </div>
        
        {% for ebook in ebooks %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="library-item card">
                <div class="content-card">
                    <div class="image-container">
                        {% if ebook.image %}
                            <img src="{{ ebook.image.url }}" class="content-image" alt="{{ ebook.name }}">
                        {% else %}
                            <img src="{% static 'images/noimage.png' %}" class="content-image" alt="{{ ebook.name }}">
                        {% endif %}
                    </div>
                    
                    <div class="content-card-body">
                        <h5 class="card-title">{{ ebook.name }}</h5>
                        <p class="card-text small">{{ ebook.description|truncatechars:100|striptags|safe }}</p>
                    </div>
                    
                    <div class="card-footer">
                        <div class="content-actions">
                            <a href="{{ ebook.ebook_file.url }}" class="btn btn-primary" download>
                                <i class="fas fa-download me-2"></i>Download
                            </a>
                            <a href="{% url 'work_detail' ebook.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-info-circle me-2"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if audiobooks %}
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title">
                <i class="fas fa-headphones me-2"></i>Audiobooks
            </h3>
        </div>
        
        {% for audiobook in audiobooks %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="library-item card">
                <div class="content-card">
                    <div class="image-container">
                        {% if audiobook.image %}
                            <img src="{{ audiobook.image.url }}" class="content-image" alt="{{ audiobook.name }}">
                        {% else %}
                            <img src="{% static 'images/noimage.png' %}" class="content-image" alt="{{ audiobook.name }}">
                        {% endif %}
                    </div>
                    
                    <div class="content-card-body">
                        <h5 class="card-title">{{ audiobook.name }}</h5>
                        <p class="card-text small">{{ audiobook.description|truncatechars:100|striptags|safe }}</p>
                    </div>
                    
                    <div class="card-footer">
                        <div class="content-actions">
                            <a href="{% url 'audio_player' audiobook.id %}" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Play Audiobook
                            </a>
                            <a href="{% url 'work_detail' audiobook.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-info-circle me-2"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if other_content %}
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="section-title">
                <i class="fas fa-file-alt me-2"></i>Additional Content
            </h3>
        </div>
        
        {% for content in other_content %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="library-item card">
                <div class="content-card">
                    <div class="image-container">
                        {% if content.product.image %}
                            <img src="{{ content.product.image.url }}" class="content-image" alt="{{ content.product.name }}">
                        {% else %}
                            <img src="{% static 'images/noimage.png' %}" class="content-image" alt="{{ content.product.name }}">
                        {% endif %}
                    </div>
                    
                    <div class="content-card-body">
                        <h5 class="card-title">{{ content.product.name }}</h5>
                        <p class="card-text small">{{ content.product.description|truncatechars:100|striptags|safe }}</p>
                        
                        <div class="mt-3">
                            <h6>Attachments:</h6>
                            <ul class="list-group">
                                {% for attachment in content.product.attachments.all %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    {{ attachment.name }}
                                    
                                    {% if attachment.attachment_type == 'file' and attachment.file %}
                                    <a href="{{ attachment.file.url }}" class="btn btn-sm btn-primary" download>
                                        <i class="fas fa-download"></i>
                                    </a>
                                    {% elif attachment.attachment_type == 'link' and attachment.link %}
                                    <a href="{{ attachment.link }}" class="btn btn-sm btn-primary" target="_blank">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                    {% endif %}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="content-actions">
                            <a href="{% url 'work_detail' content.product.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-info-circle me-2"></i>Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="row">
        <div class="col-12 text-center">
            <a href="{% url 'profile' %}" class="btn btn-outline-primary mb-3">
                <i class="fas fa-arrow-left me-2"></i>Back to Profile
            </a>
        </div>
    </div>
</div>
{% endblock %}
