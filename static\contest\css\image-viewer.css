/* Fullscreen Image Viewer for Submission Details */

.image-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 9999;
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    cursor: zoom-in;
}

.image-viewer-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    cursor: zoom-in;
}

.image-viewer-img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
    transition: transform 0.3s ease;
    transform-origin: 50% 50%;
}

/* For panning when zoomed */
.image-viewer-container.panning {
    cursor: move;
    cursor: grabbing;
}

.image-viewer-controls {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 20px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 30px;
    width: fit-content;
    margin: 0 auto;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-viewer-overlay:hover .image-viewer-controls {
    opacity: 1;
}

.image-viewer-btn {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.image-viewer-btn:hover {
    background-color: rgba(255, 255, 255, 0.4);
}

.image-viewer-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    z-index: 10000;
}

.image-viewer-close:hover {
    background-color: rgba(255, 0, 0, 0.7);
}

/* Zoom levels */
.zoom-level-1 {
    transform: scale(1);
}

.zoom-level-2 {
    transform: scale(1.5);
}

.zoom-level-3 {
    transform: scale(2);
}

.zoom-level-4 {
    transform: scale(3);
}

/* Prevent text selection */
.no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Protected image */
.protected-image {
    position: relative;
    display: inline-block;
}

.protected-image img {
    pointer-events: none;
}

.fullscreen-btn {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    border-radius: 4px;
    padding: 5px 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    z-index: 100;
}

.fullscreen-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .image-viewer-controls {
        bottom: 60px;
    }

    .image-viewer-btn {
        width: 36px;
        height: 36px;
    }

    .image-viewer-close {
        top: 10px;
        right: 10px;
    }
}
