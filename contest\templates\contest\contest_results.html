{% extends "contest/base.html" %}
{% load static %}

{% block contest_title %}Results - {{ contest.name }}{% endblock %}

{% block contest_content %}
<div class="row">
    <div class="col-12">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="card-title mb-0">Contest Results</h2>
                <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-light btn-sm">Back to Contest</a>
            </div>
            <div class="card-body">
                {% if contest.results_announced %}
                    <div class="alert alert-success">
                        <h3 class="alert-heading">Winners Announced!</h3>
                        <p>The results for the {{ contest.name }} have been determined. Congratulations to all winners!</p>
                    </div>

                    <!-- Grand Prize Winner -->
                    {% if grand_winner %}
                        <div class="winner-section mb-5">
                            <h3 class="text-center mb-4">Grand Prize Winner</h3>
                            <div class="row justify-content-center">
                                <div class="col-md-8">
                                    <div class="card bg-light">
                                        <div class="card-body text-center">
                                            <h4 class="card-title">{{ grand_winner.title }}</h4>
                                            <h5 class="card-subtitle mb-3">by {{ grand_winner.user.username }}</h5>
                                            <div class="winner-image mb-3">
                                                <a href="{% url 'contest:submission_detail' slug=grand_winner.slug %}">
                                                    <img src="{{ grand_winner.file.url }}" class="img-fluid rounded" alt="{{ grand_winner.title }}">
                                                </a>
                                            </div>
                                            <div class="winner-stats">
                                                <span class="badge bg-primary">{{ grand_winner.total_likes }} Likes</span>
                                                <span class="badge bg-success">{{ grand_winner.total_jury_score }} Jury Points</span>
                                                <span class="badge bg-info">{{ grand_winner.category.name }}</span>
                                            </div>
                                            <div class="mt-3">
                                                <a href="{% url 'contest:submission_detail' slug=grand_winner.slug %}" class="btn btn-primary">View Submission</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Category Winners -->
                    {% if category_winners %}
                        <div class="category-winners mb-5">
                            <h3 class="text-center mb-4">Category Winners</h3>
                            <div class="row">
                                {% for winner in category_winners %}
                                    <div class="col-md-4 mb-4">
                                        <div class="card h-100">
                                            <div class="card-header bg-secondary text-white">
                                                <h5 class="card-title mb-0">{{ winner.category.name }}</h5>
                                            </div>
                                            <div class="card-body text-center">
                                                <h5 class="card-title">{{ winner.title }}</h5>
                                                <h6 class="card-subtitle mb-3">by {{ winner.user.username }}</h6>
                                                <div class="winner-image mb-3">
                                                    <a href="{% url 'contest:submission_detail' slug=winner.slug %}">
                                                        <img src="{{ winner.file.url }}" class="img-fluid rounded" alt="{{ winner.title }}" style="max-height: 200px;">
                                                    </a>
                                                </div>
                                                <div class="winner-stats">
                                                    <span class="badge bg-primary">{{ winner.total_likes }} Likes</span>
                                                    <span class="badge bg-success">{{ winner.total_jury_score }} Jury Points</span>
                                                </div>
                                                <div class="mt-3">
                                                    <a href="{% url 'contest:submission_detail' slug=winner.slug %}" class="btn btn-outline-primary btn-sm">View Submission</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                    <!-- Honorable Mentions -->
                    {% if honorable_mentions %}
                        <div class="honorable-mentions">
                            <h3 class="text-center mb-4">Honorable Mentions</h3>
                            <div class="row">
                                {% for submission in honorable_mentions %}
                                    <div class="col-md-3 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">{{ submission.title }}</h5>
                                                <h6 class="card-subtitle mb-3">by {{ submission.user.username }}</h6>
                                                <div class="winner-image mb-3">
                                                    <a href="{% url 'contest:submission_detail' slug=submission.slug %}">
                                                        <img src="{{ submission.file.url }}" class="img-fluid rounded" alt="{{ submission.title }}" style="max-height: 150px;">
                                                    </a>
                                                </div>
                                                <div class="winner-stats">
                                                    <span class="badge bg-primary">{{ submission.total_likes }} Likes</span>
                                                </div>
                                                <div class="mt-3">
                                                    <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-outline-secondary btn-sm">View</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endif %}

                {% else %}
                    <div class="alert alert-info">
                        <h3 class="alert-heading">Results Coming Soon!</h3>
                        <p>The results for {{ contest.name }} will be announced on {{ contest.results_date|date:"F j, Y" }}.</p>
                        <p>Thank you for your patience and participation!</p>
                    </div>

                    <div class="text-center my-5">
                        <img src="{% static 'contest/img/waiting.svg' %}" alt="Waiting for results" class="img-fluid" style="max-height: 300px;">
                        <h4 class="mt-4">The jury is currently reviewing all submissions</h4>
                        <p class="lead">Check back on {{ contest.results_date|date:"F j, Y" }} to see the winners!</p>
                    </div>
                {% endif %}
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-primary">Back to Contest</a>
                <a href="{% url 'contest:submission_gallery' slug=contest.slug %}" class="btn btn-outline-secondary">View All Submissions</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
