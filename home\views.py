from django.shortcuts import render
from django.utils import timezone
from django.contrib import messages
from django.db.models import Q
from works.models import Product
from blog.models import Post
from .models import CarouselItem, AudioListeningSession, AudioEvent
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_POST
from django.contrib.auth.models import User
from django.contrib.admin.views.decorators import staff_member_required
from django.db.models import Count, Avg, Sum
from django.db.models.functions import TruncDay
import json
import logging
from datetime import datetime, timedelta
from django.db import connection


logger = logging.getLogger(__name__)


def index(request):
    """
    Display the home page with carousel items and latest content.
    """
    carousel_items = CarouselItem.objects.filter(
        is_active=True,
        start_date__lte=timezone.now()
    ).exclude(
        end_date__isnull=False,
        end_date__lt=timezone.now()
    ).order_by('order')

    latest_post = Post.objects.filter(post_status=1)\
        .order_by('-post_created_on').first()
    latest_product = Product.objects.order_by('-created_date').first()

    context = {
        'carousel_items': carousel_items,
        'latest_post': latest_post,
        'latest_product': latest_product,
    }
    return render(request, 'home/index.html', context)


def search(request):
    """
    Display search results for works and blog posts.
    """
    query = request.GET.get('q', '')
    works = []
    blog_posts = []

    if 'q' in request.GET:
        if not query:
            messages.warning(request, "You didn't enter any search criteria!")
        else:
            works = Product.objects.filter(
                Q(name__icontains=query) |
                Q(description__icontains=query)
            ).distinct()

            blog_posts = Post.objects.filter(
                Q(post_title__icontains=query) |
                Q(post_content__icontains=query),
                post_status=1
            ).distinct()

    total_results = len(works) + len(blog_posts)

    context = {
        'works': works,
        'blog_posts': blog_posts,
        'search_term': query,
        'total_results': total_results,
    }

    return render(request, 'home/search_results.html', context)


def test_service_worker(request):
    """
    Display a page for testing the service worker functionality.
    """
    return render(request, 'home/test_service_worker.html')


@csrf_exempt
@require_POST
def audio_analytics(request):
    """
    API endpoint to receive audio player analytics data
    """
    try:
        data = json.loads(request.body)
        
        # Log the analytics data
        logger.info(
            f"Received audio analytics: {len(data.get('events', []))} events, "
            f"{len(data.get('sessions', []))} sessions"
        )
        
        # Process user information
        user_id = data.get('userId')
        user = None
        anonymous_user_id = None
        
        # Check if this is a registered user or anonymous
        if user_id and not user_id.startswith('anon_'):
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                # If user doesn't exist, treat as anonymous
                anonymous_user_id = user_id
        else:
            anonymous_user_id = user_id
        
        # Process sessions
        sessions = data.get('sessions', [])
        for session_data in sessions:
            # Convert timestamps to datetime objects
            start_time = datetime.fromtimestamp(
                session_data.get('startTime', 0) / 1000.0,
                tz=timezone.get_current_timezone()
            )
            
            end_time = None
            if session_data.get('endTime'):
                end_time = datetime.fromtimestamp(
                    session_data.get('endTime', 0) / 1000.0,
                    tz=timezone.get_current_timezone()
                )
            
            # Create or update session
            session, created = AudioListeningSession.objects.update_or_create(
                session_id=session_data.get('id'),
                defaults={
                    'user': user,
                    'anonymous_user_id': anonymous_user_id,
                    'work_id': data.get('workId', 'unknown'),
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration_ms': session_data.get('duration', 0),
                    'initial_position': session_data.get('initialPosition', 0),
                    'last_position': session_data.get('lastPosition', 0),
                    'completed': session_data.get('completed', False),
                    'progress_percentage': session_data.get('progressPercentage', 0),
                }
            )
            
            if created:
                logger.info(f"Created new session: {session.session_id}")
            else:
                logger.info(f"Updated existing session: {session.session_id}")
        
        # Process events
        events = data.get('events', [])
        for event_data in events:
            # Convert timestamp to datetime
            timestamp = datetime.fromtimestamp(
                event_data.get('timestamp', 0) / 1000.0,
                tz=timezone.get_current_timezone()
            )
            
            # Find associated session if any
            session = None
            session_id = event_data.get('sessionId')
            if session_id:
                try:
                    session = AudioListeningSession.objects.get(
                        session_id=session_id
                    )
                except AudioListeningSession.DoesNotExist:
                    pass
            
            # Create event
            event, created = AudioEvent.objects.update_or_create(
                event_id=event_data.get('id'),
                defaults={
                    'session': session,
                    'user': user,
                    'anonymous_user_id': anonymous_user_id,
                    'work_id': data.get('workId', 'unknown'),
                    'event_type': event_data.get('type', 'other'),
                    'timestamp': timestamp,
                    'playback_position': event_data.get('playbackPosition', 0),
                    'url': event_data.get('url', ''),
                }
            )
            
            # Store additional data as JSON
            if 'data' in event_data and event_data['data']:
                event.set_data(event_data['data'])
                event.save()
            
            if created:
                logger.debug(f"Created new event: {event.event_id}")
        
        # Log summary
        if sessions:
            total_duration = sum(session.get('duration', 0) for session in sessions)
            logger.info(
                f"User {data.get('userId')} listened to work "
                f"{data.get('workId')} for {total_duration/1000:.2f} seconds "
                f"across {len(sessions)} sessions"
            )
        
        return JsonResponse({'status': 'success'})
    except Exception as e:
        logger.error(f"Error processing audio analytics: {str(e)}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=400)


@staff_member_required
def analytics_dashboard(request):
    """
    Dashboard for audio player analytics - restricted to staff members only
    """
    # Get date range from request or use default (last 30 days)
    days = int(request.GET.get('days', 30))
    start_date = timezone.now() - timedelta(days=days)
    
    # Get sessions in date range
    sessions = AudioListeningSession.objects.filter(
        start_time__gte=start_date
    )
    
    # Get events in date range
    events = AudioEvent.objects.filter(
        timestamp__gte=start_date
    )
    
    # Basic stats
    total_sessions = sessions.count()
    
    # Count unique users (both registered and anonymous)
    registered_count = sessions.values('user').distinct().count()
    anon_filter = sessions.filter(user__isnull=True)
    anon_count = anon_filter.values('anonymous_user_id').distinct().count()
    total_users = registered_count + anon_count
    
    total_duration = sessions.aggregate(
        total=Sum('duration_ms')
    )['total'] or 0
    
    avg_session_duration = sessions.aggregate(
        avg=Avg('duration_ms')
    )['avg'] or 0
    
    # Check if the completed field exists in the database
    completed_field_exists = True
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT column_name FROM information_schema.columns 
            WHERE table_name='home_audiolisteningsession' AND column_name='completed'
        """)
        completed_field_exists = bool(cursor.fetchone())
    
    # Completion statistics - only calculate if the field exists
    completed_sessions = 0
    completed_percentage = 0
    avg_progress = 0
    
    if completed_field_exists:
        completed_sessions = sessions.filter(completed=True).count()
        completed_percentage = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
        avg_progress = sessions.aggregate(avg=Avg('progress_percentage'))['avg'] or 0
    else:
        # Use placeholder values when fields don't exist
        logger.warning("'completed' field does not exist in the database")
    
    # Sessions by day
    sessions_by_day = sessions.annotate(
        day=TruncDay('start_time')
    ).values('day').annotate(
        count=Count('id')
    ).order_by('day')
    
    # Events by type
    events_by_type = events.values('event_type').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # Most active works - avoid fields that may not exist
    if completed_field_exists:
        most_active_works = sessions.values('work_id').annotate(
            count=Count('id'),
            total_duration=Sum('duration_ms'),
            completion_rate=Avg('progress_percentage')
        ).order_by('-total_duration')[:10]
    else:
        most_active_works = sessions.values('work_id').annotate(
            count=Count('id'),
            total_duration=Sum('duration_ms')
        ).order_by('-total_duration')[:10]
        # Add placeholder for completion_rate
        for work in most_active_works:
            work['completion_rate'] = 0
    
    # Convert durations from ms to minutes
    for work in most_active_works:
        work['total_duration'] = work['total_duration'] / 1000 / 60 if work['total_duration'] else 0
    
    # Most active users - avoid fields that may not exist
    if completed_field_exists:
        most_active_users = sessions.values(
            'user__username', 'anonymous_user_id'
        ).annotate(
            count=Count('id'),
            total_duration=Sum('duration_ms'),
            completion_rate=Avg('progress_percentage')
        ).order_by('-total_duration')[:10]
    else:
        most_active_users = sessions.values(
            'user__username', 'anonymous_user_id'
        ).annotate(
            count=Count('id'),
            total_duration=Sum('duration_ms')
        ).order_by('-total_duration')[:10]
        # Add placeholder for completion_rate
        for user in most_active_users:
            user['completion_rate'] = 0
    
    # Convert durations from ms to minutes
    for user in most_active_users:
        user['total_duration'] = user['total_duration'] / 1000 / 60 if user['total_duration'] else 0
    
    # Prepare data for template
    context = {
        'total_sessions': total_sessions,
        'total_users': total_users,
        'total_duration': total_duration / 1000 / 60,  # Convert to minutes
        'avg_session_duration': avg_session_duration / 1000 / 60,  # Minutes
        'completed_sessions': completed_sessions,
        'completed_percentage': completed_percentage,
        'avg_progress': avg_progress,
        'sessions_by_day': list(sessions_by_day),
        'events_by_type': list(events_by_type),
        'most_active_works': list(most_active_works),
        'most_active_users': list(most_active_users),
        'days': days,
        'completed_field_exists': completed_field_exists
    }
    
    return render(request, 'home/analytics_dashboard.html', context)
