from django.shortcuts import render, redirect, reverse, get_object_or_404
from django.contrib import messages
from django.conf import settings
from works.models import Product
from profiles.models import UserProfile
from profiles.forms import UserProfileForm
from .models import OrderItem, Order
from .forms import OrderForm
from works.context_processors import bookcart_contents
import stripe
import json
from django.http import JsonResponse


def checkout(request):
    """
    Handle the checkout process for a user's bookcart.

    Processes both GET and POST requests. For GET requests, displays the
    checkout form and sets up Stripe payment. For POST requests, validates
    the order form, creates the order, and processes the payment.

    Requires user to be logged in.
    """
    stripe_public_key = settings.STRIPE_PUBLIC_KEY
    stripe_secret_key = settings.STRIPE_SECRET_KEY

    if not request.user.is_authenticated:
        messages.warning(
            request,
            'You need to be logged in to complete your purchase. '
            'Please log in or register to continue.'
        )
        return redirect(reverse('account_login') + '?next=' + reverse('checkout'))

    if request.method == 'POST':
        bookcart = request.session.get('bookcart', {})

        form_data = {
            'full_name': request.POST['full_name'],
            'email': request.POST['email'],
            'phone_number': request.POST['phone_number'],
            'billing_address1': request.POST['billing_address1'],
            'billing_address2': request.POST['billing_address2'],
            'billing_city': request.POST['billing_city'],
            'billing_postcode': request.POST['billing_postcode'],
            'billing_country': request.POST['billing_country'],
        }

        order_form = OrderForm(form_data)
        if order_form.is_valid():
            try:
                order = order_form.save(commit=False)
                if request.user.is_authenticated:
                    order.user = request.user

                order.payment_status = 'pending'
                order.save()

                pid = request.POST.get('client_secret', '').split('_secret')[0]

                # Create order items
                for item_id in bookcart:
                    try:
                        product = Product.objects.get(pk=item_id)
                        OrderItem.objects.create(
                            order=order,
                            product=product,
                            price=product.price
                        )
                    except Product.DoesNotExist:
                        messages.error(request, "Product not found.")
                        order.delete()
                        return redirect(reverse('view_bookcart'))

                # Update payment intent with order ID
                try:
                    if pid:
                        stripe.api_key = stripe_secret_key
                        print(f"Adding order_id: {order.id} to payment intent {pid}")
                        result = stripe.PaymentIntent.modify(
                            pid,
                            metadata={
                                'order_id': str(order.id),
                                'bookcart_items': json.dumps([{
                                    'id': item_id,
                                } for item_id in bookcart]),
                            }
                        )
                        print(f"Updated payment intent metadata: {result.metadata}")
                except Exception as e:
                    print(f"Error updating payment intent metadata: {str(e)}")
                    messages.warning(
                        request, 'Warning: Could not update payment information. '
                        'Your order is still being processed.'
                    )

                # Handle authenticated user profile
                if request.user.is_authenticated:
                    try:
                        profile = UserProfile.objects.get(user=request.user)
                        order.user_profile = profile
                        order.save()

                        # Calculate and save total
                        total = sum(
                            item.price
                            for item in order.orderitem_set.all()
                        )
                        order.total_amount = total
                        order.save()

                        if 'save-info' in request.POST:
                            request.session['save_info'] = True

                        # Set webhook triggered flag to ensure signal handler sends the email
                        order._webhook_triggered = True
                        order.payment_status = 'paid'
                        order.save()

                        # Don't send confirmation email directly - let the signal handler do it
                        # based on the combine_order_emails setting

                        return redirect(reverse(
                            'checkout_success', args=[order.id]))
                    except UserProfile.DoesNotExist:
                        messages.error(request, 'Profile not found.')
                        return redirect(reverse('view_bookcart'))

                # For non-authenticated users, let the signal handler send the email
                # Update payment status to 'paid' after successful processing
                order._webhook_triggered = True
                order.payment_status = 'paid'
                order.save()

                return redirect(reverse('checkout_success', args=[order.id]))

            except Exception as e:
                messages.error(
                    request, f'Error processing your order: {str(e)}'
                )
                return redirect(reverse('view_bookcart'))
        else:
            messages.error(request, f'Form errors: {order_form.errors}')
            return redirect(reverse('view_bookcart'))

    # GET request handling
    else:
        bookcart = request.session.get('bookcart', {})
        if not bookcart:
            messages.error(
                request, "You ain't having no books in your bookcart."
            )
            return redirect(reverse('all_works'))

        current_bookcart = bookcart_contents(request)
        total = current_bookcart['total']
        stripe_total = round(total * 100)

        # Create Stripe payment intent with error handling
        try:
            stripe.api_key = stripe_secret_key
            intent = stripe.PaymentIntent.create(
                amount=stripe_total,
                currency=settings.STRIPE_CURRENCY,
                automatic_payment_methods={'enabled': True},
                metadata={
                    'bookcart_items': json.dumps([{
                        'id': item_id,
                    } for item_id in bookcart]),
                    'username': request.user.username if request.user.is_authenticated else 'Guest',
                }
            )
        except stripe.error.StripeError as e:
            messages.error(request,
                f'There was an error setting up your payment. Please try again later. Error: {str(e)}')
            return redirect(reverse('view_bookcart'))
        except Exception as e:
            messages.error(request,
                'There was an unexpected error. Please try again later.')
            return redirect(reverse('view_bookcart'))

        # Pre-fill form for authenticated users
        if request.user.is_authenticated:
            try:
                profile = UserProfile.objects.get(user=request.user)
                initial_data = {
                    'full_name': profile.profile_full_name,
                    'email': profile.profile_email,
                    'phone_number': profile.profile_phone_number,
                    'billing_address1': profile.profile_address1,
                    'billing_address2': profile.profile_address2,
                    'billing_city': profile.profile_city,
                    'billing_postcode': profile.profile_postcode,
                    'billing_country': profile.profile_country,
                }
                order_form = OrderForm(initial=initial_data)
            except UserProfile.DoesNotExist:
                # If profile doesn't exist, create it
                UserProfile.objects.create(user=request.user)
                order_form = OrderForm()
        else:
            order_form = OrderForm()

    context = {
        'order_form': order_form,
        'stripe_public_key': stripe_public_key,
        'client_secret': intent.client_secret,
        'bookcart_items': current_bookcart['bookcart_items'],
        'total': total,
    }

    return render(request, 'checkout/checkout.html', context)


def checkout_success(request, order_id):
    """
    Handle successful checkouts and post-purchase processing.

    Updates order status, saves user profile information if requested,
    and cleans up the session bookcart.
    """
    order = get_object_or_404(Order, id=order_id)
    print(f"Checkout success for order {order_id} with initial payment status: {order.payment_status}")

    # Calculate total from order items
    total = sum(
        item.price
        for item in order.orderitem_set.all()
    )

    # Handle user profile if authenticated
    if request.user.is_authenticated:
        try:
            profile = request.user.profile
            # Save the user's info
            if 'save_info' in request.session:
                profile_data = {
                    'profile_full_name': order.full_name,
                    'profile_email': order.email,
                    'profile_phone_number': order.phone_number,
                    'profile_address1': order.billing_address1,
                    'profile_address2': order.billing_address2,
                    'profile_city': order.billing_city,
                    'profile_postcode': order.billing_postcode,
                    'profile_country': order.billing_country,
                }

                user_profile_form = UserProfileForm(
                    data=profile_data, instance=profile)
                if user_profile_form.is_valid():
                    user_profile_form.save()
                else:
                    messages.error(
                        request, 'There was an error with your form. '
                        'Please double check your information.'
                    )

        except UserProfile.DoesNotExist:
            messages.error(request, 'Profile not found.')

    # Check if this is a contest book purchase
    contest_book_purchase = request.session.get('contest_book_purchase', None)
    if contest_book_purchase:
        # Update the access request status
        from contest.models import BookAccessRequest
        try:
            access_request_id = contest_book_purchase.get('access_request_id')
            if access_request_id:
                access_request = BookAccessRequest.objects.get(id=access_request_id)
                access_request.status = 'approved'
                access_request.save()
                print(f"Updated contest book access request {access_request_id} to approved")
        except Exception as e:
            print(f"Error updating contest book access request: {str(e)}")

        # Remove the contest book purchase flag from session
        del request.session['contest_book_purchase']

    # Clean up the session
    if 'bookcart' in request.session:
        del request.session['bookcart']
    if 'save_info' in request.session:
        del request.session['save_info']

    # Only update payment status if still pending
    # This ensures we don't trigger signals multiple times
    if order.payment_status == 'pending':
        print(f"Changing payment status from 'pending' to 'paid' for order {order_id}")

        # Update status - this will trigger the signal to send the combined email
        # We don't need to send a separate confirmation email anymore
        order.payment_status = 'paid'
        # Set a flag to indicate this was triggered by the webhook
        order._webhook_triggered = True
        order.save()
        print(f"Order saved with payment status: {order.payment_status}")

    messages.success(
        request,
        f'Order successfully processed! Your order number is {order_id}. '
        f'A confirmation email has been sent to {order.email}.'
    )

    context = {
        'order': order,
        'total': total,
    }

    return render(request, 'checkout/checkout_success.html', context)

def cache_checkout_data(request):
    """
    Save additional data to the PaymentIntent metadata before confirming payment.

    This function allows custom data like user info or save-info preference
    to be stored in the payment intent for later use in webhooks.
    """
    try:
        pid = request.POST.get('client_secret', '').split('_secret')[0]
        if not pid:
            return JsonResponse({'error': 'No payment intent ID found'}, status=400)

        stripe.api_key = settings.STRIPE_SECRET_KEY

        # Get the current payment intent to preserve existing metadata
        intent = stripe.PaymentIntent.retrieve(pid)
        existing_metadata = intent.metadata.to_dict()

        # Add new metadata without overwriting existing metadata
        updated_metadata = {
            'save_info': request.POST.get('save_info', 'false'),
            'username': request.user.username if request.user.is_authenticated else 'Guest',
        }

        # Combine existing and new metadata
        combined_metadata = {**existing_metadata, **updated_metadata}

        # Update payment intent with combined metadata
        stripe.PaymentIntent.modify(pid, metadata=combined_metadata)

        return JsonResponse({'success': True})
    except Exception as e:
        import traceback
        print(f"Error in cache_checkout_data: {str(e)}")
        print(traceback.format_exc())

        messages.error(
            request,
            'Sorry, your payment cannot be processed right now. '
            'Please try again later.'
        )
        return JsonResponse({'error': str(e)}, status=400)

def payment_complete(request):
    """
    Handle customer's return after completing Stripe payment.

    This view processes the payment result, identifies the order, and either
    displays the success page or handles the error appropriately.
    """
    # Retrieve the payment intent ID and order ID from the URL query string
    payment_intent_id = request.GET.get('payment_intent', '')
    order_id = request.GET.get('order_id', '')

    if not payment_intent_id:
        messages.error(request, 'Payment information missing. Please try again.')
        return redirect(reverse('view_bookcart'))

    # Verify the payment intent status
    try:
        stripe.api_key = settings.STRIPE_SECRET_KEY
        intent = stripe.PaymentIntent.retrieve(payment_intent_id)

        print(f"Payment intent status: {intent.status}")
        print(f"Payment intent metadata: {intent.metadata}")
        print(f"Order ID from URL: {order_id}")

        # Try different methods to find the order
        order = None

        # Method 1: Use order ID from URL if available
        if order_id:
            try:
                order = Order.objects.get(id=order_id)
                print(f"Found order using URL order_id: {order_id}")
            except Order.DoesNotExist:
                print(f"Order with ID {order_id} from URL not found")

        # Method 2: Get order ID from payment intent metadata
        if not order and 'order_id' in intent.metadata:
            try:
                metadata_order_id = intent.metadata.get('order_id')
                order = Order.objects.get(id=metadata_order_id)
                print(f"Found order using metadata order_id: {metadata_order_id}")
            except Order.DoesNotExist:
                print(f"Order with ID {metadata_order_id} from metadata not found")

        # Method 3: Look for the most recent pending order for this user
        if not order and request.user.is_authenticated:
            try:
                # Try to find the most recent pending order for this user
                order = Order.objects.filter(
                    user=request.user,
                    payment_status='pending'
                ).order_by('-order_date').first()

                if order:
                    print(f"Found recent pending order for user: {order.id}")
            except Exception as e:
                print(f"Error finding recent order: {str(e)}")

        # Process the order if found
        if order:
            # If payment succeeded, redirect to success page
            if intent.status == 'succeeded':
                order.payment_status = 'paid'
                order.save()
                print(f"Updated order {order.id} status to paid")

                # Clear shopping cart
                if 'bookcart' in request.session:
                    del request.session['bookcart']

                # Don't send confirmation email here - it will be sent in checkout_success

                return redirect(reverse('checkout_success', args=[order.id]))
            else:
                # Payment required further action or failed
                messages.warning(
                    request,
                    f'Payment processing incomplete. Status: {intent.status}. '
                    'Please try again or contact support.'
                )
                return redirect(reverse('view_bookcart'))
        else:
            # Could not find the order
            print("Could not find any matching order")
            messages.warning(
                request,
                'Payment received but order information is missing. '
                'Please contact customer support with payment ID: '
                f'{payment_intent_id}'
            )
            return redirect(reverse('view_bookcart'))
    except stripe.error.StripeError as e:
        print(f"Stripe error in payment_complete: {str(e)}")
        messages.error(
            request, f'There was an error verifying your payment: {str(e)}'
        )
        return redirect(reverse('view_bookcart'))
    except Exception as e:
        import traceback
        print(f"Unexpected error in payment_complete: {str(e)}")
        print(traceback.format_exc())

        messages.error(
            request, 'An unexpected error occurred. Please try again later.'
        )
        return redirect(reverse('view_bookcart'))

def create_order(request):
    """
    Create an order via AJAX before processing payment.

    This ensures the order exists before payment is processed,
    allowing us to track the order ID throughout the payment flow.
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    bookcart = request.session.get('bookcart', {})
    if not bookcart:
        return JsonResponse({'error': 'Your bookcart is empty'}, status=400)

    form_data = {
        'full_name': request.POST.get('full_name', ''),
        'email': request.POST.get('email', ''),
        'phone_number': request.POST.get('phone_number', ''),
        'billing_address1': request.POST.get('billing_address1', ''),
        'billing_address2': request.POST.get('billing_address2', ''),
        'billing_city': request.POST.get('billing_city', ''),
        'billing_postcode': request.POST.get('billing_postcode', ''),
        'billing_country': request.POST.get('billing_country', ''),
    }

    order_form = OrderForm(form_data)

    if order_form.is_valid():
        try:
            order = order_form.save(commit=False)

            if request.user.is_authenticated:
                order.user = request.user

            order.payment_status = 'pending'
            order.save()

            print(f"Created order ID: {order.id}")

            # Create order items
            current_bookcart = bookcart_contents(request)
            total = current_bookcart['total']

            for item_id in bookcart:
                try:
                    product = Product.objects.get(pk=item_id)
                    OrderItem.objects.create(
                        order=order,
                        product=product,
                        price=product.price
                    )
                except Product.DoesNotExist:
                    order.delete()
                    return JsonResponse({
                        'error': f'Product with ID {item_id} not found'
                    }, status=400)

            # Calculate and save total
            order.total_amount = total
            order.save()

            # Get the payment intent from the client_secret
            client_secret = request.POST.get('client_secret', '')
            if client_secret:
                try:
                    pid = client_secret.split('_secret')[0]
                    stripe.api_key = settings.STRIPE_SECRET_KEY

                    # Update payment intent with order ID
                    stripe.PaymentIntent.modify(
                        pid,
                        metadata={
                            'order_id': str(order.id),
                        }
                    )
                    print(f"Updated payment intent {pid} with order ID {order.id}")
                except Exception as e:
                    print(f"Error updating payment intent: {str(e)}")
                    # Continue anyway, as we have the order created

            return JsonResponse({
                'success': True,
                'order_id': order.id
            })

        except Exception as e:
            import traceback
            print(f"Error creating order: {str(e)}")
            print(traceback.format_exc())
            return JsonResponse({'error': str(e)}, status=500)
    else:
        return JsonResponse({
            'error': 'Invalid form data',
            'form_errors': order_form.errors
        }, status=400)
