{% extends "contest/base.html" %}
{% load static %}

{% block contest_title %}Book Access Request - {{ contest.name }}{% endblock %}

{% block contest_content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="card-title mb-0">Contest Book Access Request</h2>
                <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-light btn-sm">Back to Contest</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-heading">About Book Access</h4>
                    <p>As a contest participant, you can request access to one part of "The Age of New Era" to inspire your artwork. You have two options:</p>
                    <ul>
                        <li><strong>Free Access:</strong> Request free access specifically for contest participants</li>
                        <li><strong>Support with $1:</strong> Purchase access for $1 to support the project</li>
                    </ul>
                    <p>All contest participants will receive access to the full novel after or on August 8th, 2023, regardless of whether they win or not.</p>
                </div>

                <form method="post" class="mt-4">
                    {% csrf_token %}
                    
                    <div class="mb-4">
                        <h5>1. Choose Your Format</h5>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="format_ebook" value="ebook" checked>
                            <label class="form-check-label" for="format_ebook">
                                E-book
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="format" id="format_audiobook" value="audiobook">
                            <label class="form-check-label" for="format_audiobook">
                                Audiobook
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5>2. Select Book Part</h5>
                        <div class="form-group">
                            <select class="form-select" name="book_part" id="book_part" required>
                                <option value="" selected disabled>-- Select a book part --</option>
                                <optgroup label="E-book Parts" id="ebook_parts">
                                    {% for part in ebook_parts %}
                                    <option value="{{ part.id }}">{{ part.name }}</option>
                                    {% endfor %}
                                </optgroup>
                                <optgroup label="Audiobook Parts" id="audiobook_parts" style="display:none;">
                                    {% for part in audiobook_parts %}
                                    <option value="{{ part.id }}">{{ part.name }}</option>
                                    {% endfor %}
                                </optgroup>
                            </select>
                            <div class="form-text">Choose one part that you'd like to access for inspiration.</div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5>3. Access Method</h5>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="access_method" id="access_free" value="free" checked>
                            <label class="form-check-label" for="access_free">
                                Request Free Access (for contest participants)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="access_method" id="access_paid" value="paid">
                            <label class="form-check-label" for="access_paid">
                                Support with $1 (immediate access)
                            </label>
                        </div>
                    </div>
                    
                    <div id="free_access_section">
                        <div class="mb-4">
                            <h5>4. Brief Reason for Request</h5>
                            <textarea class="form-control" name="reason" id="reason" rows="3" maxlength="200" placeholder="Briefly describe why you're interested in this part (max 200 characters)"></textarea>
                            <div class="form-text"><span id="reason_chars">0</span>/200 characters</div>
                        </div>
                    </div>
                    
                    <div id="paid_access_section" style="display:none;">
                        <div class="mb-4">
                            <h5>4. Payment Information</h5>
                            <div class="alert alert-warning">
                                <p>After clicking "Submit Request", you'll be redirected to a secure payment page to complete your $1 support payment.</p>
                                <p>This is a symbolic amount to support the project and get immediate access to your selected part.</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <h5>5. Contest Information</h5>
                        <div class="form-group mb-3">
                            <label for="category">Which category are you planning to enter?</label>
                            <select class="form-select" name="category" id="category" required>
                                <option value="" selected disabled>-- Select a category --</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="agree_terms" id="agree_terms" required>
                            <label class="form-check-label" for="agree_terms">
                                I agree to the <a href="{% url 'contest:contest_terms' slug=contest.slug %}" target="_blank">contest terms and conditions</a>
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary btn-lg">Submit Request</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
{{ block.super }}
<script>
    // Toggle between e-book and audiobook parts
    document.addEventListener('DOMContentLoaded', function() {
        const formatRadios = document.querySelectorAll('input[name="format"]');
        const ebookParts = document.getElementById('ebook_parts');
        const audiobookParts = document.getElementById('audiobook_parts');
        
        formatRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'ebook') {
                    ebookParts.style.display = 'block';
                    audiobookParts.style.display = 'none';
                    // Reset selection
                    document.getElementById('book_part').selectedIndex = 0;
                } else {
                    ebookParts.style.display = 'none';
                    audiobookParts.style.display = 'block';
                    // Reset selection
                    document.getElementById('book_part').selectedIndex = 0;
                }
            });
        });
        
        // Toggle between free and paid access sections
        const accessMethodRadios = document.querySelectorAll('input[name="access_method"]');
        const freeAccessSection = document.getElementById('free_access_section');
        const paidAccessSection = document.getElementById('paid_access_section');
        
        accessMethodRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'free') {
                    freeAccessSection.style.display = 'block';
                    paidAccessSection.style.display = 'none';
                } else {
                    freeAccessSection.style.display = 'none';
                    paidAccessSection.style.display = 'block';
                }
            });
        });
        
        // Character counter for reason textarea
        const reasonTextarea = document.getElementById('reason');
        const reasonChars = document.getElementById('reason_chars');
        
        reasonTextarea.addEventListener('input', function() {
            const remaining = this.value.length;
            reasonChars.textContent = remaining;
        });
    });
</script>
{% endblock %}
{% endblock %}
