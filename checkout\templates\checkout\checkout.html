{% extends "base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block extra_css %}
    <link rel="stylesheet" href="{% static 'checkout/css/stripe.css' %}">
{% endblock %}

{% block content %}
<div class="container bg-black bg-opacity-75 p-3 rounded mb-4">
    <div class="row">
        <div class="col">
            <hr>
            <h2 class="logo-font mb-4">Book Cart</h2>
            <hr>
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-md-8 col-lg-6 mx-auto">
            <!-- Order Summary Section -->
            <div class="mb-5">
                <p>We calculated number of your story journeys, that's {{ product_count }}</p>
                <div class="row">
                    <div class="col-7 offset-2">
                        <p class="mb-1 mt-0 small">Story Title</p>
                    </div>
                    <div class="col-3 text-end">
                        <p class="mb-1 mt-0 small">Price</p>
                    </div>
                </div>
                {% for item in bookcart_items %}
                    <div class="row">
                        <div class="col-2 mb-1">
                            
                            {% if item.product.id %}
                                <a href="{% url 'work_detail' item.product.id %}" aria-label="View details of {{ item.product.name }}">
                                    {% if item.product.image %}
                                        <img class="w-100" src="{{ item.product.image.url }}" alt="{{ item.product.name }}">
                                    {% else %}
                                        <img class="w-100" src="{% static 'images/noimage.png' %}" alt="{{ item.product.name }}">
                                    {% endif %}
                                </a>
                            {% endif %}
                        </div>
                        
                        <div class="col-7">
                            <a href="{% url 'work_detail' item.product.id %}" class="text-decoration-none" aria-label="View details of {{ item.product.name }}">
                                <p class="my-0"><strong>{{ item.product.name }}</strong></p>
                            </a>
                        </div>
                        <div class="col-3 text-end">
                            <p class="my-0">${{ item.price }}</p>
                        </div>
                    </div>
                {% endfor %}
                <hr class="my-0">
                <div class="row">
                    <div class="col-7 offset-2">
                        <p class="my-0 text-end">Total:</p>
                    </div>
                    <div class="col-3 text-end">
                        <p class="my-0"><strong>${{ total | floatformat:2 }}</strong></p>
                    </div>
                </div>
                <!-- Tax notice -->
                <div class="row mt-2">
                    <div class="col-12 text-end">
                        <p class="small text-muted">Final price (taxes may apply in some locations)</p>
                    </div>
                </div>
            </div>
            
            <!-- Checkout Form Section -->
            <div>
                <h3 class="mb-3">Checkout Information</h3>
                <p>Please fill out the form below to get your goodies delivered nicely.</p>
                <form method="post" action="{% url 'checkout' %}" id="payment-form">
                    {% csrf_token %}
                    <fieldset class="rounded px-3 mb-5">
                        <legend class="fieldset-label small px-2 w-auto">Details</legend>
                        {{ order_form.full_name | as_crispy_field }}
                        {{ order_form.email | as_crispy_field }}
                    </fieldset>
                    <fieldset class="rounded px-3 mb-5">
                        <legend class="fieldset-label small px-2 w-auto">Billing Address</legend>
                        {{ order_form.phone_number | as_crispy_field }}
                        {{ order_form.billing_address1 | as_crispy_field }}
                        {{ order_form.billing_address2 | as_crispy_field }}
                        {{ order_form.billing_city | as_crispy_field }}
                        {{ order_form.billing_postcode | as_crispy_field }}
                        {{ order_form.billing_country | as_crispy_field }}
                        <div class="form-check form-check-inline float-end me-0">
                            {% if user.is_authenticated %}
                                <label class="form-check-label" for="id-save-info">Save this billing information to my profile</label>
                                <input class="form-check-input ms-2 me-0" type="checkbox" id="id-save-info" name="save-info" checked>
                            {% else %}
                                <label class="form-check-label" for="id-save-info">
                                    <a class="text-info" href="{% url 'account_signup' %}" aria-label="Create an account to save billing information">Create an account</a> or 
                                    <a class="text-info" href="{% url 'account_login' %}" aria-label="Login to save billing information">login</a> to save this information
                                </label>
                            {% endif %}
                        </div>
                    </fieldset>
                    <fieldset class="px-3">
                        <legend class="fieldset-label small px-2 w-auto">Payment</legend>
                        
                        <!-- Stripe Payment Element will go here -->
                        <div class="mb-3" id="payment-element"></div>
                        
                        <!-- Display form errors -->
                        <div class="mb-3 text-danger" id="card-errors" role="alert"></div>
                        
                        <!-- Pass the client secret to the view -->
                        <input type="hidden" value="{{ client_secret }}" name="client_secret">
                    </fieldset>

                    <div class="submit-button text-end mt-5 mb-2">
                        <a href="{% url 'view_bookcart' %}" class="btn btn-outline-secondary">
                            <span class="icon">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                            <span class="fw-bold">Adjust Bookcart</span>
                        </a>
                        <button id="submit-button" class="btn btn-primary">
                            <span class="fw-bold">Complete Order</span>
                            <span class="icon">
                                <i class="fas fa-lock"></i>
                            </span>
                        </button>
                        <p class="small text-danger my-0">
                            <span class="icon">
                                <i class="fas fa-exclamation-circle"></i>
                            </span>
                            <span>Your card will be charged <strong>${{ total|floatformat:2 }}</strong></span>
                        </p>
                    </div>
                </form>
                <div id="loading-overlay" class="overlay">
                    <h1 class="text-light logo-font loading-spinner">
                        <span class="icon">
                            <i class="fas fa-3x fa-sync-alt fa-spin"></i>
                        </span>
                    </h1>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    {{ block.super }}
    {{ stripe_public_key|json_script:"id_stripe_public_key" }}
    {{ client_secret|json_script:"id_client_secret" }}
    <script src="https://js.stripe.com/v3/"></script>
    <script src="{% static 'checkout/js/stripe_elements.js' %}"></script>
{% endblock %}
