from django import forms
from .models import Submission, JuryScore, Comment
from utils.image_validation import validate_image
from django.core.exceptions import ValidationError
from django.conf import settings
from django.forms.widgets import Select


class BookPartSelect(Select):
    """
    Custom select widget that adds the book_part attribute to each option.
    """
    def create_option(self, name, value, label, selected, index, subindex=None, attrs=None):
        option = super().create_option(name, value, label, selected, index, subindex, attrs)
        if value and hasattr(value, 'value'):
            # For Django 3.2+, value is a ModelChoiceIteratorValue
            pk = value.value
            # Get the book_part attribute from the model instance
            instance = self.choices.queryset.filter(pk=pk).first()
            if instance and hasattr(instance, 'book_part'):
                option['attrs']['data-book-part'] = instance.book_part
        elif value:
            # For older Django versions or direct values
            try:
                # Try to use value directly as pk
                instance = self.choices.queryset.filter(pk=value).first()
                if instance and hasattr(instance, 'book_part'):
                    option['attrs']['data-book-part'] = instance.book_part
            except (ValueError, TypeError):
                pass
        return option


class SubmissionForm(forms.ModelForm):
    """
    Form for creating and editing contest submissions.
    """
    file = forms.FileField(
        validators=[validate_image],
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/jpeg,image/jpg,image/png,image/webp'
        }),
        help_text="Upload your artwork (max 10MB). Accepted formats: JPEG, JPG, PNG, WebP."
    )

    rating = forms.ChoiceField(
        choices=[(1, '1'), (2, '2'), (3, '3'), (4, '4'), (5, '5')],
        widget=forms.RadioSelect(attrs={'class': 'star-rating'}),
        required=True,
        label="Rating (1-5 stars)",
        help_text="Your rating for the book part that inspired your artwork."
    )

    ai_tools_used = forms.BooleanField(
        required=False,
        label="AI Tools Used",
        help_text="Check if you used AI tools like Midjourney, DALL-E, or Stable Diffusion"
    )
    ai_tools_details = forms.CharField(
        required=False,
        label="AI Tools Details",
        widget=forms.Textarea(attrs={
            'rows': 3,
            'class': 'form-control',
            'placeholder': 'Describe which AI tools you used, what prompts you provided, and any post-processing steps'
        }),
        help_text="Please provide specific details about the AI tools used in creating your artwork"
    )

    class Meta:
        model = Submission
        fields = [
            'title', 'description', 'category', 'file', 'model_3d_url',
            'book_part', 'rating', 'review_text', 'book_fragment', 'chapter_reference',
            'ai_tools_used', 'ai_tools_details'
        ]
        widgets = {
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Describe your artwork, techniques used, and inspiration.'
            }),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'model_3d_url': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://sketchfab.com/models/your-model-id/embed or other platform URL'
            }),
            'book_part': BookPartSelect(attrs={
                'class': 'form-control form-select',
                'data-placeholder': 'Select a book part...'
            }),
            'review_text': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': 'Write a 48-200 word review explaining how the selected part inspired your artwork.'
            }),
            'book_fragment': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Include at least one sentence from the book that inspired your artwork. No need to add quotation marks - they will be added automatically.'
            }),
            'chapter_reference': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g., Chapter 5: The Awakening'
            }),
        }

    def clean_file(self):
        """
        Validate the uploaded file.
        """
        file = self.cleaned_data.get('file')
        if file:
            # Check file size
            if file.size > settings.MAX_UPLOAD_SIZE:
                raise ValidationError(
                    f'File too large. Max size is {settings.MAX_UPLOAD_SIZE/1024/1024:.0f}MB.'
                )

            # Only image files are allowed now
            # 3D models should be shared via links in the description

        return file

    def clean(self):
        """
        Custom form validation.

        Ensures that ai_tools_details is provided when ai_tools_used is checked.
        """
        cleaned_data = super().clean()
        ai_tools_used = cleaned_data.get('ai_tools_used')
        ai_tools_details = cleaned_data.get('ai_tools_details')

        if ai_tools_used and not ai_tools_details:
            self.add_error(
                'ai_tools_details',
                'Please provide details about the AI tools used for your artwork.'
            )

        return cleaned_data

    def clean_review_text(self):
        """
        Validate the review text length.
        """
        review_text = self.cleaned_data.get('review_text')
        if review_text:
            word_count = len(review_text.split())
            if word_count < 48 or word_count > 200:
                raise ValidationError(
                    'Review must be between 48 and 200 words.'
                )
        return review_text


class JuryScoreForm(forms.ModelForm):
    """
    Form for jury members to score submissions.
    """
    class Meta:
        model = JuryScore
        fields = ['points', 'comment']
        widgets = {
            'points': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': 1,
                'max': 100
            }),
            'comment': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Optional: Add comments about this submission.'
            }),
        }

    def clean_points(self):
        """
        Validate the points value.
        """
        points = self.cleaned_data.get('points')
        if points < 1 or points > 100:
            raise ValidationError('Points must be between 1 and 100.')
        return points


class CommentForm(forms.ModelForm):
    """
    Form for adding comments to submissions.
    """
    class Meta:
        model = Comment
        fields = ['content']
        widgets = {
            'content': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Share your thoughts about this submission...'
            }),
        }

    def clean_content(self):
        """
        Validate the comment content.
        """
        content = self.cleaned_data.get('content')
        if not content or len(content.strip()) < 2:
            raise ValidationError('Comment must not be empty.')
        return content
