# Generated by Django 5.1.5 on 2025-02-03 10:44

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CollaborationRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('email', models.EmailField(max_length=254)),
                ('collaboration_type', models.CharField(choices=[('WRITING', 'Writing Partnership'), ('REVIEW', 'Book Review'), ('BETA', 'Beta Reading'), ('OTHER', 'Other')], default='OTHER', max_length=20)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
        ),
    ]
