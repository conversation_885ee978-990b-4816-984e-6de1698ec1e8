from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from image_cropping import ImageCroppingMixin
from .models import CarouselItem, AudioListeningSession, AudioEvent
from .forms import CarouselItemAdminForm


# Add a link to the analytics dashboard in the admin index
class AudioAnalyticsAdminSite(admin.AdminSite):
    """
    Custom admin site with analytics dashboard link
    """
    def each_context(self, request):
        context = super().each_context(request)
        context['analytics_dashboard_url'] = reverse('analytics_dashboard')
        return context


# Register the custom admin site
admin_site = AudioAnalyticsAdminSite(name='audio_analytics_admin')


@admin.register(CarouselItem)
class CarouselItemAdmin(ImageCroppingMixin, admin.ModelAdmin):
    """
    Admin interface for managing carousel items on the home page.
    """
    form = CarouselItemAdminForm
    list_display = (
        'title',
        'style',
        'order',
        'is_active',
        'cropping',
    )
    list_filter = ('is_active', 'style')
    search_fields = ('title', 'subtitle')
    fieldsets = (
        ('Basic Information', {
            'fields': (
                ('title', 'style'),
                'subtitle',
                'description',
            )
        }),
        ('Image Settings', {
            'description': (
                'Image min 1600x800 (best fit 1920x1080) for optimal display.'
            ),
            'fields': (
                'image',
                'alt_text',
                'cropping',
            )
        }),
        ('Link Settings', {
            'description': 'Choose one of the following link types',
            'fields': (
                'product',
                'blog_post',
                'external_link',
                'open_in_new_tab',
            )
        }),
        ('Display Settings', {
            'fields': (
                'order',
                'is_active',
                'start_date',
                'end_date',
            )
        }),
    )
    ordering = ('order',)
    list_editable = ('order', 'is_active')


@admin.register(AudioListeningSession)
class AudioListeningSessionAdmin(admin.ModelAdmin):
    list_display = (
        'session_id', 
        'get_user_display', 
        'work_id', 
        'start_time', 
        'end_time', 
        'get_duration_display',
        'completed',
        'progress_percentage',
        'view_analytics_dashboard',
    )
    list_filter = ('start_time', 'work_id', 'completed')
    search_fields = ('session_id', 'work_id', 'anonymous_user_id')
    date_hierarchy = 'start_time'
    
    def get_user_display(self, obj):
        if obj.user:
            return f"{obj.user.username} (ID: {obj.user.id})"
        return f"Anonymous ({obj.anonymous_user_id})"
    get_user_display.short_description = 'User'
    
    def get_duration_display(self, obj):
        if obj.duration_ms:
            seconds = obj.duration_ms / 1000
            minutes = seconds // 60
            seconds = seconds % 60
            if minutes > 60:
                hours = minutes // 60
                minutes = minutes % 60
                return f"{int(hours)}h {int(minutes)}m {int(seconds)}s"
            return f"{int(minutes)}m {int(seconds)}s"
        return "N/A"
    get_duration_display.short_description = 'Duration'
    
    def view_analytics_dashboard(self, obj):
        url = reverse('analytics_dashboard')
        return format_html('<a href="{}">View Dashboard</a>', url)
    view_analytics_dashboard.short_description = 'Analytics'


@admin.register(AudioEvent)
class AudioEventAdmin(admin.ModelAdmin):
    list_display = (
        'event_id', 
        'event_type', 
        'get_user_display', 
        'work_id', 
        'timestamp', 
        'playback_position',
        'view_analytics_dashboard',
    )
    list_filter = ('event_type', 'timestamp', 'work_id')
    search_fields = ('event_id', 'work_id', 'anonymous_user_id')
    date_hierarchy = 'timestamp'
    
    def get_user_display(self, obj):
        if obj.user:
            return f"{obj.user.username} (ID: {obj.user.id})"
        return f"Anonymous ({obj.anonymous_user_id})"
    get_user_display.short_description = 'User'
    
    def view_analytics_dashboard(self, obj):
        url = reverse('analytics_dashboard')
        return format_html('<a href="{}">View Dashboard</a>', url)
    view_analytics_dashboard.short_description = 'Analytics'
