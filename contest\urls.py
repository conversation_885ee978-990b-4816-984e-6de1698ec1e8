from django.urls import path
from . import views

app_name = 'contest'

urlpatterns = [
    # Contest views
    path('', views.contest_list, name='contest_list'),
    path('<slug:slug>/', views.contest_detail, name='contest_detail'),
    path('<slug:slug>/terms/', views.contest_terms, name='contest_terms'),
    path('<slug:slug>/guide/', views.contest_guide, name='contest_guide'),
    path('<slug:slug>/book-access/', views.book_access_request, name='book_access_request'),

    # Submission views
    path('<slug:slug>/submit/', views.submission_create, name='submission_create'),
    # Prioritize slug patterns over pk patterns
    path('submission/<slug:slug>/', views.submission_detail, name='submission_detail'),
    path('submission/<int:pk>/', views.submission_detail_pk, name='submission_detail_pk'),
    path('submission/<slug:slug>/edit/', views.submission_edit, name='submission_edit'),
    path('submission/<int:pk>/edit/', views.submission_edit_pk, name='submission_edit_pk'),
    path('submission/<slug:slug>/delete/', views.submission_delete, name='submission_delete'),
    path('submission/<int:pk>/delete/', views.submission_delete_pk, name='submission_delete_pk'),

    # Gallery views
    path('<slug:slug>/gallery/', views.submission_gallery, name='submission_gallery'),

    # Voting views
    path('submission/<slug:slug>/like/', views.submission_like, name='submission_like'),
    path('submission/<int:pk>/like/', views.submission_like_pk, name='submission_like_pk'),
    path('submission/<slug:slug>/unlike/', views.submission_unlike, name='submission_unlike'),
    path('submission/<int:pk>/unlike/', views.submission_unlike_pk, name='submission_unlike_pk'),

    # API endpoints
    path('api/check-review/', views.check_existing_review, name='check_existing_review'),

    # Jury views
    path('jury/<slug:slug>/', views.jury_dashboard, name='jury_dashboard'),
    path('jury/submission/<slug:slug>/score/', views.jury_score_submission, name='jury_score_submission'),
    path('jury/submission/<int:pk>/score/', views.jury_score_submission_pk, name='jury_score_submission_pk'),

    # Leaderboard
    path('<slug:slug>/leaderboard/', views.leaderboard, name='leaderboard'),

    # Results
    path('<slug:slug>/results/', views.contest_results, name='contest_results'),
]
