{% extends "base.html" %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'works/css/works.css' %}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center my-4">Our Works</h2>
        </div>
    </div>

    <!-- Category Navigation -->
    <div class="text-center mb-4">
        <a href="{% url 'works' %}"
           class="btn {% if not current_category %}btn-primary{% else %}btn-outline-primary{% endif %} me-2"
           aria-label="View all works">
            All
        </a>
        {% for category in categories %}
            <a href="{% url 'works' %}?category={{ category.name }}"
               class="btn {% if current_category == category.name %}btn-primary{% else %}btn-outline-primary{% endif %} me-2"
               aria-label="View works in category {{ category.friendly_name }}">
                {{ category.friendly_name }}
            </a>
        {% endfor %}
    </div>

    <!-- Work Cards -->
    <div class="row g-4">
        {% for work in works %}
            <div class="col-md-4 mb-4">
                <div class="card work-card h-100">
                    <div class="work-image-container bg-black rounded">
                        {% if work.image %}
                            <img src="{{ work.image.url }}" class="card-img-top img-fluid rounded" alt="{{ work.name }}">
                        {% else %}
                            <img src="{% static 'images/noimage.png' %}" class="card-img-top img-fluid" alt="{{ work.name }}">
                        {% endif %}
                    </div>

                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'work_detail' work.id %}"
                               class="text-decoration-none text-dark"
                               aria-label="View details of {{ work.name }}">
                                {{ work.name }}
                            </a>
                        </h5>
                        <p class="card-text work-description">{{ work.description|truncatewords:20|striptags|safe }}</p>

                        <div class="d-flex justify-content-between align-items-center">
                            <span class="h5 mb-0">${{ work.price }}</span>
                            <span class="badge bg-primary">{{ work.category.friendly_name }}</span>
                        </div>

                        <div class="rating mt-2">
                            {% with average_rating=work.average_rating %}
                                {% if average_rating %}
                                    <small class="text-muted">
                                        <i class="fas fa-star text-warning"></i>
                                        {{ average_rating|floatformat:1 }} / 5
                                        ({{ work.total_reviews_count }} reviews)
                                    </small>
                                {% else %}
                                    <small class="text-muted">No ratings yet</small>
                                {% endif %}
                            {% endwith %}
                        </div>
                    </div>

                    <div class="card-footer bg-white border-top-0">
                        <a href="{% url 'work_detail' work.id %}"
                           class="btn btn-outline-primary w-100"
                           aria-label="View details of {{ work.name }}">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
{% endblock %}
