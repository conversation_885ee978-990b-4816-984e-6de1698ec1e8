import stripe
import logging
import json
from django.conf import settings
from django.http import HttpResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from .models import Order


# Set up logger
logger = logging.getLogger(__name__)


@require_POST
@csrf_exempt
def stripe_webhook(request):
    """Handle Stripe webhooks"""

    # Get the webhook data and verify signature
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE')

    if not sig_header:
        logger.error("No Stripe signature header found")
        return HttpResponse(status=400)

    webhook_secret = settings.STRIPE_WEBHOOK_SECRET

    if not webhook_secret:
        logger.error("No webhook secret found in settings")
        return HttpResponse("Webhook secret not configured", status=500)

    # Log the incoming webhook data for debugging
    logger.info(f"Received webhook with signature: {sig_header[:10]}...")
    logger.info(f"Using webhook secret: {webhook_secret[:5]}...")

    # For debugging, print the raw payload
    try:
        event_json = json.loads(payload)
        logger.info(f"Webhook event type: {event_json.get('type')}")
        logger.info(f"Webhook event ID: {event_json.get('id')}")
    except Exception as e:
        logger.error(f"Error parsing webhook payload: {str(e)}")

    try:
        # Parse the event
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
        data = event['data']
    except ValueError as e:
        # Invalid payload
        logger.error(f"Invalid payload: {str(e)}")
        return HttpResponse(content=f"Invalid payload: {str(e)}", status=400)
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        logger.error(f"Invalid signature: {str(e)}")
        logger.error(f"This usually means the webhook secret in your settings doesn't match the one in Stripe")
        return HttpResponse(content=f"Invalid signature: {str(e)}", status=400)
    except Exception as e:
        # Other errors
        logger.error(f"Webhook error: {str(e)}")
        return HttpResponse(content=f"Webhook error: {str(e)}", status=400)

    # Get the type of event
    event_type = event['type']
    logger.info(f"Webhook received: {event_type}")

    # Handle the event based on its type
    try:
        if event_type == 'payment_intent.succeeded':
            payment_intent = event['data']['object']
            logger.info(f"Payment intent succeeded: {payment_intent['id']}")

            # Get the order ID from metadata
            metadata = payment_intent.get('metadata', {})
            order_id = metadata.get('order_id')

            if not order_id:
                logger.warning(f"No order_id found in payment intent metadata. This is expected for test payments.")
                # For test payments without order_id, just acknowledge receipt
                return HttpResponse(content=f"Test webhook received: {event_type}", status=200)

            handle_successful_payment(payment_intent)
        elif event_type == 'payment_intent.payment_failed':
            payment_intent = event['data']['object']
            logger.info(f"Payment intent failed: {payment_intent['id']}")
            handle_failed_payment(payment_intent)
        elif event_type == 'charge.refunded':
            charge = event['data']['object']
            logger.info(f"Charge refunded: {charge['id']}")
            handle_refund(charge)
        else:
            logger.info(f"Unhandled event type: {event_type}")

        # Return a 200 response to acknowledge receipt of the event
        return HttpResponse(content=f"Webhook received: {event_type}", status=200)
    except Exception as e:
        logger.error(f"Error processing webhook {event_type}: {str(e)}")
        return HttpResponse(content=f"Error processing webhook: {str(e)}", status=400)


def handle_successful_payment(payment_intent):
    """Handle successful payment intent"""
    # Get the order from the metadata
    metadata = payment_intent.get('metadata', {})
    order_id = metadata.get('order_id')

    if not order_id:
        logger.error("No order_id found in payment intent metadata")
        return HttpResponse(content="No order_id found in payment intent metadata", status=400)

    logger.info(f"Processing successful payment for order {order_id}")

    try:
        order = Order.objects.get(id=order_id)
        order.stripe_pid = payment_intent['id']
        order.status = 'COMPLETED'
        order.payment_status = 'paid'  # Set payment status to trigger ebook delivery

        # Set the webhook trigger flag
        order._webhook_triggered = True

        # Save the order to trigger the signal
        order.save()

        # Don't send confirmation email directly - let the signal handler do it
        # based on the combine_order_emails setting
        logger.info(f"Order {order_id} saved with webhook_triggered flag - signal handler will send email")

        logger.info(f"Payment for order {order_id} processed successfully")
        return HttpResponse(status=200)
    except Order.DoesNotExist:
        logger.error(f"Order {order_id} not found")
        return HttpResponse(content=f"Order {order_id} not found", status=404)
    except Exception as e:
        logger.error(f"Error processing payment for order {order_id}: {str(e)}")
        return HttpResponse(content=f"Error processing payment: {str(e)}", status=500)


def handle_failed_payment(payment_intent):
    """Handle failed payment intent"""
    metadata = payment_intent.get('metadata', {})
    order_id = metadata.get('order_id')

    if not order_id:
        logger.error("No order_id found in payment intent metadata")
        return HttpResponse(content="No order_id found in payment intent metadata", status=400)

    try:
        order = Order.objects.get(id=order_id)
        order.status = 'FAILED'
        order.save()
        logger.info(f"Order {order_id} marked as failed")
        return HttpResponse(status=200)
    except Order.DoesNotExist:
        logger.error(f"Order {order_id} not found")
        return HttpResponse(content=f"Order {order_id} not found", status=404)
    except Exception as e:
        logger.error(f"Error handling failed payment for order {order_id}: {str(e)}")
        return HttpResponse(content=f"Error handling failed payment: {str(e)}", status=500)


def handle_refund(charge):
    """Handle refund event"""
    # Get the order using the payment intent ID
    payment_intent_id = charge.get('payment_intent')

    if not payment_intent_id:
        logger.error("No payment_intent found in charge")
        return HttpResponse(content="No payment_intent found in charge", status=400)

    try:
        order = Order.objects.get(stripe_pid=payment_intent_id)
        order.status = 'REFUNDED'
        order.save()
        logger.info(f"Order with payment intent {payment_intent_id} marked as refunded")
        return HttpResponse(status=200)
    except Order.DoesNotExist:
        logger.error(f"Order with payment intent {payment_intent_id} not found")
        return HttpResponse(content=f"Order with payment intent {payment_intent_id} not found", status=404)
    except Exception as e:
        logger.error(f"Error handling refund for payment intent {payment_intent_id}: {str(e)}")
        return HttpResponse(content=f"Error handling refund: {str(e)}", status=500)
