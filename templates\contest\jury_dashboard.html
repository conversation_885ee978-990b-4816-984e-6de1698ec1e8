{% extends "contest/base.html" %}
{% load static %}

{% block contest_title %}Jury Dashboard - {{ contest.name }}{% endblock %}

{% block contest_content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="card-title mb-0">Jury Dashboard</h2>
                <a href="{% url 'contest:contest_detail' slug=contest.slug %}" class="btn btn-light btn-sm">Back to Contest</a>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <p><strong>Jury Instructions:</strong></p>
                    <ul>
                        <li>As a jury member, you can award up to 100 points to each submission.</li>
                        <li>You should evaluate submissions based on originality, technical skill, and relevance to the book.</li>
                        <li>The final winner will be determined by combining public votes and jury scores.</li>
                    </ul>
                </div>

                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h3>Filter by Category</h3>
                    <div class="btn-group">
                        <a href="{% url 'contest:jury_dashboard' slug=contest.slug %}" class="btn btn-outline-primary {% if not current_category %}active{% endif %}">All Categories</a>
                        {% for category in categories %}
                            <a href="?category={{ category.name }}" class="btn btn-outline-primary {% if current_category == category.name %}active{% endif %}">{{ category.name }}</a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-warning">
                <h3 class="card-title mb-0">Submissions Awaiting Your Score</h3>
            </div>
            <div class="card-body">
                {% if unscored_submissions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Artist</th>
                                    <th>Category</th>
                                    <th>Submitted</th>
                                    <th>Public Votes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for submission in unscored_submissions %}
                                    <tr>
                                        <td>{{ submission.title }}</td>
                                        <td>{{ submission.user.username }}</td>
                                        <td>{{ submission.category.name }}</td>
                                        <td>{{ submission.submitted_at|date:"M d, Y" }}</td>
                                        <td>{{ submission.total_likes }}</td>
                                        <td>
                                            <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-sm btn-primary">View</a>
                                            <a href="{% url 'contest:jury_score_submission' slug=submission.slug %}" class="btn btn-sm btn-success">Score</a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-success">
                        You have scored all submissions in this category!
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3 class="card-title mb-0">Submissions You've Scored</h3>
            </div>
            <div class="card-body">
                {% if scored_submissions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Artist</th>
                                    <th>Category</th>
                                    <th>Your Score</th>
                                    <th>Public Votes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for submission in scored_submissions %}
                                    <tr>
                                        <td>{{ submission.title }}</td>
                                        <td>{{ submission.user.username }}</td>
                                        <td>{{ submission.category.name }}</td>
                                        <td>
                                            {% for score in submission.jury_scores.all %}
                                                {% if score.judge == user %}
                                                    {{ score.points }}/100
                                                {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td>{{ submission.total_likes }}</td>
                                        <td>
                                            <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-sm btn-primary">View</a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="alert alert-info">
                        You haven't scored any submissions yet.
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
