/* Audio Player Styles - Modern & Futuristic Theme */
.audio-player-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    color: #fff;
}

/* Header Section with Logo */
.player-header {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.site-logo {
    flex: 0 0 auto;
    margin-right: 1.5rem;
}

.logo-image {
    max-height: 50px;
    width: auto;
}

.header-content {
    flex: 1;
}

.player-header h2 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 600;
}

.player-header .author {
    font-size: 1.2rem;
    opacity: 0.8;
    margin-bottom: 1rem;
    color: #ccc;
}

/* Cover Image Section */
.cover-image-section {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.main-cover-image {
    width: 250px;
    height: 250px;
    object-fit: cover;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    padding: 3px;
    background-color: rgba(255, 255, 255, 0.05);
}

.main-cover-image:hover {
    transform: scale(1.02);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.5);
}

@media (min-width: 768px) {
    .main-cover-image {
        width: 300px;
        height: 300px;
    }
}

/* Main Player Section */
.player-main {
    display: flex;
    flex-direction: column-reverse;
    gap: 1.5rem;
}

@media (min-width: 992px) {
    .player-main {
        flex-direction: row;
    }
}

/* Chapters Panel */
.chapters-panel {
    flex: 1;
    max-width: 100%;
}

@media (min-width: 992px) {
    .chapters-panel {
        max-width: 350px;
    }
}

.chapters-panel .card {
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    background-color: rgba(30, 30, 40, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chapters-panel .card-header {
    background-color: rgba(40, 40, 50, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1rem;
}

.chapters-panel .card-header h4 {
    color: #fff;
    font-weight: 500;
}

.chapter-list {
    max-height: 400px;
    overflow-y: auto;
    scrollbar-width: thin;
}

.chapter-list::-webkit-scrollbar {
    width: 6px;
}

.chapter-list::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.chapter-item {
    cursor: pointer;
    padding: 12px 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    position: relative;
}

.chapter-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.chapter-item.active {
    background-color: rgba(40, 167, 69, 0.2);
    border-left: 4px solid #28a745;
}

.chapter-cover {
    width: 40px;
    height: 40px;
    object-fit: cover;
    margin-right: 12px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.chapter-info {
    flex: 1;
}

.chapter-title {
    font-weight: 500;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #fff;
}

.chapter-number {
    font-size: 0.8rem;
    opacity: 0.7;
    color: #ccc;
}

/* Offline indicator for chapter list */
.offline-indicator {
    position: absolute;
    top: 8px;
    right: 10px;
    color: #28a745;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.offline-indicator i {
    filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.5));
}

/* Player Panel */
.player-panel {
    flex: 2;
}

.player-panel .card {
    height: 100%;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    background-color: rgba(30, 30, 40, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-panel .card-body {
    padding: 1.5rem;
}

.current-chapter-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 1.5rem;
}

.current-chapter-text {
    flex: 1;
    width: 100%;
}

.current-chapter-title {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #fff;
    font-weight: 500;
}

.current-part-title {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 0.5rem;
    color: #ccc;
}

/* Audio Controls */
.custom-audio-player {
    width: 100%;
    background-color: rgba(20, 20, 30, 0.7);
    border-radius: 12px;
    padding: 20px;
    margin-top: 1rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Progress Bar - Enhanced for mobile */
.progress-container {
    width: 100%;
    height: 8px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 10px;
    position: relative;
    overflow: hidden;
}

/* Make progress bar larger on mobile for better touch targets */
@media (max-width: 768px) {
    .progress-container {
        height: 16px;
        border-radius: 8px;
        margin: 10px 0 15px 0;
    }
    
    /* Add visible dot indicator for touch point */
    .progress-container::after {
        content: '';
        position: absolute;
        top: 50%;
        left: var(--progress-position, 0%);
        transform: translate(-50%, -50%);
        width: 20px;
        height: 20px;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.2s ease;
        pointer-events: none;
        z-index: 5;
    }
    
    .progress-container:active::after {
        opacity: 1;
    }
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 4px;
    width: 0;
    transition: width 0.1s linear;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    margin-bottom: 15px;
    color: #ccc;
}

.controls-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    position: relative;
    margin-bottom: 15px;
}

.main-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin: 0 auto;
}

.control-button {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 1.2rem;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    background-color: rgba(255, 255, 255, 0.1);
}

.control-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.play-pause-button {
    background: linear-gradient(135deg, #28a745, #20c997);
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

.play-pause-button:hover {
    background: linear-gradient(135deg, #218838, #1ca38b);
    transform: scale(1.05);
}

.speed-control {
    position: relative;
    margin-left: auto;
    margin-top: 10px;
}

@media (min-width: 576px) {
    .speed-control {
        margin-top: 0;
    }
}

.speed-button {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.speed-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.speed-options {
    position: absolute;
    top: -180px;
    right: 0;
    background-color: rgba(30, 30, 40, 0.95);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    padding: 8px 0;
    display: none;
    z-index: 10;
    min-width: 100px;
}

@media (max-width: 576px) {
    .speed-options {
        top: auto;
        bottom: 40px;
        right: 0;
    }
}

.speed-options.show {
    display: block;
}

.speed-option {
    padding: 8px 15px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    text-align: center;
    color: #fff;
}

.speed-option:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.speed-option.active {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

/* Error Message */
.error-message {
    display: none;
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    padding: 10px 15px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: center;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Autoplay Message */
#autoplayMessage {
    background-color: rgba(13, 110, 253, 0.2);
    border-color: rgba(13, 110, 253, 0.3);
    color: #0d6efd;
    border-radius: 8px;
    position: relative;
    padding-right: 30px;
}

.close-message {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
    font-size: 1.2rem;
    color: #0d6efd;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.close-message:hover {
    opacity: 1;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease forwards;
}

/* Back Button */
.btn-secondary, .btn-outline-secondary {
    background-color: rgba(108, 117, 125, 0.2);
    border-color: rgba(108, 117, 125, 0.5);
    color: #fff;
    transition: all 0.2s ease;
}

.btn-secondary:hover, .btn-outline-secondary:hover {
    background-color: rgba(108, 117, 125, 0.4);
    border-color: rgba(108, 117, 125, 0.7);
    transform: translateY(-2px);
}

/* Mobile Optimizations */
@media (max-width: 576px) {
    .player-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .site-logo {
        margin-bottom: 1rem;
        align-self: center;
    }
    
    .header-content {
        text-align: center;
        width: 100%;
    }
    
    .main-cover-image {
        width: 200px;
        height: 200px;
    }
    
    .controls-buttons {
        gap: 5px;
        flex-wrap: wrap;
        justify-content: space-around;
    }
    
    .control-button {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
    
    .play-pause-button {
        width: 50px;
        height: 50px;
        order: 0;
    }
    
    .player-main {
        flex-direction: column;
    }
    
    /* Ensure buttons don't overlap by setting minimum width */
    .main-controls {
        gap: 5px;
        width: 100%;
        justify-content: space-around;
    }
}

/* Mobile layout with controls under image */
@media (max-width: 768px) {
    .player-main {
        gap: 1rem;
    }
    
    .cover-image-section {
        margin-bottom: 1rem;
    }
    
    .player-panel {
        margin-bottom: 1.5rem;
    }
    
    .chapters-panel {
        margin-top: 1rem;
    }
    
    /* Improve control button layout on mobile */
    .controls-buttons {
        padding: 10px 0;
    }
    
    /* Ensure skip buttons don't overlap */
    .control-button[data-action="skip-backward"],
    .control-button[data-action="skip-forward"] {
        margin: 0 3px;
    }
}

/* Offline Mode Status */
.offline-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    flex-wrap: wrap;
}

.offline-badges {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
}

.offline-badge,
.availability-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.offline-badge {
    background-color: rgba(30, 144, 255, 0.2);
    color: #1e90ff;
}

.offline-badge.offline {
    background-color: rgba(128, 128, 128, 0.2);
    color: #808080;
}

.availability-badge {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

/* Download Options */
.download-options .btn-group {
    display: flex;
}

.download-options .btn {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.85rem;
    padding: 8px 5px;
}

/* Download Progress */
.download-progress-container {
    background-color: rgba(30, 30, 40, 0.8);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: opacity 0.5s ease;
}

.download-progress-title {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #fff;
    font-size: 1rem;
    font-weight: 500;
}

/* Offline Downloads Management */
.offline-management .card {
    background-color: rgba(30, 30, 40, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.downloaded-chapters-list {
    max-height: 200px;
    overflow-y: auto;
    border-radius: 6px;
    background-color: rgba(20, 20, 30, 0.5);
    margin-top: 0.5rem;
    scrollbar-width: thin;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.downloaded-part-header {
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 5px;
}

.downloaded-part-header:first-child {
    margin-top: 0;
}

.downloaded-chapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: background-color 0.2s ease;
}

.downloaded-chapter-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.downloaded-chapter-item:last-child {
    border-bottom: none;
}

.downloaded-chapter-item .chapter-info {
    flex: 1;
    overflow: hidden;
}

.downloaded-chapter-item .chapter-title {
    font-size: 0.9rem;
    font-weight: normal;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba(255, 255, 255, 0.9);
}

.downloaded-chapter-item .chapter-meta {
    display: flex;
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 2px;
}

.downloaded-chapter-item .chapter-size {
    background-color: rgba(255, 255, 255, 0.1);
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
}

.downloaded-chapter-item .remove-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.downloaded-chapter-item .remove-btn:hover {
    color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

/* Scrollbar styling for the downloaded chapters list */
.downloaded-chapters-list::-webkit-scrollbar {
    width: 6px;
}

.downloaded-chapters-list::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
}

.downloaded-chapters-list::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

/* Download Section */
.download-section {
    background-color: rgba(20, 20, 30, 0.5);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

#downloadButton {
    transition: all 0.3s ease;
    font-weight: 500;
    letter-spacing: 0.5px;
    border-radius: 8px;
}

#downloadButton:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#downloadButton:active {
    transform: translateY(0);
}

#downloadProgress {
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
    background-color: rgba(255, 255, 255, 0.1);
}

#downloadProgressBar {
    transition: width 0.2s ease;
    font-size: 0.7rem;
    line-height: 10px;
}

#downloadStatus {
    padding: 5px;
    border-radius: 4px;
    font-weight: 500;
}

/* Download dropdown styling */
.download-options .dropdown-menu {
    width: 100%;
    border-radius: 8px;
    padding: 0.5rem 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(40, 40, 50, 0.95);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.download-options .dropdown-item {
    color: rgba(255, 255, 255, 0.9);
    padding: 0.6rem 1rem;
    transition: all 0.2s ease;
}

.download-options .dropdown-item:hover,
.download-options .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.download-options .dropdown-item:active {
    background-color: rgba(40, 167, 69, 0.3);
}

.download-options .dropdown-item i {
    width: 1.2rem;
    text-align: center;
    margin-right: 0.5rem;
}

.download-options .dropdown-divider {
    border-color: rgba(255, 255, 255, 0.1);
    margin: 0.25rem 0;
}

/* Improved mobile layout for download options */
@media (max-width: 576px) {
    .download-options .dropdown-toggle {
        font-size: 1rem;
        padding: 0.6rem 1rem;
    }
    
    .download-options .dropdown-item {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
    }
    
    .download-options .dropdown-item i {
        margin-right: 0.75rem;
    }
    
    /* Fix for the offline management section */
    .offline-management .card-body {
        padding: 0.75rem;
    }
    
    /* Fix for download progress container */
    .download-progress-container {
        padding: 0.5rem;
    }
}

/* Ensure skip buttons don't overlap */
.control-button[data-action="skip-backward"],
.control-button[data-action="skip-forward"] {
    margin: 0 3px;
}

@media (max-width: 576px) {
    .control-button[data-action="skip-backward"],
    .control-button[data-action="skip-forward"] {
        margin: 0 2px;
        padding: 0;
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }
    
    /* Hide the text on very small screens */
    @media (max-width: 375px) {
        .control-button[data-action="skip-backward"] span,
        .control-button[data-action="skip-forward"] span {
            display: none;
        }
    }
} 