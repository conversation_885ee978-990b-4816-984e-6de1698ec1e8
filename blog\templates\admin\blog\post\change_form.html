{% extends "admin/change_form.html" %}
{% load i18n admin_urls static %}

{% block extrahead %}
{{ block.super }}
<style>
  #image-snippets-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 5px;
  }
  .snippet-box {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #000;
  }
  .snippet-header {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #000;
  }
  .snippet-preview {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
  }
  .snippet-preview img {
    max-height: 100px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .snippet-code {
    margin-bottom: 10px;
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 4px;
  }
  .snippet-code textarea {
    width: 100%;
    height: 80px;
    font-family: monospace;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #212529;
    color: #f8f9fa;
  }
  .snippet-info {
    background-color: #fff;
    color: #212529;
    padding: 10px;
    border-radius: 4px;
  }
  .copy-btn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    cursor: pointer;
  }
  .accordion-header {
    background-color: #e9ecef;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 10px;
    font-weight: bold;
    color: #000;
  }
  .accordion-content {
    display: none;
    padding: 0 15px;
    color: #000;
  }
</style>
{% endblock %}

{% block after_field_sets %}
{{ block.super }}

{% if image_snippets %}
<div class="module aligned">
  <h2>Post Image HTML Snippets</h2>
  <div class="accordion-header" onclick="toggleAccordion(this)">
    ▶ Click to show/hide image HTML snippets ({{ image_snippets|length }} available)
  </div>
  <div class="accordion-content" id="image-snippets-container">
    <p class="help">Copy these HTML snippets to embed images in your post content:</p>
    
    {% for snippet in image_snippets %}
    <div class="snippet-box">
      <div class="snippet-header">
        <h3>Image #{{ snippet.id }}</h3>
        <button type="button" class="copy-btn" onclick="copySnippet('{{ snippet.id }}')">Copy HTML</button>
      </div>
      <div class="snippet-preview">
        <div class="preview-image">
          {{ snippet.html|safe }}
        </div>
        <div class="snippet-info">
          <p>Paste this HTML where you want the image to appear in your post.</p>
        </div>
      </div>
      <div class="snippet-code">
        <textarea id="snippet-{{ snippet.id }}" readonly>{{ snippet.html }}</textarea>
      </div>
    </div>
    {% endfor %}
  </div>
</div>
{% endif %}
{% endblock %}

{% block admin_change_form_document_ready %}
{{ block.super }}
<script>
function copySnippet(id) {
  const textarea = document.getElementById('snippet-' + id);
  textarea.select();
  document.execCommand('copy');
  
  // Show feedback
  const btn = event.target;
  const originalText = btn.textContent;
  btn.textContent = 'Copied!';
  btn.style.backgroundColor = '#007bff';
  
  setTimeout(() => {
    btn.textContent = originalText;
    btn.style.backgroundColor = '#28a745';
  }, 1500);
}

function toggleAccordion(element) {
  const content = element.nextElementSibling;
  if (content.style.display === "block") {
    content.style.display = "none";
    element.innerHTML = element.innerHTML.replace('▼', '▶');
  } else {
    content.style.display = "block";
    element.innerHTML = element.innerHTML.replace('▶', '▼');
  }
}
</script>
{% endblock %}

{% block footer %}
{{ block.super }}
<script type="text/javascript">
    window.DJANGO_TAGS = {
        existing: {{ existing_tags|default:"[]"|safe }},
        current: {{ current_tags|default:"[]"|safe }}
    };
</script>
{% endblock %}