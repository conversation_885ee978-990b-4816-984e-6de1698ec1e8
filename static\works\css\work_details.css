/* Work Details Page Styling */

/* <PERSON><PERSON> Styling for Work Detail Page */
#productDetailPage .btn {
    padding: 0.5rem 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
}

#productDetailPage .btn-outline-success {
    color: #28a745;
    border-color: #28a745;
    background-color: rgba(0, 0, 0, 0.5);
}

#productDetailPage .btn-outline-success:hover {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

#productDetailPage .btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: rgba(0, 0, 0, 0.5);
}

#productDetailPage .btn-outline-secondary:hover {
    color: #fff;
    background-color: #6c757d;
    border-color: #6c757d;
}

#productDetailPage .btn-success {
    color: #fff;
    background-color: #28a745;
    border-color: #28a745;
}

#productDetailPage .btn-success:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

#productDetailPage .btn-primary {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

#productDetailPage .btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Card Styling */
#productDetailPage .card {
    background-color: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

#productDetailPage .card-body {
    padding: 1.5rem;
}

#productDetailPage .card-title {
    color: #fff;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

/* Purchase Card */
#productDetailPage .purchase-card {
    border-left: 4px solid #007bff;
}

/* Content Access Card */
#productDetailPage .content-access-card {
    border-left: 4px solid #28a745;
}

/* Tab content button styling */
#productDetailPage .tab-content .btn {
    padding: 0.5rem 1rem;
    line-height: 1.5;
    border-radius: 0.25rem;
}

/* Nav tabs styling */
#productDetailPage .nav-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-left: 0;
}

#productDetailPage .nav-tabs .nav-link {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-right: 2px;
    border-bottom: none;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease;
}

#productDetailPage .nav-tabs .nav-link:first-child {
    margin-left: 0;
}

#productDetailPage .nav-tabs .nav-item:first-child .nav-link {
    border-left: 1px solid rgba(255, 255, 255, 0.2);
}

#productDetailPage .card-header {
    background-color: rgba(0, 0, 0, 0.5);
    border-bottom: none;
    padding: 0;
    padding-left: 0.5rem;
}

#productDetailPage .nav-tabs .nav-link.active {
    color: #212529;
    background-color: #f8f9fa;
    border-color: #dee2e6 #dee2e6 #f8f9fa;
    font-weight: 500;
}

#productDetailPage .nav-tabs .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.3) transparent;
}

#productDetailPage .tab-content {
    background-color: #f8f9fa;
    color: #212529;
    border-bottom-left-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
    padding: 1.5rem;
}

/* Text Styling */
#productDetailPage .text-muted {
    color: #6c757d !important;
    font-style: italic;
}

/* Form row styling */
#productDetailPage .form-row .d-flex {
    align-items: center;
}

/* Text color */
#productDetailPage .text-white {
    color: #fff !important;
}

/* Background for buttons */
#productDetailPage .btn-outline-success,
#productDetailPage .btn-outline-secondary,
#productDetailPage .btn-outline-primary {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Responsive adjustments */
@media (max-width: 576px) {
    #productDetailPage .btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    #productDetailPage .card-body {
        padding: 1rem;
    }

    #productDetailPage .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
}

/* Alert styling */
#productDetailPage .alert {
    border-radius: 0.25rem;
    padding: 0.75rem 1.25rem;
    margin-bottom: 1rem;
}

#productDetailPage .alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

/* Navigation Card */
#productDetailPage .navigation-card {
    border-left: 4px solid #6c757d;
}

#productDetailPage .btn-outline-primary {
    color: #007bff;
    border-color: #007bff;
    background-color: rgba(0, 0, 0, 0.5);
}

#productDetailPage .btn-outline-primary:hover {
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

/* Review card styling */
#productDetailPage .review-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 0.5rem;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#productDetailPage .review-card .rating {
    margin-left: 1rem;
}

#productDetailPage .nav-profile-image {
    width: 40px;
    height: 40px;
    margin-right: 0.75rem;
}

/* Improve tab content styling */
#productDetailPage .tab-content p {
    margin-bottom: 1rem;
    line-height: 1.6;
}

/* Contest review styling */
.contest-review {
    margin-bottom: 1rem;
}

.contest-review blockquote {
    background-color: rgba(0, 0, 0, 0.05);
    border-left: 3px solid #007bff;
    padding: 0.75rem 1rem;
    margin: 1rem 0;
    font-size: 0.95rem;
    font-style: italic;
}

.contest-review .text-end p {
    margin-bottom: 0.25rem;
    line-height: 1.2;
    color: #000;
}