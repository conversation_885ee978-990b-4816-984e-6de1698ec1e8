from django.shortcuts import render, redirect, get_object_or_404
from django.contrib import messages
from django.views.generic import CreateView, Template<PERSON><PERSON><PERSON>, DetailView
from django.urls import reverse_lazy
from django.contrib.admin.views.decorators import staff_member_required
from django.utils.decorators import method_decorator
from django.http import HttpResponse, Http404, JsonResponse
from django.views.decorators.http import require_GET
from django.utils import timezone
from .forms import NewsletterSubscriptionForm
from .models import Subscriber, Newsletter, EmailTracking
from django.conf import settings
from django.template.loader import render_to_string
from base64 import b64decode
from utils.rate_limit import rate_limit

# Import email manager utilities
from email_manager.utils import (
    send_email_with_rate_limit,
    should_send_newsletter_confirmation
)


@method_decorator(rate_limit('subscribe', limit=3, period=3600), name='dispatch')
class SubscribeView(CreateView):
    template_name = 'newsletter/subscribe.html'
    form_class = NewsletterSubscriptionForm
    success_url = reverse_lazy('newsletter:subscribe_success')

    def form_valid(self, form):
        subscriber = form.save(commit=False)
        subscriber.save()

        # Only send confirmation email if configured to do so
        if should_send_newsletter_confirmation():
            # Send confirmation email
            context = {
                'confirmation_url': self.request.build_absolute_uri(
                    reverse_lazy('newsletter:confirm_subscription',
                               kwargs={'token': subscriber.confirmation_token})
                ),
                'subscriber_name': subscriber.name,
                'unsubscribe_url': self.request.build_absolute_uri(
                    reverse_lazy('newsletter:unsubscribe',
                               kwargs={'token': subscriber.confirmation_token})
                )
            }

            html_message = render_to_string('newsletter/email/confirmation.html', context)
            text_message = render_to_string('newsletter/email/confirmation.txt', context)

            # Use rate-limited email sending
            send_email_with_rate_limit(
                subject='Confirm your newsletter subscription',
                recipient=subscriber.email,
                html_content=html_message,
                text_content=text_message,
                priority=2,  # Important priority
                content_type='newsletter_confirmation',
                object_id=subscriber.id
            )

        messages.success(
            self.request,
            "Thanks for subscribing! Please check your email to confirm your subscription."
        )
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(
            self.request,
            "There was an error with your submission. Please check the form and try again."
        )
        return super().form_invalid(form)


class SubscribeSuccessView(TemplateView):
    template_name = 'newsletter/subscribe_success.html'


@require_GET
@rate_limit('unsubscribe', limit=5, period=3600)
def unsubscribe(request, token):
    subscriber = get_object_or_404(Subscriber, confirmation_token=token)
    subscriber.unsubscribe()
    messages.success(request, "You have been successfully unsubscribed from our newsletter.")
    return render(request, 'newsletter/unsubscribe_success.html', {'subscriber': subscriber})


@rate_limit('confirm_subscription', limit=5, period=3600)
def confirm_subscription(request, token):
    try:
        subscriber = Subscriber.objects.get(confirmation_token=token, is_confirmed=False)
        subscriber.confirm_subscription()
        messages.success(request, "Your subscription has been confirmed. Thank you!")
    except Subscriber.DoesNotExist:
        messages.error(request, "Invalid or expired confirmation link.")

    return redirect('home')


@method_decorator(staff_member_required, name='dispatch')
class NewsletterPreviewView(DetailView):
    model = Newsletter
    template_name = 'newsletter/email/newsletter_template.html'
    context_object_name = 'newsletter'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['preview'] = True
        context['subscriber'] = Subscriber(
            name="John Doe",
            email="<EMAIL>"
        )
        return context


@require_GET
@rate_limit('track_open', limit=10, period=60)
def track_open(request, token):
    try:
        tracking = EmailTracking.objects.get(tracking_token=token)
        tracking.mark_opened()
    except EmailTracking.DoesNotExist:
        raise Http404("Tracking not found")

    # Return a 1x1 transparent GIF
    return HttpResponse(
        b64decode('R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7'),
        content_type='image/gif'
    )


@require_GET
@rate_limit('track_click', limit=10, period=60)
def track_click(request, token, link_id):
    try:
        tracking = EmailTracking.objects.get(tracking_token=token)
        tracking.mark_clicked()
        # Decode and redirect to the original URL
        original_url = b64decode(link_id).decode('utf-8')
        return redirect(original_url)
    except (EmailTracking.DoesNotExist, Exception):
        raise Http404("Link not found")


def debug_scheduled_newsletters(request):
    if not request.user.is_superuser:
        return JsonResponse({'error': 'Not authorized'}, status=403)

    now = timezone.now()
    scheduled = Newsletter.objects.filter(status='scheduled')

    data = {
        'current_time': now.isoformat(),
        'newsletters': [{
            'id': n.id,
            'subject': n.subject,
            'scheduled_for': n.scheduled_for.isoformat() if n.scheduled_for else None,
            'should_send': bool(n.scheduled_for and n.scheduled_for <= now)
        } for n in scheduled]
    }

    return JsonResponse(data)
