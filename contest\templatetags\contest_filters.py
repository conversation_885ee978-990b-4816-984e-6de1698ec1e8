from django import template
from django.template.defaultfilters import stringfilter
import re

register = template.Library()

@register.filter
@stringfilter
def split(value, arg):
    """
    Split a string by the given delimiter and return the list.
    Usage: {{ value|split:"delimiter" }}
    """
    return value.split(arg)

@register.filter
def extract_sketchfab_id(url):
    """
    Extract the Sketchfab model ID from a URL.

    Handles various URL formats:
    - https://sketchfab.com/3d-models/angel-wings-a575b36467d840be84d8f91d5074d7d4
    - https://sketchfab.com/models/a575b36467d840be84d8f91d5074d7d4/embed
    - https://skfb.ly/pu7Pw (not supported, returns None)

    Returns the model ID or None if not found.
    """
    # Pattern for regular URL format
    pattern1 = r'sketchfab\.com/3d-models/[^/]+-([a-f0-9]{32})'
    # Pattern for embed URL format
    pattern2 = r'sketchfab\.com/models/([a-f0-9]{32})'

    # Try regular URL format
    match = re.search(pattern1, url)
    if match:
        return match.group(1)

    # Try embed URL format
    match = re.search(pattern2, url)
    if match:
        return match.group(1)

    # No match found
    return None
