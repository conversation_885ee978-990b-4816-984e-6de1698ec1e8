{% extends "contest/base.html" %}
{% load static %}

{% block extra_css %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'contest/css/tempus-view.css' %}">
{% endblock %}

{% block contest_title %}
    {% if current_category %}
        {{ current_category }} Submissions
    {% else %}
        All Submissions
    {% endif %}
{% endblock %}

{% block contest_content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title">
                    {% if current_category %}
                        {{ current_category }} Submissions for {{ contest.name }}
                    {% else %}
                        All Submissions for {{ contest.name }}
                    {% endif %}
                </h2>
                <div class="mb-3">
                    <form method="GET" class="row g-3">
                        <div class="col-md-4 col-12 mb-2">
                            <div class="input-group">
                                <input type="text" name="search" class="form-control" placeholder="Search submissions..." value="{{ search_query }}">
                                <button class="btn btn-outline-primary" type="submit">Search</button>
                            </div>
                        </div>

                        <div class="col-md-8 col-12 text-md-end text-center">
                            <div class="btn-group flex-wrap">
                                <a href="{% url 'contest:submission_gallery' slug=contest.slug %}" class="btn btn-outline-primary {% if not current_category %}active{% endif %}">All Categories</a>
                                {% for category in categories %}
                                    <a href="{% url 'contest:submission_gallery' slug=contest.slug %}?category={{ category.name }}" class="btn btn-outline-primary {% if current_category == category.name %}active{% endif %}">{{ category.name }}</a>
                                {% endfor %}
                            </div>
                        </div>
                    </form>
                </div>

                <div class="row align-items-center mb-3">
                    <div class="col-md-4 col-12 mb-2">
                        <div class="d-flex flex-column flex-md-row gap-2">
                            <div class="dropdown flex-grow-1">
                                <button class="btn btn-outline-secondary dropdown-toggle w-100 text-truncate" type="button" id="bookPartDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    {% if current_book_part %}{{ current_book_part }}{% else %}All Book Parts{% endif %}
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="bookPartDropdown">
                                    <li><a class="dropdown-item {% if not current_book_part %}active{% endif %}" href="?{% if sort_by %}sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}">All Book Parts</a></li>
                                    {% for book_part in book_parts %}
                                        <li><a class="dropdown-item {% if current_book_part == book_part.name %}active{% endif %}" href="?book_part={{ book_part.id }}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}">{{ book_part.name }}</a></li>
                                    {% endfor %}
                                </ul>
                            </div>

                            <div class="dropdown">
                                <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="aiFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    {% if ai_filter == 'yes' %}AI-Assisted Only{% elif ai_filter == 'no' %}Non-AI Only{% else %}AI Filter{% endif %}
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="aiFilterDropdown">
                                    <li><a class="dropdown-item {% if not ai_filter %}active{% endif %}" href="?{% if sort_by %}sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">All Submissions</a></li>
                                    <li><a class="dropdown-item {% if ai_filter == 'yes' %}active{% endif %}" href="?ai_used=yes{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">AI-Assisted Only</a></li>
                                    <li><a class="dropdown-item {% if ai_filter == 'no' %}active{% endif %}" href="?ai_used=no{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}">Non-AI Only</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8 col-12">
                        <div class="d-flex flex-wrap justify-content-md-end justify-content-center align-items-center">
                            <div class="btn-group me-md-2 mb-2 mb-md-0">
                                <a href="?sort=likes{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}" class="btn btn-outline-secondary {% if sort_by == 'likes' %}active{% endif %}">
                                    <i class="fas fa-thumbs-up"></i> Most Liked
                                </a>
                                <a href="?sort=date{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}" class="btn btn-outline-secondary {% if sort_by == 'date' %}active{% endif %}">
                                    <i class="fas fa-calendar-alt"></i> Newest
                                </a>
                            </div>
                            <div class="ms-md-2 tempus-view-btn-container">
                                <button id="tempus-view-btn" class="btn tempus-view-btn text-white" title="Enhanced gallery view with zoom and slideshow">
                                    <i class="fas fa-expand"></i> TEMPUS View
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% if page_obj %}
    <div class="row">
        {% for submission in page_obj %}
            <div class="col-md-4 mb-4">
                <div class="card h-100 submission-card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-start">
                            <h5 class="card-title mb-0" title="{{ submission.title }}">{{ submission.title }}</h5>
                            <span class="badge bg-primary rounded-pill ms-1 flex-shrink-0">
                                <i class="fas fa-thumbs-up"></i> {{ submission.total_likes }}
                            </span>
                        </div>
                    </div>

                    <div class="submission-image position-relative">
                        {% if submission.file.url|lower|slice:"-4:" == '.jpg' or submission.file.url|lower|slice:"-5:" == '.jpeg' or submission.file.url|lower|slice:"-4:" == '.png' %}
                            <img src="{{ submission.file.url }}" class="card-img-top" alt="{{ submission.title }}" style="height: 250px; object-fit: cover;">
                        {% elif submission.file.url|lower|slice:"-4:" == '.obj' or submission.file.url|lower|slice:"-5:" == '.gltf' %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                <div class="text-center">
                                    <i class="fas fa-cube fa-3x text-muted"></i>
                                    <p class="mt-2">3D Model</p>
                                </div>
                            </div>
                        {% else %}
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 250px;">
                                <i class="fas fa-file-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}

                        <div class="category-badge position-absolute top-0 end-0 m-2">
                            <span class="badge bg-secondary">{{ submission.category.name }}</span>
                            {% if submission.ai_tools_used %}
                            <span class="badge bg-info d-block mt-1" title="AI tools were used in creating this artwork">AI-Assisted</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center mb-2">
                            <div class="rating me-md-2 mb-1 mb-md-0">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= submission.rating %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <small class="text-muted">{{ submission.book_part.name }}</small>
                        </div>

                        <p class="card-text">{{ submission.description|truncatewords:15 }}</p>

                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                By: {{ submission.user.username }}<br>
                                {{ submission.submitted_at|date:"M d, Y" }}
                            </small>

                            <div class="comment-count">
                                <i class="fas fa-comment text-muted"></i>
                                <span class="text-muted">{{ submission.comments.count }}</span>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer bg-transparent text-center">
                        <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Details
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="row">
            <div class="col-12">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if sort_by %}&sort={{ sort_by }}{% endif %}{% if current_category %}&category={{ current_category }}{% endif %}{% if current_book_part_id %}&book_part={{ current_book_part_id }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if ai_filter %}&ai_used={{ ai_filter }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
    {% endif %}
{% else %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                No submissions found. Be the first to submit your artwork!
                <a href="{% url 'contest:submission_create' slug=contest.slug %}" class="alert-link">Submit now</a>
            </div>
        </div>
    </div>
{% endif %}

<!-- TEMPUS View Container -->
<div id="tempus-view-container" class="tempus-view d-none">
    <div class="tempus-view-image-container">
        <img id="tempus-view-image" class="tempus-view-image" src="" alt="">
        <div id="tempus-view-close" class="tempus-view-close">
            <i class="fas fa-times"></i>
        </div>
        <div id="tempus-view-prev" class="tempus-view-nav tempus-view-prev">
            <i class="fas fa-chevron-left"></i>
        </div>
        <div id="tempus-view-next" class="tempus-view-nav tempus-view-next">
            <i class="fas fa-chevron-right"></i>
        </div>

        <!-- Controls bar -->
        <div class="tempus-view-controls">
            <button id="tempus-view-slideshow" class="tempus-view-control" title="Start Slideshow">
                <i class="fas fa-play"></i>
            </button>
            <button id="tempus-view-zoom" class="tempus-view-control" title="Zoom Image">
                <i class="fas fa-search-plus"></i>
            </button>
            <button id="tempus-view-fullscreen" class="tempus-view-control" title="Fullscreen Mode">
                <i class="fas fa-expand-arrows-alt"></i>
            </button>
        </div>
    </div>

    <!-- Bottom details bar -->
    <div id="tempus-view-details" class="tempus-view-details">
        <div class="tempus-view-details-left">
            <div id="tempus-view-title" class="tempus-view-title"></div>
            <div id="tempus-view-artist" class="tempus-view-artist"></div>
        </div>

        <div class="tempus-view-details-center">
            <span id="tempus-view-category" class="tempus-view-category"></span>
            <span class="tempus-view-likes">
                <i class="fas fa-thumbs-up"></i>
                <span id="tempus-view-likes"></span>
            </span>
        </div>

        <div class="tempus-view-details-right">
            <a id="tempus-view-details-link" href="#" class="tempus-view-details-link">
                <i class="fas fa-info-circle"></i> View Full Details
            </a>
        </div>
    </div>
</div>

{% block extra_js %}
{{ block.super }}
<script src="{% static 'contest/js/tempus-view-v2.js' %}"></script>
{% endblock %}
{% endblock %}