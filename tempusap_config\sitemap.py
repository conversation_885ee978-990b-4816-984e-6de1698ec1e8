from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from blog.models import Post
from works.models import Product


class StaticViewSitemap(Sitemap):
    """Sitemap for static pages"""
    priority = 0.8
    changefreq = 'weekly'

    def items(self):
        return ['home', 'works', 'blog', 'about']

    def location(self, item):
        return reverse(item)


class BlogSitemap(Sitemap):
    """Sitemap for blog posts"""
    priority = 0.7
    changefreq = 'weekly'

    def items(self):
        return Post.objects.filter(post_status=1)  # Only published posts

    def lastmod(self, obj):
        return obj.post_updated_at

    def location(self, obj):
        return obj.get_absolute_url()


class WorksSitemap(Sitemap):
    """Sitemap for works/products"""
    priority = 0.7
    changefreq = 'weekly'

    def items(self):
        return Product.objects.all()  # All products

    def lastmod(self, obj):
        return obj.updated_date if hasattr(obj, 'updated_date') else None

    def location(self, obj):
        return obj.get_absolute_url()


# Combine all sitemaps
sitemaps = {
    'static': StaticViewSitemap,
    'blog': BlogSitemap,
    'works': WorksSitemap,
} 