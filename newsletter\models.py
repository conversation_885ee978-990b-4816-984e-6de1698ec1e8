from django.db import models
import uuid
from django.utils import timezone
from django.template import Template, Context
from django.conf import settings
from django.template.loader import render_to_string
from django.urls import reverse

# Import email manager utilities
from email_manager.utils import send_email_with_rate_limit


class Subscriber(models.Model):
    email = models.EmailField(unique=True)
    name = models.CharField(max_length=100)
    confirmation_token = models.UUIDField(default=uuid.uuid4, editable=False)
    is_confirmed = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    confirmed_at = models.DateTimeField(null=True, blank=True)
    unsubscribed_at = models.DateTimeField(null=True, blank=True)
    last_email_sent_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.email})"

    def confirm_subscription(self):
        self.is_confirmed = True
        self.confirmed_at = timezone.now()
        self.save()

    def unsubscribe(self):
        self.is_active = False
        self.unsubscribed_at = timezone.now()
        self.save()


class NewsletterTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    subject_template = models.CharField(max_length=200, help_text="You can use {{ subscriber.name }} etc.")
    content_template = models.TextField(help_text="HTML content with template variables")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def create_newsletter(self):
        """Create a new newsletter from this template"""
        return Newsletter.objects.create(
            subject=self.subject_template,
            content=self.content_template,
            status='draft'
        )


class Newsletter(models.Model):
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('scheduled', 'Scheduled'),
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('error', 'Error'),
    ]

    subject = models.CharField(max_length=200)
    content = models.TextField(help_text="You can use {{ subscriber.name }} and {{ subscriber.email }} in your content.")
    preview_text = models.CharField(max_length=200, blank=True, help_text="Short summary that appears in email clients")
    created_at = models.DateTimeField(auto_now_add=True)
    scheduled_for = models.DateTimeField(null=True, blank=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    error_message = models.TextField(blank=True)
    track_opens = models.BooleanField(default=True)
    track_clicks = models.BooleanField(default=True)
    sent_to_count = models.IntegerField(default=0)
    open_count = models.IntegerField(default=0)
    click_count = models.IntegerField(default=0)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return self.subject

    def get_absolute_url(self):
        return reverse('newsletter:preview', args=[self.pk])

    def get_preview_html(self, subscriber=None):
        if not subscriber:
            subscriber = Subscriber(name="John Doe", email="<EMAIL>")

        context = {
            'newsletter': self,
            'subscriber': subscriber,
            'preview': True,
        }
        return render_to_string('newsletter/email/newsletter_template.html', context)

    def send_to_subscriber(self, subscriber):
        try:
            # Create tracking entry with unique token for this send
            tracking = EmailTracking.objects.create(
                subscriber=subscriber,
                newsletter=self,
                tracking_token=uuid.uuid4()  # Generate new token for each send
            )

            # Process the content as a Django template first
            template = Template(self.content)
            context = Context({
                'subscriber': subscriber,
                'newsletter': self,
            })
            processed_content = template.render(context)

            # Process the preview text as a template too
            if self.preview_text:
                preview_template = Template(self.preview_text)
                processed_preview = preview_template.render(context)
            else:
                processed_preview = ""

            # Prepare context with tracking pixels and links
            email_context = {
                'newsletter': {
                    'content': processed_content,
                    'preview_text': processed_preview,
                },
                'subscriber': subscriber,
                'tracking_pixel': f"{settings.SITE_URL}/newsletter/track/open/{tracking.tracking_token}/",
                'unsubscribe_url': f"{settings.SITE_URL}/newsletter/unsubscribe/{subscriber.confirmation_token}/",
                'preview': False,
            }

            # Render both HTML and text versions
            html_content = render_to_string('newsletter/email/newsletter_template.html', email_context)
            text_content = render_to_string('newsletter/email/newsletter_template.txt', email_context)

            # Send the email using rate-limited email sending
            success, message = send_email_with_rate_limit(
                subject=self.subject,
                recipient=subscriber.email,
                html_content=html_content,
                text_content=text_content,
                priority=4,  # Low priority for newsletters
                content_type='newsletter',
                object_id=self.id
            )

            # If email couldn't be sent immediately, it was queued
            if not success:
                return False, message

            # Update subscriber's last_email_sent_at
            subscriber.last_email_sent_at = timezone.now()
            subscriber.save()

            return True, None

        except Exception as e:
            return False, str(e)

    def send_to_all(self):
        if self.status not in ['draft', 'scheduled']:
            return False, "Newsletter is already being sent or has been sent"

        self.status = 'sending'
        self.save()

        try:
            subscribers = Subscriber.objects.filter(is_confirmed=True, is_active=True)
            error_count = 0
            success_count = 0

            for subscriber in subscribers:
                success, error = self.send_to_subscriber(subscriber)
                if success:
                    success_count += 1
                else:
                    error_count += 1

            self.sent_at = timezone.now()
            self.status = 'sent'
            self.sent_to_count = success_count
            self.save()

            return True, f"Newsletter sent successfully to {success_count} subscribers. {error_count} failures."

        except Exception as e:
            self.status = 'error'
            self.error_message = str(e)
            self.save()
            return False, str(e)


class EmailTracking(models.Model):
    subscriber = models.ForeignKey(Subscriber, on_delete=models.CASCADE)
    newsletter = models.ForeignKey(Newsletter, on_delete=models.CASCADE)
    sent_at = models.DateTimeField(auto_now_add=True)
    opened_at = models.DateTimeField(null=True, blank=True)
    clicked_at = models.DateTimeField(null=True, blank=True)
    tracking_token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    click_count = models.IntegerField(default=0)

    class Meta:
        ordering = ['-sent_at']
        # Removed unique_together constraint to allow multiple sends to same subscriber

    def __str__(self):
        return f"{self.subscriber.email} - {self.newsletter.subject}"

    def mark_opened(self):
        if not self.opened_at:
            self.opened_at = timezone.now()
            self.save()
            # Update newsletter stats
            self.newsletter.open_count = models.F('open_count') + 1
            self.newsletter.save()

    def mark_clicked(self):
        if not self.clicked_at:
            self.clicked_at = timezone.now()
        self.click_count = models.F('click_count') + 1
        self.save()
        # Update newsletter stats
        self.newsletter.click_count = models.F('click_count') + 1
        self.newsletter.save()
