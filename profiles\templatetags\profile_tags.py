from django import template
from django.utils.safestring import mark_safe
import re

register = template.Library()

@register.filter
def replace_currently_text(html_content):
    """
    Replace the 'Currently' text in file input fields with 'Profile Image'
    """
    if html_content and isinstance(html_content, str):
        # Replace different variations of "Currently" text
        html_content = html_content.replace('>Currently:<', '>Profile Image:<')
        html_content = html_content.replace('>Currently<', '>Profile Image<')
        html_content = html_content.replace('>Currently: <', '>Profile Image: <')
        
        # Also handle the case where it's in a span or other element
        html_content = re.sub(r'(<[^>]*>)Currently(:|)(<[^>]*>)', 
                             r'\1Profile Image\2\3', 
                             html_content)
        
        return mark_safe(html_content)
    return html_content 