# Generated by Django 5.1.5 on 2025-03-13 14:28

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('home', '0002_carouselitem_cropping'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AudioListeningSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_id', models.CharField(max_length=100, unique=True)),
                ('anonymous_user_id', models.CharField(blank=True, max_length=100, null=True)),
                ('work_id', models.CharField(max_length=100)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('duration_ms', models.IntegerField(default=0)),
                ('initial_position', models.FloatField(default=0)),
                ('last_position', models.FloatField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='AudioEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_id', models.CharField(max_length=100, unique=True)),
                ('anonymous_user_id', models.CharField(blank=True, max_length=100, null=True)),
                ('work_id', models.CharField(max_length=100)),
                ('event_type', models.CharField(choices=[('play', 'Play'), ('pause', 'Pause'), ('ended', 'Ended'), ('seeking', 'Seeking'), ('speed_change', 'Speed Change'), ('position_update', 'Position Update'), ('session_start', 'Session Start'), ('session_end', 'Session End'), ('previous_chapter', 'Previous Chapter'), ('next_chapter', 'Next Chapter'), ('skip_backward', 'Skip Backward'), ('skip_forward', 'Skip Forward'), ('progress_bar_click', 'Progress Bar Click'), ('chapter_select', 'Chapter Select'), ('download_chapter', 'Download Chapter'), ('download_remaining', 'Download Remaining'), ('download_entire', 'Download Entire'), ('download_complete', 'Download Complete'), ('download_failed', 'Download Failed'), ('page_hidden', 'Page Hidden'), ('page_visible', 'Page Visible'), ('page_unload', 'Page Unload'), ('other', 'Other')], max_length=50)),
                ('timestamp', models.DateTimeField()),
                ('playback_position', models.FloatField(default=0)),
                ('url', models.URLField(blank=True, null=True)),
                ('data', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='events', to='home.audiolisteningsession')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddIndex(
            model_name='audiolisteningsession',
            index=models.Index(fields=['user'], name='home_audiol_user_id_dcdd19_idx'),
        ),
        migrations.AddIndex(
            model_name='audiolisteningsession',
            index=models.Index(fields=['work_id'], name='home_audiol_work_id_e4d38e_idx'),
        ),
        migrations.AddIndex(
            model_name='audiolisteningsession',
            index=models.Index(fields=['start_time'], name='home_audiol_start_t_98a31c_idx'),
        ),
        migrations.AddIndex(
            model_name='audioevent',
            index=models.Index(fields=['user'], name='home_audioe_user_id_fc91bb_idx'),
        ),
        migrations.AddIndex(
            model_name='audioevent',
            index=models.Index(fields=['work_id'], name='home_audioe_work_id_eb6353_idx'),
        ),
        migrations.AddIndex(
            model_name='audioevent',
            index=models.Index(fields=['event_type'], name='home_audioe_event_t_983748_idx'),
        ),
        migrations.AddIndex(
            model_name='audioevent',
            index=models.Index(fields=['timestamp'], name='home_audioe_timesta_3eadee_idx'),
        ),
    ]
