{% extends "contest/base.html" %}
{% load static %}

{% block contest_title %}Delete Submission{% endblock %}

{% block contest_content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h2 class="card-title mb-0">Delete Submission</h2>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <h4 class="alert-heading">Warning!</h4>
                    <p>Are you sure you want to delete your submission "{{ submission.title }}"?</p>
                    <p>This action cannot be undone. All data associated with this submission, including likes and jury scores, will be permanently deleted.</p>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="submission-preview text-center mb-4">
                            {% if submission.file.url|lower|slice:"-4:" == '.jpg' or submission.file.url|lower|slice:"-5:" == '.jpeg' or submission.file.url|lower|slice:"-4:" == '.png' %}
                                <img src="{{ submission.file.url }}" alt="{{ submission.title }}" class="img-fluid rounded">
                            {% else %}
                                <div class="file-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <div class="text-center">
                                        <i class="fas fa-file fa-5x text-muted"></i>
                                        <p class="mt-3">File: {{ submission.file.url|cut:"/" }}</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h3>Submission Details</h3>
                        <ul class="list-group mb-4">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Title
                                <span>{{ submission.title }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Category
                                <span>{{ submission.category.name }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Submitted
                                <span>{{ submission.submitted_at|date:"F j, Y" }}</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                Likes
                                <span class="badge bg-primary rounded-pill">{{ submission.total_likes }}</span>
                            </li>
                        </ul>

                        <form method="POST">
                            {% csrf_token %}

                            {% if submission.review_created %}
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="delete_review" name="delete_review">
                                <label class="form-check-label" for="delete_review">
                                    Also delete my book review from the book page
                                </label>
                                <div class="form-text">
                                    If unchecked, your book review will remain on the book page even after deleting this submission.
                                </div>
                            </div>
                            {% endif %}

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-danger btn-lg">Yes, Delete This Submission</button>
                                <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-secondary btn-lg">No, Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
