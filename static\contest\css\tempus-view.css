/**
 * TEMPUS View - Enhanced gallery viewing experience
 * Styles for the lightbox with bottom details bar
 */

/* Main container */
.tempus-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box; /* Include padding in width/height calculation */
}

/* Prevent body scrolling when TEMPUS View is open */
body.tempus-view-open {
    overflow: hidden;
}

/* Image container */
.tempus-view-image-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    padding: 10px;
    box-sizing: border-box;
}

/* Image */
.tempus-view-image {
    max-width: 90%;
    max-height: 85%;
    object-fit: contain;
    transition: transform 0.3s ease;
    cursor: zoom-in;
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
    width: auto;
    height: auto;
    margin: 0 auto; /* Center the image */
    display: block; /* Ensure block display */
    position: relative; /* Enable positioning */
    transform-origin: center; /* Center transform origin by default */
    will-change: transform; /* Optimize for animations */
}

.tempus-view-image.zoomed {
    max-width: 100%;
    max-height: 100%;
    cursor: move; /* Show move cursor when zoomed */
}

/* When actively panning */
.tempus-view-container.panning .tempus-view-image {
    cursor: grabbing;
    transition: none; /* Disable transition during panning for smoother movement */
}

/* Prevent image dragging/saving in the entire application */
.submission-image-container img,
.submission-card img {
    user-select: none;
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;
}

/* Details bar at bottom */
.tempus-view-details {
    position: fixed; /* Changed from static to fixed */
    bottom: 0; /* Ensure it stays at the bottom */
    left: 0; /* Ensure it spans the full width */
    width: 100%;
    background-color: rgba(0, 0, 0, 0.8); /* Darker background for better visibility */
    padding: 15px 30px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(5px);
    height: auto;
    z-index: 10000; /* Ensure it's above other elements */
    box-sizing: border-box; /* Include padding in width calculation */
}

/* Close button */
.tempus-view-close {
    position: fixed; /* Changed from absolute to fixed */
    top: 15px;
    right: 15px;
    color: #fff;
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10001; /* Increased z-index */
    opacity: 0.8;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.7); /* Darker background for better visibility */
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); /* Added shadow for better visibility */
}

.tempus-view-close:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.7);
    transform: scale(1.1);
}

/* Navigation buttons */
.tempus-view-nav {
    position: fixed; /* Changed from absolute to fixed */
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    font-size: 2rem;
    cursor: pointer;
    z-index: 10001; /* Increased z-index */
    opacity: 0.7;
    transition: all 0.2s ease;
    background-color: rgba(0, 0, 0, 0.7); /* Darker background for better visibility */
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); /* Added shadow for better visibility */
}

.tempus-view-nav:hover {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.7);
    transform: translateY(-50%) scale(1.1);
}

.tempus-view-nav.disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.tempus-view-prev {
    left: 20px;
}

.tempus-view-next {
    right: 20px;
}

/* Controls bar */
.tempus-view-controls {
    position: fixed; /* Changed from absolute to fixed */
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 10px 15px;
    border-radius: 30px;
    z-index: 10000;
    transition: opacity 0.3s ease, transform 0.3s ease;
    width: auto; /* Ensure width is based on content */
    max-width: 90%; /* Prevent controls from extending beyond screen */
}

/* Hidden controls */
.tempus-view-controls.hidden,
.tempus-view-details.hidden,
.tempus-view-close.hidden,
.tempus-view-nav.hidden {
    opacity: 0;
    pointer-events: none;
}

/* Show controls on hover */
.tempus-view:hover .tempus-view-controls.hidden,
.tempus-view:hover .tempus-view-details.hidden,
.tempus-view:hover .tempus-view-close.hidden,
.tempus-view:hover .tempus-view-nav.hidden {
    opacity: 0.5;
}

/* Control buttons */
.tempus-view-control {
    color: #fff;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.2s ease;
    background-color: transparent;
    border: none;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.tempus-view-control:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

/* Details content styling */
.tempus-view-details-left {
    flex: 1;
}

.tempus-view-details-center {
    flex: 1;
    text-align: center;
}

.tempus-view-details-right {
    flex: 1;
    text-align: right;
}

.tempus-view-title {
    font-size: 1.5rem;
    margin-bottom: 5px;
    font-weight: 600;
}

.tempus-view-artist {
    font-size: 1.1rem;
    font-style: italic;
    margin-bottom: 5px;
}

.tempus-view-category {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.2);
    padding: 3px 10px;
    border-radius: 15px;
    font-size: 0.9rem;
    margin-right: 10px;
}

.tempus-view-likes {
    display: inline-block;
    color: #ff6b6b;
}

.tempus-view-likes i {
    margin-right: 5px;
}

.tempus-view-details-link {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    text-decoration: none;
    transition: background-color 0.2s;
}

.tempus-view-details-link:hover {
    background-color: #0069d9;
    color: white;
    text-decoration: none;
}

/* Fullscreen mode */
.tempus-view-fullscreen .tempus-view-image {
    max-width: 100%;
    max-height: 100%;
}

.tempus-view-fullscreen .tempus-view-details {
    display: none;
}

/* TEMPUS View button in gallery */
.tempus-view-btn {
    background-color: #6f42c1;
    border-color: #6f42c1;
    transition: all 0.3s ease;
    display: inline-block !important;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
}

.tempus-view-btn:hover {
    background-color: #5a32a3;
    border-color: #5a32a3;
}

.tempus-view-btn i {
    margin-right: 0.5rem;
}

/* Container for TEMPUS View button */
.tempus-view-btn-container {
    display: inline-block;
}

/* Mobile optimizations for TEMPUS View button */
@media (max-width: 767.98px) {
    .tempus-view-btn-container {
        width: 100%;
        text-align: center;
        margin-top: 0.5rem;
    }

    .tempus-view-btn {
        width: 100%;
        max-width: 250px;
    }
}

/* Make submission images clickable */
.submission-card .submission-image {
    cursor: pointer;
    overflow: hidden;
}

.submission-card .submission-image img {
    transition: transform 0.3s ease;
}

.submission-card .submission-image:hover img {
    transform: scale(1.05);
}

/* Comprehensive responsive design for all screen sizes */

/* Base responsive adjustments for screens below 1200px */
@media (max-width: 1200px) {
    .tempus-view-image-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: calc(100% - 100px); /* Adjust for details bar */
        padding: 0;
        position: relative;
        margin-bottom: 100px; /* Make room for details bar */
    }

    .tempus-view-image {
        max-width: 85%;
        max-height: 60%;
        margin: 0 auto;
        position: relative;
        top: -5%;
    }

    .tempus-view-controls {
        bottom: 100px;
        padding: 8px 15px;
        gap: 12px;
        z-index: 10001;
    }

    .tempus-view-details {
        padding: 10px 15px;
        max-height: 35%;
        overflow-y: auto;
    }

    .tempus-view-nav {
        top: 40%;
    }

    .tempus-view-prev {
        left: 15px;
    }

    .tempus-view-next {
        right: 15px;
    }
}

/* Specific fix for screens around 1084x677 (like in the screenshot) */
@media (min-width: 1000px) and (max-width: 1100px) and (min-height: 650px) and (max-height: 700px) {
    .tempus-view-controls {
        bottom: 120px; /* Move controls up to ensure visibility */
        transform: translateX(-50%) scale(1); /* Ensure proper scaling */
    }

    .tempus-view-image {
        max-height: 55%; /* Slightly reduce image height */
        top: -8%; /* Move image up slightly */
    }

    .tempus-view-details {
        padding: 8px 12px; /* Reduce padding */
    }
}

/* Medium screens (tablets, small laptops) */
@media (max-width: 992px) {
    .tempus-view-image {
        max-width: 85%;
        max-height: 55%;
        top: -8%;
    }

    .tempus-view-controls {
        bottom: 90px;
        padding: 8px 12px;
        gap: 10px;
    }

    .tempus-view-details {
        padding: 8px 12px;
    }

    .tempus-view-title {
        font-size: 1.3rem;
    }

    .tempus-view-artist {
        font-size: 1.1rem;
    }

    .tempus-view-nav {
        font-size: 1.8rem;
        width: 45px;
        height: 45px;
    }
}

/* Tablets and large phones */
@media (max-width: 768px) {
    .tempus-view-image {
        max-width: 85%;
        max-height: 50%;
        top: -10%;
    }

    .tempus-view-details {
        flex-direction: column;
        padding: 8px 10px;
        height: auto;
    }

    .tempus-view-details-left,
    .tempus-view-details-center,
    .tempus-view-details-right {
        width: 100%;
        text-align: center;
        margin-bottom: 8px;
    }

    .tempus-view-title {
        font-size: 1.2rem;
    }

    .tempus-view-artist {
        font-size: 1rem;
    }

    .tempus-view-controls {
        bottom: 80px;
        padding: 6px 10px;
        gap: 8px;
    }

    .tempus-view-control {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    .tempus-view-nav {
        font-size: 1.5rem;
        width: 40px;
        height: 40px;
    }

    .tempus-view-prev {
        left: 10px;
    }

    .tempus-view-next {
        right: 10px;
    }
}

/* Small tablets and large phones */
@media (max-width: 600px) {
    .tempus-view-image {
        max-width: 85%;
        max-height: 45%;
        top: -12%;
    }

    .tempus-view-controls {
        bottom: 70px;
        padding: 5px 8px;
        gap: 6px;
    }

    .tempus-view-control {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .tempus-view-details {
        padding: 6px 8px;
        max-height: 30%;
    }

    .tempus-view-title {
        font-size: 1.1rem;
        margin-bottom: 3px;
    }

    .tempus-view-artist {
        font-size: 0.9rem;
    }

    .tempus-view-category {
        font-size: 0.8rem;
        padding: 2px 8px;
    }

    .tempus-view-details-link {
        padding: 5px 10px;
        font-size: 0.9rem;
    }

    .tempus-view-nav {
        font-size: 1.3rem;
        width: 35px;
        height: 35px;
    }
}

/* Phones */
@media (max-width: 480px) {
    .tempus-view-image {
        max-width: 85%;
        max-height: 40%;
        top: -15%;
    }

    .tempus-view-controls {
        bottom: 60px;
        padding: 4px 8px;
        gap: 5px;
        transform: translateX(-50%) scale(0.95);
    }

    .tempus-view-control {
        width: 30px;
        height: 30px;
        font-size: 0.85rem;
    }

    .tempus-view-details {
        padding: 5px 8px;
        max-height: 28%;
    }

    .tempus-view-title {
        font-size: 1rem;
        margin-bottom: 2px;
    }

    .tempus-view-artist {
        font-size: 0.85rem;
    }

    .tempus-view-category {
        font-size: 0.75rem;
        padding: 2px 6px;
    }

    .tempus-view-details-link {
        padding: 4px 8px;
        font-size: 0.85rem;
    }

    .tempus-view-nav {
        font-size: 1.2rem;
        width: 32px;
        height: 32px;
    }

    .tempus-view-prev {
        left: 5px;
    }

    .tempus-view-next {
        right: 5px;
    }
}

/* Small phones */
@media (max-width: 400px) {
    .tempus-view-image {
        max-width: 85%;
        max-height: 35%;
        top: -18%;
    }

    .tempus-view-controls {
        bottom: 55px;
        padding: 3px 6px;
        gap: 4px;
        transform: translateX(-50%) scale(0.9);
    }

    .tempus-view-control {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .tempus-view-details {
        padding: 4px 6px;
        max-height: 25%;
    }

    .tempus-view-title {
        font-size: 0.9rem;
        margin-bottom: 1px;
    }

    .tempus-view-artist {
        font-size: 0.8rem;
    }

    .tempus-view-category {
        font-size: 0.7rem;
        padding: 1px 5px;
    }

    .tempus-view-details-link {
        padding: 3px 6px;
        font-size: 0.8rem;
    }

    .tempus-view-nav {
        font-size: 1rem;
        width: 30px;
        height: 30px;
    }
}

/* Very small phones */
@media (max-width: 320px) {
    .tempus-view-image {
        max-width: 85%;
        max-height: 30%;
        top: -20%;
    }

    .tempus-view-controls {
        bottom: 50px;
        padding: 2px 5px;
        gap: 3px;
        transform: translateX(-50%) scale(0.85);
    }

    .tempus-view-control {
        width: 26px;
        height: 26px;
        font-size: 0.75rem;
    }

    .tempus-view-details {
        padding: 3px 5px;
        max-height: 22%;
    }

    .tempus-view-title {
        font-size: 0.85rem;
        margin-bottom: 1px;
    }

    .tempus-view-artist {
        font-size: 0.75rem;
    }

    .tempus-view-category {
        font-size: 0.65rem;
        padding: 1px 4px;
    }

    .tempus-view-details-link {
        padding: 2px 5px;
        font-size: 0.75rem;
    }

    .tempus-view-nav {
        font-size: 0.9rem;
        width: 28px;
        height: 28px;
    }
}

/* Orientation-specific adjustments */
@media (max-width: 1200px) and (orientation: landscape) {
    .tempus-view-image {
        max-width: 70%;
        max-height: 65%;
        top: -5%;
    }

    .tempus-view-controls {
        bottom: 60px;
    }

    .tempus-view-details {
        max-height: 25%;
    }
}

@media (max-width: 768px) and (orientation: landscape) {
    .tempus-view-image {
        max-width: 65%;
        max-height: 60%;
        top: -8%;
    }

    .tempus-view-details {
        flex-direction: row;
        padding: 5px 8px;
        max-height: 20%;
    }

    .tempus-view-details-left,
    .tempus-view-details-center,
    .tempus-view-details-right {
        margin-bottom: 0;
    }

    .tempus-view-controls {
        bottom: 50px;
    }
}

@media (max-width: 600px) and (orientation: landscape) {
    .tempus-view-image {
        max-width: 60%;
        max-height: 55%;
        top: -10%;
    }

    .tempus-view-controls {
        bottom: 40px;
    }

    .tempus-view-details {
        max-height: 18%;
    }
}

/* Special case for Galaxy S10 size (726x677) */
@media (min-width: 720px) and (max-width: 730px) and (min-height: 670px) and (max-height: 680px) {
    .tempus-view-image {
        max-width: 80%;
        max-height: 45%;
        top: -10%;
    }

    .tempus-view-controls {
        bottom: 80px;
    }

    .tempus-view-details {
        max-height: 25%;
    }
}

/* Ensure all controls are visible and accessible */
.tempus-view-controls,
.tempus-view-details,
.tempus-view-close,
.tempus-view-nav {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Mobile touch optimizations */
@media (max-width: 768px) {
    /* Optimize for touch - larger touch targets */
    .tempus-view-control {
        width: 44px;
        height: 44px;
    }

    /* Improve zoom experience on mobile */
    .tempus-view-image.zoomed {
        max-width: 100%;
        max-height: 100%;
        transform-origin: center center; /* Default center origin for mobile */
    }

    /* Hide controls when zoomed on mobile for better viewing */
    .tempus-view-image.zoomed ~ .tempus-view-controls,
    .tempus-view-image.zoomed ~ .tempus-view-details,
    .tempus-view-image.zoomed ~ .tempus-view-close,
    .tempus-view-image.zoomed ~ .tempus-view-nav {
        opacity: 0;
        pointer-events: none;
    }

    /* Show a hint for users on how to interact */
    .tempus-view-container::after {
        content: '';
        position: fixed;
        bottom: 120px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        opacity: 0;
        transition: opacity 0.5s ease;
        pointer-events: none;
        white-space: nowrap;
        z-index: 10002;
    }

    /* Show hint on first load */
    .tempus-view-container.show-hint::after {
        content: 'Pinch to zoom, tap to show/hide controls';
        opacity: 1;
        animation: fadeOut 3s forwards;
        animation-delay: 2s;
    }

    @keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }
}

/* Ensure image is always visible */
.tempus-view-image {
    transition: transform 0.3s ease, max-width 0.3s ease, max-height 0.3s ease;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
}
