/**
 * Offline Audio Manager - Manages audio downloads and offline playback functionality
 * Handles IndexedDB storage, download queue, and offline status tracking
 */
class OfflineAudioManager {
    constructor() {
        this.db = null;
        this.initialized = false;
        this.downloadQueue = [];
        this.isDownloading = false;
        this.DOWNLOAD_STATES = {
            PENDING: 'pending',
            DOWNLOADING: 'downloading',
            COMPLETED: 'completed',
            FAILED: 'failed'
        };
        
        // IndexedDB database configuration
        this.DB_NAME = 'tempus_offline_audio';
        this.DB_VERSION = 1;
        this.AUDIO_STORE = 'audio_metadata';
        
        // Initialize the database
        this.initDatabase();
        
        // Listen for online/offline events
        this.setupNetworkListeners();
    }
    
    /**
     * Initialize the IndexedDB database for offline audio storage
     */
    initDatabase() {
        const request = indexedDB.open(this.DB_NAME, this.DB_VERSION);
        
        request.onerror = (event) => {
            console.error('Error opening offline audio database:', event.target.error);
        };
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            
            // Create object store for audio metadata if it doesn't exist
            if (!db.objectStoreNames.contains(this.AUDIO_STORE)) {
                const store = db.createObjectStore(this.AUDIO_STORE, { keyPath: 'id' });
                store.createIndex('audiobookId', 'audiobookId', { unique: false });
                store.createIndex('partNumber', 'partNumber', { unique: false });
                store.createIndex('chapterNumber', 'chapterNumber', { unique: false });
                store.createIndex('url', 'url', { unique: false });
                console.log('Created offline audio metadata store');
            }
        };
        
        request.onsuccess = (event) => {
            this.db = event.target.result;
            this.initialized = true;
            console.log('Offline audio database initialized');
            
            // Pre-load existing cache items into the database
            this.syncCacheWithDatabase();
            
            // Dispatch event to indicate the manager is ready
            window.dispatchEvent(new CustomEvent('offline-manager-ready'));
        };
    }
    
    /**
     * Sync the browser cache contents with the database
     * This ensures we have metadata for any files that are already cached
     */
    syncCacheWithDatabase() {
        if (!('caches' in window)) {
            console.warn('Cache API not supported, cannot sync');
            return;
        }
        
        // Open the audio cache
        caches.open('audio-cache-v1').then(cache => {
            // Get all cached requests
            cache.keys().then(requests => {
                requests.forEach(request => {
                    const url = request.url;
                    // Check if it's an audio file
                    if (this.isAudioUrl(url)) {
                        // Extract metadata from the URL
                        const metadata = this.extractMetadataFromUrl(url);
                        if (metadata) {
                            // Store in database if not already there
                            this.ensureMetadataInDatabase(metadata);
                        }
                    }
                });
            });
        }).catch(error => {
            console.error('Error syncing cache with database:', error);
        });
    }
    
    /**
     * Check if a URL is for an audio file
     */
    isAudioUrl(url) {
        const audioExtensions = ['.mp3', '.m4a', '.wav', '.ogg', '.flac', '.aac'];
        return audioExtensions.some(ext => url.toLowerCase().endsWith(ext));
    }
    
    /**
     * Extract metadata from a URL
     */
    extractMetadataFromUrl(url) {
        try {
            // Try to extract audiobookId, part information, and chapter information from URL
            // Example: https://tempus-valleyberg-bucket.s3.eu-north-1.amazonaws.com/audiobooks/the-age-of-new-era-audiobook/part-1-the-origin/01-chapter-1.mp3
            
            const urlObj = new URL(url);
            const pathParts = urlObj.pathname.split('/');
            
            // Find the audiobook ID part
            const audiobookPathIndex = pathParts.findIndex(part => part === 'audiobooks');
            if (audiobookPathIndex === -1) return null;
            
            // The format looks like 'the-age-of-new-era-audiobook'
            const audiobookName = pathParts[audiobookPathIndex + 1];
            // The format looks like 'part-1-the-origin'
            const partFolderMatch = pathParts.find(part => part.startsWith('part-'));
            // The format looks like '01-chapter-1.mp3'
            const chapterFile = pathParts[pathParts.length - 1];
            
            if (!audiobookName || !partFolderMatch || !chapterFile) return null;
            
            // Extract part number from folder name (e.g., 'part-1-the-origin' -> 1)
            const partNumberMatch = partFolderMatch.match(/part-(\d+)/);
            const partNumber = partNumberMatch ? parseInt(partNumberMatch[1], 10) : 1;
            
            // Extract chapter number from filename (e.g., '01-chapter-1.mp3' -> 1)
            // First, remove the file extension
            const baseFilename = chapterFile.substring(0, chapterFile.lastIndexOf('.'));
            // Then get the chapter number from the filename
            let chapterNumber = 0; // Default to 0
            const chapterMatch = baseFilename.match(/(\d+)-chapter-(\d+)/);
            if (chapterMatch) {
                chapterNumber = parseInt(chapterMatch[2], 10);
            } else {
                // Try to extract from format like '01-chapter-name'
                const simpleMatch = baseFilename.match(/^(\d+)/);
                if (simpleMatch) {
                    chapterNumber = parseInt(simpleMatch[1], 10);
                }
            }
            
            // Generate a reasonable title from the filename
            const titleParts = baseFilename.split('-');
            // Remove the number prefix
            titleParts.shift();
            // Format the title
            let title = titleParts.map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
            if (!title) title = `Chapter ${chapterNumber}`;
            
            // Create a unique ID
            const id = `audiobook-${audiobookName}_${partNumber}_${chapterNumber}`;
            
            return {
                id,
                audiobookId: audiobookName,
                partNumber,
                chapterNumber,
                title,
                partTitle: `Part ${partNumber}`,
                url,
                status: this.DOWNLOAD_STATES.COMPLETED
            };
        } catch (error) {
            console.error('Error extracting metadata from URL:', error, url);
            return null;
        }
    }
    
    /**
     * Ensure metadata is stored in the database
     */
    ensureMetadataInDatabase(metadata) {
        if (!this.initialized || !metadata) return;
        
        const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
        const store = transaction.objectStore(this.AUDIO_STORE);
        
        // Check if already exists
        const getRequest = store.get(metadata.id);
        getRequest.onsuccess = (event) => {
            const existingData = event.target.result;
            if (!existingData) {
                // Add to database
                store.put(metadata);
                console.log(`Added cached file to database: ${metadata.title}`);
            }
        };
    }
    
    /**
     * Set up network status change listeners
     */
    setupNetworkListeners() {
        window.addEventListener('online', () => {
            console.log('🌐 Network connection restored');
            this.processDownloadQueue();
            // Dispatch event for UI to update
            window.dispatchEvent(new CustomEvent('offline-status-changed', { 
                detail: { isOnline: true } 
            }));
        });
        
        window.addEventListener('offline', () => {
            console.log('📴 Network connection lost');
            // Dispatch event for UI to update
            window.dispatchEvent(new CustomEvent('offline-status-changed', { 
                detail: { isOnline: false } 
            }));
        });
    }
    
    /**
     * Check if a specific chapter is available offline
     * @param {string} audiobookId - ID of the audiobook
     * @param {number} partNumber - Part number
     * @param {number} chapterNumber - Chapter number
     * @returns {Promise<boolean>} - Promise resolving to true if available offline
     */
    isChapterAvailableOffline(audiobookId, partNumber, chapterNumber) {
        return new Promise((resolve, reject) => {
            if (!this.initialized) {
                resolve(false);
                return;
            }
            
            const transaction = this.db.transaction([this.AUDIO_STORE], 'readonly');
            const store = transaction.objectStore(this.AUDIO_STORE);
            
            // Create a composite ID for the chapter
            const chapterId = `${audiobookId}_${partNumber}_${chapterNumber}`;
            const request = store.get(chapterId);
            
            request.onsuccess = (event) => {
                const result = event.target.result;
                if (result && result.status === this.DOWNLOAD_STATES.COMPLETED) {
                    // Also verify it's in the cache
                    this.verifyInCache(result.url).then(inCache => {
                        resolve(inCache);
                    });
                } else {
                    resolve(false);
                }
            };
            
            request.onerror = (event) => {
                console.error('Error checking offline availability:', event.target.error);
                resolve(false);
            };
        });
    }
    
    /**
     * Verify a URL is in the cache
     */
    async verifyInCache(url) {
        try {
            // Try to open the cache first
            const cache = await caches.open('audio-cache-v1').catch(err => {
                console.log('Cache API not available or restricted, falling back to IndexedDB only');
                return null;
            });
            
            // If cache isn't available, return false
            if (!cache) return false;
            
            // Check if the url exists in cache
            const response = await cache.match(url);
            return !!response;
        } catch (error) {
            console.log('Cache verification error, assuming not in cache:', error.message);
            return false;
        }
    }
    
    /**
     * Get all downloaded chapters for an audiobook
     * @param {string} audiobookId - ID of the audiobook
     * @returns {Promise<Array>} - Promise resolving to array of downloaded chapter metadata
     */
    getDownloadedChapters(audiobookId) {
        return new Promise((resolve, reject) => {
            if (!this.initialized) {
                resolve([]);
                return;
            }
            
            const transaction = this.db.transaction([this.AUDIO_STORE], 'readonly');
            const store = transaction.objectStore(this.AUDIO_STORE);
            const index = store.index('audiobookId');
            const request = index.getAll(audiobookId);
            
            request.onsuccess = (event) => {
                const results = event.target.result || [];
                // Only return completed downloads
                const completed = results.filter(item => item.status === this.DOWNLOAD_STATES.COMPLETED);
                
                // Verify each one is actually in the cache
                Promise.all(completed.map(item => 
                    this.verifyInCache(item.url).then(inCache => ({...item, inCache}))
                )).then(verifiedResults => {
                    resolve(verifiedResults.filter(item => item.inCache));
                });
            };
            
            request.onerror = (event) => {
                console.error('Error getting downloaded chapters:', event.target.error);
                resolve([]);
            };
        });
    }
    
    /**
     * Queue a chapter for download
     * @param {Object} metadata - Metadata for the chapter to download
     * @returns {Promise<boolean>} - Promise resolving to true if queued successfully
     */
    queueChapterDownload(metadata) {
        if (!metadata || !metadata.audiobookId || !metadata.partNumber === undefined || 
            metadata.chapterNumber === undefined || !metadata.url || !metadata.title) {
            console.error('Invalid metadata for download:', metadata);
            return Promise.resolve(false);
        }
        
        // Create a composite ID for the chapter
        const chapterId = `${metadata.audiobookId}_${metadata.partNumber}_${metadata.chapterNumber}`;
        metadata.id = chapterId;
        metadata.queueTime = Date.now();
        metadata.status = this.DOWNLOAD_STATES.PENDING;
        
        return new Promise((resolve, reject) => {
            if (!this.initialized) {
                console.warn('Database not initialized yet, will retry');
                // Queue for processing when db is ready
                this.downloadQueue.push({metadata, resolve, reject});
                return;
            }
            
            const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
            const store = transaction.objectStore(this.AUDIO_STORE);
            
            // Check if already exists
            const getRequest = store.get(chapterId);
            getRequest.onsuccess = (event) => {
                const existingData = event.target.result;
                if (existingData && existingData.status === this.DOWNLOAD_STATES.COMPLETED) {
                    // Verify it's actually in the cache
                    this.verifyInCache(existingData.url).then(inCache => {
                        if (inCache) {
                            console.log(`Chapter already downloaded and in cache: ${metadata.title}`);
                            resolve(true);
                        } else {
                            // Cached file is missing, need to re-download
                            this.queueForDownload(metadata, store, resolve, reject);
                        }
                    });
                } else {
                    this.queueForDownload(metadata, store, resolve, reject);
                }
            };
            
            getRequest.onerror = (error) => {
                console.error('Error checking chapter in database:', error);
                reject(error);
            };
        });
    }
    
    /**
     * Queue a chapter for download
     * @param {Object} metadata - Chapter metadata
     * @param {IDBObjectStore} store - IndexedDB object store (optional)
     * @param {Function} resolve - Promise resolve function
     * @param {Function} reject - Promise reject function
     */
    queueForDownload(metadata, store, resolve, reject) {
        try {
            // Always create a new transaction for this operation
            const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
            const newStore = transaction.objectStore(this.AUDIO_STORE);
            
            // Add transaction complete and error handlers
            transaction.oncomplete = () => {
                console.log(`Successfully queued chapter for download: ${metadata.title}`);
                // Add to download queue after transaction completes
                this.downloadQueue.push({metadata, resolve, reject});
                
                // Start processing the queue if not already downloading
                if (!this.isDownloading) {
                    this.processDownloadQueue();
                }
            };
            
            transaction.onerror = (event) => {
                console.error('Transaction error when queueing download:', event.target.error);
                // Still add to download queue even if DB operation fails
                this.downloadQueue.push({metadata, resolve, reject});
                
                if (!this.isDownloading) {
                    this.processDownloadQueue();
                }
            };
            
            // Store the metadata in IndexedDB
            const putRequest = newStore.put(metadata);
            
            putRequest.onerror = (event) => {
                console.error('Error queueing chapter for download:', event.target.error);
                // Don't reject here, let the transaction error handler handle it
            };
        } catch (error) {
            console.error('Error in queueForDownload:', error);
            // Still add to download queue even if DB operation fails
            this.downloadQueue.push({metadata, resolve, reject});
            
            // Start processing the queue if not already downloading
            if (!this.isDownloading) {
                this.processDownloadQueue();
            }
        }
    }
    
    /**
     * Queue multiple chapters for download
     * @param {Array} chaptersMetadata - Array of chapter metadata
     * @returns {Promise<Array>} - Promise resolving to array of results
     */
    queueMultipleChapters(chaptersMetadata) {
        return Promise.all(
            chaptersMetadata
                .filter(metadata => metadata && metadata.url && metadata.title)
                .map(metadata => this.queueChapterDownload(metadata))
        );
    }
    
    /**
     * Process the download queue
     */
    processDownloadQueue() {
        if (!navigator.onLine) {
            console.log('Offline - download queue processing paused');
            return;
        }
        
        if (this.isDownloading || this.downloadQueue.length === 0) {
            return;
        }
        
        this.isDownloading = true;
        const {metadata, resolve, reject} = this.downloadQueue.shift();
        
        this.updateChapterStatus(metadata.id, this.DOWNLOAD_STATES.DOWNLOADING, {
            progress: 0,
            startTime: Date.now()
        });
        
        // Dispatch event for UI to update
        window.dispatchEvent(new CustomEvent('chapter-download-started', { 
            detail: { metadata } 
        }));
        
        this.downloadChapter(metadata)
            .then(success => {
                this.updateChapterStatus(metadata.id, 
                    success ? this.DOWNLOAD_STATES.COMPLETED : this.DOWNLOAD_STATES.FAILED);
                
                // Dispatch event for UI to update
                window.dispatchEvent(new CustomEvent('chapter-download-completed', { 
                    detail: { metadata, success } 
                }));
                
                resolve(success);
                
                // Process next in queue
                this.isDownloading = false;
                this.processDownloadQueue();
            })
            .catch(error => {
                this.updateChapterStatus(metadata.id, this.DOWNLOAD_STATES.FAILED, { error });
                
                // Dispatch event for UI to update
                window.dispatchEvent(new CustomEvent('chapter-download-failed', { 
                    detail: { metadata, error } 
                }));
                
                reject(error);
                
                // Process next in queue
                this.isDownloading = false;
                this.processDownloadQueue();
            });
    }
    
    /**
     * Update the status of a chapter in the database
     * @param {string} chapterId - ID of the chapter
     * @param {string} status - New status
     * @param {Object} extraData - Additional data to store
     */
    updateChapterStatus(chapterId, status, extraData = {}) {
        if (!this.initialized) return;
        
        const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
        const store = transaction.objectStore(this.AUDIO_STORE);
        
        const request = store.get(chapterId);
        request.onsuccess = (event) => {
            const data = event.target.result;
            if (data) {
                data.status = status;
                data.lastUpdated = Date.now();
                
                // Merge in any extra data
                Object.assign(data, extraData);
                
                store.put(data);
                
                // Dispatch event for UI to update
                window.dispatchEvent(new CustomEvent('chapter-status-updated', { 
                    detail: { chapterId, status, data } 
                }));
            }
        };
        
        request.onerror = (event) => {
            console.error('Error updating chapter status:', event.target.error);
        };
    }
    
    /**
     * Update download progress for a chapter
     * @param {string} chapterId - ID of the chapter
     * @param {number} progress - Download progress (0-100)
     */
    updateDownloadProgress(chapterId, progress) {
        this.updateChapterStatus(chapterId, this.DOWNLOAD_STATES.DOWNLOADING, { progress });
        
        // Dispatch event for UI to update
        window.dispatchEvent(new CustomEvent('download-progress-updated', { 
            detail: { chapterId, progress } 
        }));
    }
    
    /**
     * Download a single chapter with retry mechanism
     */
    async downloadChapter(metadata, maxRetries = 3) {
        console.log(`Starting download for ${metadata.title}`);
                
                // Maximum number of retries for transient errors
                let retryCount = 0;
                
                const attemptDownload = async () => {
                    try {
                // Update progress to indicate download is starting
                this.updateDownloadProgress(metadata.id, 0, {
                    message: 'Starting download...'
                });
                
                // Try to create a HEAD request first to verify URL without triggering CORS
                let urlIsValid = true;
                try {
                    // Using XHR which has better CSP compatibility than fetch
                    const xhr = new XMLHttpRequest();
                    xhr.open('HEAD', metadata.url, false); // Synchronous to simplify flow
                    xhr.send();
                    
                    if (xhr.status >= 400) {
                        console.error(`URL validation failed: ${metadata.url} (${xhr.status})`);
                        urlIsValid = false;
                    }
                } catch (error) {
                    // If HEAD request fails due to CORS/CSP, we'll still try the actual download
                    console.log('Could not validate URL due to security policy, will attempt download anyway');
                }
                
                if (!urlIsValid) {
                    throw new Error(`File not found or not accessible at ${metadata.url}`);
                }
                
                // Use XMLHttpRequest for download (more compatible with CSP than fetch)
                return new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', metadata.url);
                    xhr.responseType = 'blob';
                    
                    // Abort controller for timeout
                    const timeoutId = setTimeout(() => {
                        xhr.abort();
                        reject(new Error('Download timeout'));
                    }, 60000); // 60 second timeout
                    
                    // Set up progress monitoring
                    xhr.onprogress = (event) => {
                        if (event.lengthComputable) {
                            const percentComplete = Math.round((event.loaded / event.total) * 100);
                            // Update progress (max 99% until processing)
                            const displayProgress = Math.min(percentComplete, 99);
                            this.updateDownloadProgress(metadata.id, displayProgress);
                            
                            // Dispatch event for progress updates
                            window.dispatchEvent(new CustomEvent('audioDownloadProgress', {
                                detail: {
                                    id: metadata.id,
                                    audiobookId: metadata.audiobookId,
                                    partNumber: metadata.partNumber,
                                    chapterNumber: metadata.chapterNumber,
                                    title: metadata.title,
                                    partTitle: metadata.partTitle,
                                    progress: displayProgress,
                                    total: event.total,
                                    received: event.loaded,
                                    current: 1,
                                    total: 1
                                }
                            }));
                        }
                    };
                        
                    xhr.onload = async () => {
                        clearTimeout(timeoutId);
                        
                        if (xhr.status >= 200 && xhr.status < 300) {
                            // Success - process the blob
                            const blob = xhr.response;
                            
                            try {
                                // Update progress to 99% to show we're processing
                                this.updateDownloadProgress(metadata.id, 99, {
                                    message: 'Processing download...'
                                });
                                
                                // Add to cache if possible
                                await this.addToCache(metadata.url, blob);
                                
                                // Verify blob size is valid
                                const fileSize = blob.size;
                                console.log(`Downloaded file size for ${metadata.title}: ${fileSize} bytes (${this.formatBytes(fileSize)})`);
                                
                                if (fileSize === 0) {
                                    console.error(`Zero file size detected for ${metadata.title}, this may indicate a download issue`);
                                }
                                
                                // Update database
                                const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
                                const store = transaction.objectStore(this.AUDIO_STORE);
                                
                                // Mark as completed in the database
                                const updateData = {
                                    ...metadata,
                                    completedAt: Date.now(),
                                    status: this.DOWNLOAD_STATES.COMPLETED,
                                    fileSize: fileSize,
                                    // Also store the blob in IndexedDB
                                    blob: blob
                                };
                                
                                store.put(updateData);
                                
                                transaction.oncomplete = () => {
                                    console.log(`Download complete for ${metadata.title}`);
                                    
                                    // Set progress to 100% and mark as complete
                                    this.updateDownloadProgress(metadata.id, 100, {
                                        message: 'Download complete!'
                                    });
                                    
                                    // Dispatch final event with 100% progress
                                    window.dispatchEvent(new CustomEvent('audioDownloadProgress', {
                                        detail: {
                                            id: metadata.id,
                                            audiobookId: metadata.audiobookId,
                                            partNumber: metadata.partNumber,
                                            chapterNumber: metadata.chapterNumber,
                                            title: metadata.title,
                                            partTitle: metadata.partTitle,
                                            progress: 100,
                                            total: fileSize,
                                            received: fileSize,
                                            current: 1,
                                            total: 1,
                                            message: 'Download complete!'
                                        }
                                    }));
                                    
                                    // Dispatch a separate completion event
                        window.dispatchEvent(new CustomEvent('audioDownloadComplete', {
                            detail: {
                                id: metadata.id,
                                audiobookId: metadata.audiobookId,
                                partNumber: metadata.partNumber,
                                chapterNumber: metadata.chapterNumber,
                                title: metadata.title,
                                partTitle: metadata.partTitle,
                                progress: 100,
                                            fileSize: fileSize
                            }
                        }));
                        
                        resolve(true);
                                };
                                
                                transaction.onerror = (error) => {
                                    console.error('Error updating database:', error);
                                    reject(error);
                                };
                            } catch (error) {
                                console.error('Error processing download:', error);
                                reject(error);
                            }
                        } else {
                            reject(new Error(`HTTP error: ${xhr.status}`));
                        }
                    };
                    
                    xhr.onerror = () => {
                        clearTimeout(timeoutId);
                        reject(new Error('Network error during download'));
                    };
                    
                    xhr.onabort = () => {
                        clearTimeout(timeoutId);
                        reject(new Error('Download was aborted'));
                    };
                    
                    xhr.send();
                });
            } catch (error) {
                // Handle retry logic
                retryCount++;
                if (retryCount <= maxRetries) {
                    console.log(`Retry ${retryCount}/${maxRetries} for ${metadata.title}`);
                    // Exponential backoff
                    const delay = Math.pow(2, retryCount) * 1000;
                    await new Promise(resolve => setTimeout(resolve, delay));
                            return attemptDownload();
                        }
                throw error;
            }
        };
        
        return attemptDownload();
    }
    
    /**
     * Get the appropriate content type based on file extension
     * @param {string} url - URL of the file
     * @returns {string} - Content type
     */
    getContentType(url) {
        const extension = url.split('.').pop().toLowerCase();
        switch (extension) {
            case 'mp3':
                return 'audio/mpeg';
            case 'ogg':
                return 'audio/ogg';
            case 'wav':
                return 'audio/wav';
            case 'm4a':
            case 'm4b':
                return 'audio/mp4';
            default:
                return 'audio/mpeg'; // Default to mp3
        }
    }
    
    /**
     * Add to cache if possible, otherwise just store in IndexedDB
     */
    async addToCache(url, blob) {
        try {
            if (!('caches' in window)) {
                console.log('Cache API not available in this browser, skipping cache storage');
                return false;
            }
            
            // Try to open cache
            const cache = await caches.open('audio-cache-v1').catch(err => {
                console.log('Unable to open cache, possibly in private browsing:', err.message);
                return null;
            });
            
            if (!cache) {
                console.log('Cache not available, storing in IndexedDB only');
                return false;
            }
            
            // Create a response from the blob
            const headers = new Headers({
                'Content-Type': this.getContentType(url),
                'Content-Length': blob.size.toString()
            });
            
            const response = new Response(blob, {
                status: 200,
                statusText: 'OK',
                headers: headers
            });
            
            // Add to cache
            await cache.put(url, response);
            return true;
        } catch (error) {
            console.log('Error adding to cache, will use IndexedDB only:', error.message);
            return false;
        }
    }
    
    /**
     * Remove a downloaded chapter
     * @param {string} audiobookId - ID of the audiobook
     * @param {number} partNumber - Part number
     * @param {number} chapterNumber - Chapter number
     * @returns {Promise<boolean>} - Promise resolving to true if removal successful
     */
    removeDownloadedChapter(audiobookId, partNumber, chapterNumber) {
        return new Promise((resolve, reject) => {
            if (!this.initialized) {
                resolve(false);
                return;
            }
            
            const chapterId = `${audiobookId}_${partNumber}_${chapterNumber}`;
            const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
            const store = transaction.objectStore(this.AUDIO_STORE);
            
            // Get the chapter data to get the URL
            const getRequest = store.get(chapterId);
            getRequest.onsuccess = (event) => {
                const chapterData = event.target.result;
                if (!chapterData) {
                    resolve(false);
                    return;
                }
                
                // Delete from IndexedDB
                const deleteRequest = store.delete(chapterId);
                deleteRequest.onsuccess = () => {
                    console.log(`Removed chapter from offline storage: ${chapterData.title}`);
                    
                    // Try to remove from cache too
                    if ('caches' in window) {
                        caches.open('audio-cache-v1').then(cache => {
                            cache.delete(chapterData.url).then(() => {
                                console.log(`Removed chapter from cache: ${chapterData.url}`);
                            });
                        });
                    }
                    
                    // Dispatch event for UI to update
                    window.dispatchEvent(new CustomEvent('chapter-removed', { 
                        detail: { chapterId, chapterData } 
                    }));
                    
                    resolve(true);
                };
                
                deleteRequest.onerror = (event) => {
                    console.error('Error removing chapter:', event.target.error);
                    reject(event.target.error);
                };
            };
            
            getRequest.onerror = (event) => {
                console.error('Error getting chapter to remove:', event.target.error);
                reject(event.target.error);
            };
        });
    }
    
    /**
     * Remove all downloaded chapters for an audiobook
     * @param {string} audiobookId - ID of the audiobook
     * @returns {Promise<boolean>} - Promise resolving to true if removal successful
     */
    removeAllDownloadedChapters(audiobookId) {
        return new Promise((resolve, reject) => {
            if (!this.initialized) {
                resolve(false);
                return;
            }
            
            this.getDownloadedChapters(audiobookId)
                .then(chapters => {
                    if (chapters.length === 0) {
                        // Even if we found no chapters, we should clean the cache too
                        this.cleanupCache(audiobookId).then(() => {
                            console.log('No chapters found, but cleaned cache just in case');
                            resolve(true);
                        });
                        return;
                    }
                    
                    // Create an array of promises for each chapter removal
                    const removalPromises = chapters.map(chapter => 
                        this.removeDownloadedChapter(
                            chapter.audiobookId, 
                            chapter.partNumber, 
                            chapter.chapterNumber
                        )
                    );
                    
                    // Wait for all removals to complete
                    Promise.all(removalPromises)
                        .then(() => {
                            console.log(`Removed all ${chapters.length} downloaded chapters for audiobook ${audiobookId}`);
                            
                            // Do an additional cache cleanup to ensure no orphaned files
                            this.cleanupCache(audiobookId).then(() => {
                                resolve(true);
                            });
                        })
                        .catch(error => {
                            console.error('Error removing all chapters:', error);
                            reject(error);
                        });
                })
                .catch(error => {
                    console.error('Error getting chapters to remove:', error);
                    reject(error);
                });
        });
    }
    
    /**
     * Clean up the cache for an audiobook, removing any orphaned files
     * @param {string} audiobookId - ID of the audiobook
     * @returns {Promise<void>} - Promise that resolves when cleanup is complete
     */
    cleanupCache(audiobookId) {
        return new Promise(async (resolve) => {
            if (!('caches' in window)) {
                resolve();
                return;
            }
            
            try {
                console.log(`Cleaning up cache for audiobook ${audiobookId}`);
                
                // Check for orphaned database entries (no corresponding cache entries)
                if (this.initialized) {
                    const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
                    const store = transaction.objectStore(this.AUDIO_STORE);
                    const index = store.index('audiobookId');
                    const request = index.getAll(audiobookId);
                    
                    request.onsuccess = async (event) => {
                        const dbEntries = event.target.result || [];
                        
                        // Open the cache
                        const cache = await caches.open('audio-cache-v1');
                        
                        // Check each DB entry
                        for (const entry of dbEntries) {
                            const response = await cache.match(entry.url);
                            if (!response) {
                                // No cache entry for this DB entry, delete it from DB
                                console.log(`Removing orphaned DB entry: ${entry.id}`);
                                store.delete(entry.id);
                            }
                        }
                    };
                }
                
                // Clean orphaned cache entries (no corresponding DB entries)
                const cache = await caches.open('audio-cache-v1');
                const cacheEntries = await cache.keys();
                
                // Get all valid URLs from the database
                const validUrls = [];
                if (this.initialized) {
                    const transaction = this.db.transaction([this.AUDIO_STORE], 'readonly');
                    const store = transaction.objectStore(this.AUDIO_STORE);
                    const dbEntries = await new Promise((resolve) => {
                        const request = store.getAll();
                        request.onsuccess = (e) => resolve(e.target.result || []);
                        request.onerror = () => resolve([]);
                    });
                    
                    dbEntries.forEach(entry => validUrls.push(entry.url));
                }
                
                // Delete any cache entries that aren't in the valid URL list
                for (const request of cacheEntries) {
                    if (!validUrls.includes(request.url) && this.isAudioUrl(request.url)) {
                        console.log(`Removing orphaned cache entry: ${request.url}`);
                        await cache.delete(request);
                    }
                }
                
                console.log('Cache cleanup completed');
                resolve();
            } catch (error) {
                console.error('Error cleaning up cache:', error);
                resolve(); // Resolve anyway to not block further operations
            }
        });
    }
    
    /**
     * Get the total storage space used by offline audio
     * @returns {Promise<number>} - Promise resolving to size in bytes
     */
    getStorageUsage() {
        return new Promise(async (resolve, reject) => {
            if (!this.initialized) {
                resolve(0);
                return;
            }
            
            try {
                // Get all completed downloads from the database
                const transaction = this.db.transaction([this.AUDIO_STORE], 'readonly');
                const store = transaction.objectStore(this.AUDIO_STORE);
                const allFiles = await new Promise((resolve, reject) => {
                    const request = store.getAll();
                    request.onsuccess = (e) => resolve(e.target.result || []);
                    request.onerror = (e) => reject(e.target.error);
                });
                
                // Filter only completed downloads
                const completedFiles = allFiles.filter(item => 
                    item.status === this.DOWNLOAD_STATES.COMPLETED);
                
                console.log(`Found ${completedFiles.length} completed downloads in database`);
                
                // Also check the cache directly to find any potential orphaned files
                let orphanedCacheEntries = [];
                if ('caches' in window) {
                    try {
                        const cache = await caches.open('audio-cache-v1');
                        const cacheRequests = await cache.keys();
                        console.log(`Found ${cacheRequests.length} items in cache`);
                        
                        // Find cache entries that aren't in our database
                        const dbUrls = completedFiles.map(file => file.url);
                        orphanedCacheEntries = cacheRequests.filter(request => 
                            !dbUrls.includes(request.url) && this.isAudioUrl(request.url));
                            
                        console.log(`Found ${orphanedCacheEntries.length} orphaned audio files in cache`);
                        
                        // Optionally clean up orphaned entries
                        if (orphanedCacheEntries.length > 0) {
                            console.log('Cleaning up orphaned cache entries');
                            orphanedCacheEntries.forEach(request => {
                                cache.delete(request).then(() => {
                                    console.log(`Deleted orphaned cache entry: ${request.url}`);
                                });
                            });
                        }
                    } catch (error) {
                        console.error('Error checking cache for orphaned entries:', error);
                    }
                }
                
                // Only count files that can be verified in cache
                const sizePromises = completedFiles.map(async (file) => {
                    try {
                        // Try to get the actual file from cache
                        if ('caches' in window) {
                            const cache = await caches.open('audio-cache-v1');
                            const response = await cache.match(file.url);
                            
                            if (response) {
                                const blob = await response.blob();
                                const size = blob.size;
                                
                                // Update size in database if needed
                                if (!file.fileSize || file.fileSize === 0) {
                                    await this.updateChapterSize(file.id, size);
                                    file.fileSize = size; // Update local copy for event dispatch
                                }
                                
                                return {
                                    id: file.id,
                                    size: size
                                };
                            } else {
                                // File is in DB but not in cache - mark for cleanup
                                console.log(`File in database but not in cache: ${file.id} (${file.url})`);
                                // Set to failed status so it's not counted anymore
                                this.updateChapterStatus(file.id, this.DOWNLOAD_STATES.FAILED, {
                                    error: 'File missing from cache'
                                });
                                return {
                                    id: file.id,
                                    size: 0
                                };
                            }
                        }
                        
                        // If we already have a valid size, use it
                        if (file.fileSize && file.fileSize > 0) {
                            return {
                                id: file.id,
                                size: file.fileSize
                            };
                        }
                        
                        // No cache API and no size - use 0
                        return {
                            id: file.id,
                            size: 0
                        };
                    } catch (error) {
                        console.error(`Error getting size for ${file.id}:`, error);
                        return {
                            id: file.id,
                            size: 0
                        };
                    }
                });
                
                // Wait for all size calculations to complete
                const sizes = await Promise.all(sizePromises);
                
                // Calculate total size and update individual files
                let totalSize = 0;
                const fileSizes = {};
                
                sizes.forEach(item => {
                    totalSize += item.size;
                    fileSizes[item.id] = item.size;
                });
                
                console.log(`Total storage used: ${totalSize} bytes (${this.formatBytes(totalSize)})`);
                
                // Dispatch event with all file sizes for UI update
                window.dispatchEvent(new CustomEvent('chapter-sizes-updated', { 
                    detail: { fileSizes }
                }));
                
                resolve(totalSize);
            } catch (error) {
                console.error('Error calculating storage usage:', error);
                resolve(0);
            }
        });
    }
    
    /**
     * Update the size of a chapter in the database
     * @param {string} chapterId - ID of the chapter
     * @param {number} size - Size in bytes
     * @returns {Promise<boolean>} - Promise resolving to true if successful
     */
    updateChapterSize(chapterId, size) {
        return new Promise((resolve, reject) => {
            if (!this.initialized || !chapterId || !size) {
                resolve(false);
                return;
            }
            
            console.log(`Updating size for chapter ${chapterId} to ${size} bytes (${this.formatBytes(size)})`);
            
            const transaction = this.db.transaction([this.AUDIO_STORE], 'readwrite');
            const store = transaction.objectStore(this.AUDIO_STORE);
            
            // Get the chapter first
            const getRequest = store.get(chapterId);
            
            getRequest.onsuccess = (event) => {
                const chapter = event.target.result;
                if (chapter) {
                    // Update the size
                    chapter.fileSize = size;
                    
                    // Save it back
                    const updateRequest = store.put(chapter);
                    
                    updateRequest.onsuccess = () => {
                        console.log(`Successfully updated size for chapter ${chapterId} to ${size} bytes`);
                        
                        // Dispatch an event just for this specific chapter update
                        const fileSizes = {};
                        fileSizes[chapterId] = size;
                        window.dispatchEvent(new CustomEvent('chapter-sizes-updated', { 
                            detail: { fileSizes }
                        }));
                        
                        resolve(true);
                    };
                    
                    updateRequest.onerror = (event) => {
                        console.error('Error updating chapter size:', event.target.error);
                        resolve(false);
                    };
                } else {
                    console.warn(`Chapter ${chapterId} not found when trying to update size`);
                    resolve(false);
                }
            };
            
            getRequest.onerror = (event) => {
                console.error('Error getting chapter for size update:', event.target.error);
                resolve(false);
            };
        });
    }
    
    /**
     * Format bytes to human-readable size
     * @param {number} bytes - Size in bytes
     * @returns {string} - Formatted size string
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * Get the audio data for a specific chapter
     * @param {string} audiobookId - ID of the audiobook
     * @param {number} partNumber - Part number
     * @param {number} chapterNumber - Chapter number
     * @returns {Promise<Object>} - Promise resolving to { blob, url } if found
     */
    getChapterAudio(audiobookId, partNumber, chapterNumber) {
        return new Promise(async (resolve, reject) => {
            try {
                // First check if the chapter is available
                const isAvailable = await this.isChapterAvailableOffline(audiobookId, partNumber, chapterNumber);
                if (!isAvailable) {
                    resolve(null);
                    return;
                }

                // Get the metadata from IndexedDB
                const transaction = this.db.transaction([this.AUDIO_STORE], 'readonly');
                const store = transaction.objectStore(this.AUDIO_STORE);
                const chapterId = `${audiobookId}_${partNumber}_${chapterNumber}`;
                
                const request = store.get(chapterId);
                request.onsuccess = async (event) => {
                    const metadata = event.target.result;
                    if (!metadata || !metadata.url) {
                        resolve(null);
                        return;
                    }

                    try {
                        // Try to get from cache
                        const cache = await caches.open('audio-cache-v1');
                        const response = await cache.match(metadata.url);
                        
                        if (!response) {
                            resolve(null);
                            return;
                        }

                        const blob = await response.blob();
                        resolve({
                            blob,
                            url: metadata.url
                        });
                    } catch (error) {
                        console.error('Error getting audio from cache:', error);
                        resolve(null);
                    }
                };

                request.onerror = (event) => {
                    console.error('Error getting chapter metadata:', event.target.error);
                    resolve(null);
                };
            } catch (error) {
                console.error('Error in getChapterAudio:', error);
                resolve(null);
            }
        });
    }

    /**
     * Retrieve a blob from cache
     */
    async retrieveBlobFromCache(url) {
        try {
            if (!('caches' in window)) {
                console.log('Cache API not available, falling back to IndexedDB');
                return null;
            }
            
            // Try to open cache with error handling
            const cache = await caches.open('audio-cache-v1').catch(err => {
                console.log('Unable to open cache, possibly in private browsing:', err.message);
                return null;
            });
            
            if (!cache) {
                console.log('Cache not available, falling back to IndexedDB');
                return null;
            }
            
            // Try to get response from cache
            const response = await cache.match(url);
            
            if (!response || !response.ok) {
                console.log('Audio file not found in cache or response not ok');
                return null;
            }
            
            // Clone the response to avoid consumption issues
            const clonedResponse = response.clone();
            
            // Get blob from response
            return await clonedResponse.blob();
        } catch (error) {
            console.log('Error retrieving from cache, will try IndexedDB:', error.message);
            return null;
        }
    }
}

// Create a global instance
window.offlineAudioManager = new OfflineAudioManager(); 