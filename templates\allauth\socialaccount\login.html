{% extends "socialaccount/base_entrance.html" %}
{% load i18n %}
{% load allauth %}
{% block head_title %}
    {% trans "Sign In" %}
{% endblock head_title %}
{% block content %}
    {% if process == "connect" %}
        {% element h1 %}
            {% blocktrans with provider.name as provider %}Connect {{ provider }}{% endblocktrans %}
        {% endelement %}
        {% element p %}
            {% blocktrans with provider.name as provider %}You are about to connect a new third-party account from {{ provider }}.{% endblocktrans %}
        {% endelement %}
    {% else %}
        {% element h1 %}
            {% blocktrans with provider.name as provider %}Sign In Via {{ provider }}{% endblocktrans %}
        {% endelement %}
        {% element p %}
            {% blocktrans with provider.name as provider %}You are about to sign in using a third-party account from {{ provider }}.{% endblocktrans %}
        {% endelement %}
    {% endif %}
    {% element form method="post" no_visible_fields=True %}
        {% slot actions %}
            {% csrf_token %}
            {% element button type="submit" %}
                {% trans "Continue" %}
            {% endelement %}
        {% endslot %}
    {% endelement %}
{% endblock content %}
