from allauth.account.adapter import De<PERSON>ult<PERSON><PERSON>untAdapter
from .models import Subscriber
from django.template.loader import render_to_string
from django.urls import reverse_lazy
from django.conf import settings
from django.contrib import messages
from django.utils.safestring import mark_safe

# Import email manager utilities
from email_manager.utils import (
    send_email_with_rate_limit,
    should_send_newsletter_confirmation,
    check_email_verification_required
)


class NewsletterAccountAdapter(DefaultAccountAdapter):
    def is_email_verification_required(self, request):
        """
        Override to check our email configuration setting.
        This allows toggling email verification from the admin panel.
        """
        return check_email_verification_required()

    def get_login_redirect_url(self, request):
        """
        Override to show a welcome message when a user logs in after registration.
        """
        # Check if this is a first login after registration
        if hasattr(request, 'user') and request.user.is_authenticated and request.user.date_joined.date() == request.user.last_login.date():
            # Show welcome message
            welcome_message = f"""
            <div class="alert-content">
                <h4>Welcome to Tempus Author Platform!</h4>
                <p>Thank you for joining our literary community. Your account has been created successfully.</p>
                <p>You can now explore our works, participate in contests, and more.</p>
                <p>Visit <a href="/profile/" class="alert-link">your profile</a> to customize your settings.</p>
            </div>
            """
            messages.success(request, mark_safe(welcome_message))

        # Use the default redirect URL
        return super().get_login_redirect_url(request)

    def save_user(self, request, user, form, commit=True):
        user = super().save_user(request, user, form, commit)

        # Check if user opted for newsletter
        if request.POST.get('newsletter_signup'):
            # Create subscriber if doesn't exist
            subscriber, created = Subscriber.objects.get_or_create(
                email=user.email,
                defaults={
                    'name': user.get_full_name() or user.username,
                    'is_confirmed': True  # Auto-confirm since they're registered users
                }
            )

            if created and should_send_newsletter_confirmation():
                # Send welcome email for newsletter subscription
                context = {
                    'subscriber_name': subscriber.name,
                    'unsubscribe_url': request.build_absolute_uri(
                        reverse_lazy('newsletter:unsubscribe',
                                   kwargs={'token': subscriber.confirmation_token})
                    )
                }

                html_message = render_to_string('newsletter/email/registration_confirmation.html', context)
                text_message = render_to_string('newsletter/email/registration_confirmation.txt', context)

                # Use rate-limited email sending
                send_email_with_rate_limit(
                    subject='Welcome to TEMPUS Newsletter',
                    recipient=subscriber.email,
                    html_content=html_message,
                    text_content=text_message,
                    priority=3,  # Normal priority
                    content_type='newsletter_welcome',
                    object_id=subscriber.id
                )

            # Add a UI message about newsletter subscription
            newsletter_message = """
            <div class="alert-content">
                <p>You've been subscribed to our newsletter. You'll receive updates about new releases and literary events.</p>
            </div>
            """
            messages.info(request, mark_safe(newsletter_message))

        return user

    def respond_email_verification_sent(self, request, user):
        """
        Override to show a UI message when verification email is sent.
        """
        # Add a UI message about email verification
        verification_message = """
        <div class="alert-content">
            <h4>Verify Your Email</h4>
            <p>We've sent a verification email to your address. Please check your inbox and follow the instructions to complete your registration.</p>
            <p>If you don't see the email, please check your spam folder.</p>
        </div>
        """
        messages.info(request, mark_safe(verification_message))

        # Call the parent method to handle the default behavior
        return super().respond_email_verification_sent(request, user)
