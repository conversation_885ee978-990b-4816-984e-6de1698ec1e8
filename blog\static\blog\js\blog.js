document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to blog post cards
    const cards = document.querySelectorAll('.card');
    
    // Create an Intersection Observer
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                // Unobserve after animation is added
                observer.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1 // Trigger when at least 10% of the element is visible
    });

    // Observe each card
    cards.forEach(card => {
        card.style.opacity = '0';
        observer.observe(card);
    });

    // Function to initialize cards that are already in view
    function initializeVisibleCards() {
        cards.forEach(card => {
            const rect = card.getBoundingClientRect();
            const isVisible = (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );

            if (isVisible) {
                card.style.opacity = '1';
                card.classList.add('animate__animated', 'animate__fadeInUp');
                observer.unobserve(card);
            }
        });
    }

    // Initialize visible cards
    initializeVisibleCards();
});
