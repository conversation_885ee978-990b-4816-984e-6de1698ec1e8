<?xml version="1.0" encoding="UTF-8"?>
<svg width="512px" height="512px" viewBox="0 0 512 512" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Coming Soon</title>
    <defs>
        <linearGradient x1="0%" y1="0%" x2="100%" y2="100%" id="linearGradient-1">
            <stop stop-color="#6F42C1" offset="0%"></stop>
            <stop stop-color="#007BFF" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Coming-Soon" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="url(#linearGradient-1)" cx="256" cy="256" r="256" opacity="0.1"></circle>
        <g id="Calendar" transform="translate(96.000000, 96.000000)">
            <rect id="Calendar-Body" stroke="#6F42C1" stroke-width="12" fill="#FFFFFF" x="6" y="36" width="308" height="284" rx="16"></rect>
            <rect id="Calendar-Header" fill="#6F42C1" x="0" y="0" width="320" height="60" rx="16"></rect>
            <g id="Calendar-Dates" transform="translate(40.000000, 84.000000)" fill="#E9ECEF">
                <rect x="0" y="0" width="40" height="40" rx="4"></rect>
                <rect x="60" y="0" width="40" height="40" rx="4"></rect>
                <rect x="120" y="0" width="40" height="40" rx="4"></rect>
                <rect x="180" y="0" width="40" height="40" rx="4"></rect>
                <rect x="240" y="0" width="40" height="40" rx="4"></rect>
                <rect x="0" y="60" width="40" height="40" rx="4"></rect>
                <rect x="60" y="60" width="40" height="40" rx="4"></rect>
                <rect x="120" y="60" width="40" height="40" rx="4"></rect>
                <rect x="180" y="60" width="40" height="40" rx="4"></rect>
                <rect x="240" y="60" width="40" height="40" rx="4"></rect>
                <rect x="0" y="120" width="40" height="40" rx="4"></rect>
                <rect x="60" y="120" width="40" height="40" rx="4"></rect>
            </g>
            <rect id="Highlighted-Date" fill="#007BFF" x="180" y="204" width="40" height="40" rx="4"></rect>
            <rect id="Highlighted-Date-2" fill="#6F42C1" x="240" y="204" width="40" height="40" rx="4"></rect>
        </g>
        <g id="Text" transform="translate(106.000000, 400.000000)" fill="#333333" font-family="Arial-BoldMT, Arial" font-weight="bold">
            <text id="COMING-SOON" font-size="36">
                <tspan x="0" y="33">COMING SOON</tspan>
            </text>
        </g>
        <g id="Stars" transform="translate(96.000000, 64.000000)" fill="#FFD700">
            <polygon id="Star-1" points="32 0 41.9 19.6 64 22.7 48 38 51.8 60 32 49.6 12.2 60 16 38 0 22.7 22.1 19.6"></polygon>
            <polygon id="Star-2" transform="translate(288.000000, 30.000000) rotate(15.000000) translate(-288.000000, -30.000000) " points="288 0 297.9 19.6 320 22.7 304 38 307.8 60 288 49.6 268.2 60 272 38 256 22.7 278.1 19.6"></polygon>
        </g>
    </g>
</svg>
