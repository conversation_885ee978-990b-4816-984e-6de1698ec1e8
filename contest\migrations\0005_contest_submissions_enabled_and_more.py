# Generated by Django 5.1.5 on 2025-04-27 11:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contest', '0004_submission_slug'),
    ]

    operations = [
        migrations.AddField(
            model_name='contest',
            name='submissions_enabled',
            field=models.Bo<PERSON>anField(default=True, help_text='Toggle to manually enable/disable submissions regardless of dates'),
        ),
        migrations.AddField(
            model_name='contest',
            name='submissions_open_date',
            field=models.DateField(blank=True, help_text='Date when submissions can start being accepted', null=True),
        ),
        migrations.AlterField(
            model_name='contest',
            name='end_date',
            field=models.DateField(help_text='Final date for accepting submissions'),
        ),
        migrations.AlterField(
            model_name='contest',
            name='start_date',
            field=models.DateField(help_text='Date when the contest is announced and visible'),
        ),
    ]
