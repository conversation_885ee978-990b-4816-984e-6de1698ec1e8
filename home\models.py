from django.db import models
from django.utils import timezone
from image_cropping import ImageRatioField
from django.contrib.auth.models import User
import json


class CarouselItem(models.Model):
    """
    Model representing items in the homepage carousel.
    """

    STYLE_CHOICES = [
        ('product', 'Product Style'),
        ('blog', 'Blog Style'),
        ('news', 'News Style'),
        ('external', 'External Style'),
    ]

    title = models.CharField(max_length=255)
    subtitle = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to="carousel/")
    cropping = ImageRatioField('image', '16x9')  # Cropping field

    alt_text = models.CharField(
        max_length=255,
        blank=True,
        help_text=(
            "Alternative text for accessibility"
        ),
    )
    style = models.CharField(
        max_length=10,
        choices=STYLE_CHOICES,
        default='external'
    )

    product = models.ForeignKey(
        'works.Product',
        null=True,
        blank=True,
        on_delete=models.SET_NULL
    )
    blog_post = models.ForeignKey(
        'blog.Post',
        null=True,
        blank=True,
        on_delete=models.SET_NULL
    )
    external_link = models.URLField(blank=True, null=True)
    open_in_new_tab = models.BooleanField(default=False)

    start_date = models.DateTimeField(default=timezone.now)
    end_date = models.DateTimeField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    order = models.IntegerField(
        default=0,
        help_text="Order of appearance in carousel"
    )

    cta_text = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="Call to Action button text"
    )
    cta_style = models.CharField(
        max_length=20,
        choices=[
            ('primary', 'Primary Button'),
            ('secondary', 'Secondary Button'),
            ('link', 'Link Style'),
        ],
        default='primary',
        blank=True,
        null=True
    )

    class Meta:
        """
        Meta class for CarouselItem model.
        """
        ordering = ['order', '-start_date']
        verbose_name = "Carousel Item"
        verbose_name_plural = "Carousel Items"

    def get_link(self):
        """
        Get the appropriate URL for the carousel item based on its style.
        """
        if self.style == 'product' and self.product:
            return self.product.get_absolute_url()
        elif self.style == 'blog' and self.blog_post:
            return self.blog_post.get_absolute_url()
        elif self.external_link:
            return self.external_link
        return "#"

    def is_visible(self):
        """
        Check if the carousel item should be visible based'
        ' on dates and active status.
        """
        now = timezone.now()
        return (
            self.is_active and
            (self.start_date <= now) and
            (not self.end_date or self.end_date >= now)
        )

    def __str__(self):
        """
        String representation of the CarouselItem model.
        """
        return f"{self.title} ({self.style})"


class AudioListeningSession(models.Model):
    """
    Represents a single listening session for an audiobook
    """
    session_id = models.CharField(max_length=100, unique=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    anonymous_user_id = models.CharField(max_length=100, null=True, blank=True)
    work_id = models.CharField(max_length=100)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(null=True, blank=True)
    duration_ms = models.IntegerField(default=0)
    initial_position = models.FloatField(default=0)
    last_position = models.FloatField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    completed = models.BooleanField(default=False, help_text="Whether the audiobook was completed in this session")
    progress_percentage = models.IntegerField(default=0, help_text="Percentage of the audiobook listened to")
    
    def __str__(self):
        user_id = self.user.id if self.user else self.anonymous_user_id
        return f"Session {self.session_id} - User {user_id} - Work {self.work_id}"
    
    class Meta:
        ordering = ['-start_time']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['work_id']),
            models.Index(fields=['start_time']),
        ]


class AudioEvent(models.Model):
    """
    Represents a single event during audio playback
    """
    EVENT_TYPES = [
        ('play', 'Play'),
        ('pause', 'Pause'),
        ('ended', 'Ended'),
        ('seeking', 'Seeking'),
        ('speed_change', 'Speed Change'),
        ('position_update', 'Position Update'),
        ('session_start', 'Session Start'),
        ('session_end', 'Session End'),
        ('previous_chapter', 'Previous Chapter'),
        ('next_chapter', 'Next Chapter'),
        ('skip_backward', 'Skip Backward'),
        ('skip_forward', 'Skip Forward'),
        ('progress_bar_click', 'Progress Bar Click'),
        ('chapter_select', 'Chapter Select'),
        ('download_chapter', 'Download Chapter'),
        ('download_remaining', 'Download Remaining'),
        ('download_entire', 'Download Entire'),
        ('download_complete', 'Download Complete'),
        ('download_failed', 'Download Failed'),
        ('page_hidden', 'Page Hidden'),
        ('page_visible', 'Page Visible'),
        ('page_unload', 'Page Unload'),
        ('other', 'Other'),
    ]
    
    event_id = models.CharField(max_length=100, unique=True)
    session = models.ForeignKey(
        AudioListeningSession, 
        on_delete=models.CASCADE,
        related_name='events', 
        null=True, 
        blank=True
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    anonymous_user_id = models.CharField(max_length=100, null=True, blank=True)
    work_id = models.CharField(max_length=100)
    event_type = models.CharField(max_length=50, choices=EVENT_TYPES)
    timestamp = models.DateTimeField()
    playback_position = models.FloatField(default=0)
    url = models.URLField(blank=True, null=True)
    data = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def set_data(self, data_dict):
        self.data = json.dumps(data_dict)
    
    def get_data(self):
        if self.data:
            return json.loads(self.data)
        return {}
    
    def __str__(self):
        user_id = self.user.id if self.user else self.anonymous_user_id
        return f"{self.event_type} - User {user_id} - {self.timestamp}"
    
    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['work_id']),
            models.Index(fields=['event_type']),
            models.Index(fields=['timestamp']),
        ]
