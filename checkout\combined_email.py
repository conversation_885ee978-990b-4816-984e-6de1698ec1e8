"""
Combined email functionality for order confirmation and digital content delivery.
"""
import logging
import os
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.conf import settings

# Import email manager utilities
from email_manager.utils import send_email_with_rate_limit

logger = logging.getLogger(__name__)

def send_combined_order_email(order):
    """
    Send a combined order confirmation and digital content email.

    This function sends a single email that includes both order confirmation
    details and any digital content (ebooks, audiobook links) associated
    with the order.

    Args:
        order: The Order object to send the email for

    Returns:
        bool: True if the email was sent successfully, False otherwise
    """
    customer_email = order.email
    subject = f'Your Order from Tempus Author Platform - #{order.id}'

    # Get order items for the email
    order_items = order.orderitem_set.all()

    # Identify digital items
    digital_items = []
    for item in order_items:
        if item.product.ebook_file or item.product.audiobook_link:
            digital_items.append(item)
            logger.info(f"Found digital content in {item.product.name}")

    # Prepare context for email template
    context = {
        'order': order,
        'order_items': order_items,
        'digital_items': digital_items,
        'has_digital_content': len(digital_items) > 0,
        'contact_email': settings.CONTACT_DISPLAY_EMAIL,
        'SITE_URL': settings.SITE_URL,
    }

    # Render email templates
    html_content = render_to_string(
        'checkout/emails/combined_order_email.html',
        context
    )

    # Create a proper plain text version
    text_content = strip_tags(html_content).replace('&nbsp;', ' ')

    logger.info(f"Sending combined order email for order {order.id} to {customer_email}")

    # Prepare attachments if there are ebook files
    attachments = []
    for item in digital_items:
        if item.product.ebook_file:
            try:
                # Try different methods to get the file path
                try:
                    # Method 1: Direct path access
                    file_path = item.product.ebook_file.path
                except (ValueError, AttributeError) as e:
                    logger.warning(f"Could not get direct path: {str(e)}")

                    # Method 2: Construct path from MEDIA_ROOT and name
                    file_path = os.path.join(settings.MEDIA_ROOT, str(item.product.ebook_file))

                    # Verify file exists
                    if not os.path.exists(file_path):
                        logger.warning(f"File does not exist at constructed path: {file_path}")

                        # Method 3: Try to get the URL and add a note in the email
                        # We'll add a note in the email that files can be downloaded from the library
                        continue

                file_name = os.path.basename(str(item.product.ebook_file))

                attachments.append({
                    'path': file_path,
                    'filename': file_name,
                    'mimetype': 'application/pdf'
                })

                logger.info(f"Added attachment: {file_name}")
            except Exception as e:
                logger.error(f"Error preparing attachment {item.product.ebook_file}: {str(e)}")

    # Log the number of attachments
    logger.info(f"Prepared {len(attachments)} attachments for order {order.id}")

    # Use rate-limited email sending with high priority
    success, message = send_email_with_rate_limit(
        subject=subject,
        recipient=customer_email,
        html_content=html_content,
        text_content=text_content,
        priority=1,  # Highest priority
        content_type='order_confirmation',
        object_id=order.id,
        attachments=attachments if attachments else None
    )

    if success:
        logger.info(f"Combined order email sent successfully for order {order.id}")

        # Update order email status
        order.email_delivery_status = 'sent'
        order.save(update_fields=['email_delivery_status'])

        return True
    else:
        logger.error(f"Failed to send combined order email for order {order.id}: {message}")

        # Update order email status
        order.email_delivery_status = 'failed'
        order.save(update_fields=['email_delivery_status'])

        return False
