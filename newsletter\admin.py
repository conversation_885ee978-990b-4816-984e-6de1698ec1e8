from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Subscriber, Newsletter, EmailTracking, NewsletterTemplate


@admin.register(Subscriber)
class SubscriberAdmin(admin.ModelAdmin):
    list_display = ('email', 'name', 'subscription_status', 'subscription_date', 'is_active')
    list_filter = ('is_confirmed', 'is_active')
    search_fields = ('email', 'name')
    readonly_fields = ('confirmation_token', 'created_at', 'confirmed_at', 'unsubscribed_at')
    actions = ['mark_confirmed', 'mark_unsubscribed']

    def subscription_status(self, obj):
        if not obj.is_active:
            return "Unsubscribed"
        if obj.confirmed_at:
            return "Confirmed via Email"
        if obj.is_confirmed:
            return "Confirmed via Registration"
        return "Pending Confirmation"
    subscription_status.short_description = "Status"

    def subscription_date(self, obj):
        return obj.confirmed_at or obj.created_at
    subscription_date.short_description = "Subscribed Since"

    def mark_confirmed(self, request, queryset):
        for subscriber in queryset:
            subscriber.confirm_subscription()
    mark_confirmed.short_description = "Mark selected subscribers as confirmed"

    def mark_unsubscribed(self, request, queryset):
        for subscriber in queryset:
            subscriber.unsubscribe()
    mark_unsubscribed.short_description = "Unsubscribe selected subscribers"


@admin.register(Newsletter)
class NewsletterAdmin(admin.ModelAdmin):
    list_display = ('subject', 'status', 'created_at', 'scheduled_for', 'sent_at', 'sent_to_count', 'open_count', 'click_count')
    list_filter = ('status', 'created_at', 'sent_at')
    search_fields = ('subject', 'content')
    readonly_fields = ('created_at', 'sent_at', 'sent_to_count', 'open_count', 'click_count', 'preview_link', 'status', 'error_message')
    fieldsets = (
        (None, {
            'fields': ('subject', 'preview_text', 'content')
        }),
        ('Tracking Options', {
            'fields': ('track_opens', 'track_clicks'),
        }),
        ('Scheduling', {
            'fields': ('scheduled_for',),
        }),
        ('Status & Statistics', {
            'fields': ('status', 'error_message', 'sent_to_count', 'open_count', 'click_count', 'preview_link'),
        }),
    )
    actions = ['send_newsletter', 'send_test_email', 'clone_newsletter']

    def preview_link(self, obj):
        if obj.pk:
            url = reverse('newsletter:preview', args=[obj.pk])
            return format_html('<a href="{}" target="_blank">Preview Newsletter</a>', url)
        return "Save first to preview"
    preview_link.short_description = "Preview"

    def send_newsletter(self, request, queryset):
        for newsletter in queryset:
            if newsletter.status == 'draft':
                if newsletter.scheduled_for and newsletter.scheduled_for > timezone.now():
                    newsletter.status = 'scheduled'
                    newsletter.save()
                else:
                    success, message = newsletter.send_to_all()
                    self.message_user(request, f"Newsletter '{newsletter.subject}': {message}")
            else:
                self.message_user(request, f"Newsletter '{newsletter.subject}' is not in draft status.")
    send_newsletter.short_description = "Send selected newsletters"

    def send_test_email(self, request, queryset):
        from django.contrib.auth import get_user_model
        User = get_user_model()
        admin_user = User.objects.filter(is_superuser=True).first()
        
        if admin_user:
            test_subscriber = Subscriber.objects.get_or_create(
                email=admin_user.email,
                defaults={'name': admin_user.get_full_name() or admin_user.username}
            )[0]
            
            for newsletter in queryset:
                success, error = newsletter.send_to_subscriber(test_subscriber)
                if success:
                    self.message_user(request, f"Test email sent successfully for '{newsletter.subject}'")
                else:
                    self.message_user(request, f"Error sending test email for '{newsletter.subject}': {error}")
        else:
            self.message_user(request, "No admin user found to send test email to.")
    send_test_email.short_description = "Send test email to admin"

    def clone_newsletter(self, request, queryset):
        for newsletter in queryset:
            # Create a new newsletter with the same content
            new_newsletter = Newsletter.objects.create(
                subject=f"Copy of {newsletter.subject}",
                content=newsletter.content,
                preview_text=newsletter.preview_text,
                track_opens=newsletter.track_opens,
                track_clicks=newsletter.track_clicks,
                status='draft'  # Always start as draft
            )
            self.message_user(request, f"Created copy of '{newsletter.subject}'")
    clone_newsletter.short_description = "Clone selected newsletters"


@admin.register(EmailTracking)
class EmailTrackingAdmin(admin.ModelAdmin):
    list_display = ('subscriber', 'newsletter', 'sent_at', 'opened_at', 'clicked_at', 'click_count')
    list_filter = ('sent_at', 'opened_at', 'clicked_at')
    search_fields = ('subscriber__email', 'newsletter__subject')
    readonly_fields = ('tracking_token', 'sent_at', 'opened_at', 'clicked_at', 'click_count')


@admin.register(NewsletterTemplate)
class NewsletterTemplateAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'created_at', 'updated_at')
    search_fields = ('name', 'description', 'subject_template', 'content_template')
    actions = ['create_newsletter_from_template']

    def create_newsletter_from_template(self, request, queryset):
        for template in queryset:
            newsletter = template.create_newsletter()
            self.message_user(request, f"Created newsletter from template '{template.name}'")
    create_newsletter_from_template.short_description = "Create newsletter from selected templates"
