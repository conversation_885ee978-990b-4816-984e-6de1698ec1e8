from django.apps import AppConfig


class EmailManagerConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'email_manager'
    verbose_name = 'Email Management'

    def ready(self):
        """
        Initialize the app when it's ready.
        We'll avoid database access during initialization to prevent warnings.
        """
        # Import signals or other non-database initialization here if needed
        pass
