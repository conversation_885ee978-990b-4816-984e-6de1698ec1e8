{% extends "contest/base.html" %}
{% load static %}
{% load contest_filters %}

{% block contest_title %}{{ submission.title }}{% endblock %}

{% block extra_css %}
{{ block.super }}
<link rel="stylesheet" href="{% static 'contest/css/image-viewer.css' %}?v={% now 'U' %}">
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script src="{% static 'contest/js/image-viewer.js' %}?v={% now 'U' %}"></script>
<script>
    // Function to copy share link to clipboard (desktop version)
    function copyShareLink() {
        const shareUrl = document.getElementById('share-url');
        shareUrl.select();
        shareUrl.setSelectionRange(0, 99999); // For mobile devices

        navigator.clipboard.writeText(shareUrl.value)
            .then(() => {
                // Change button text/icon temporarily to show success
                const copyBtn = document.getElementById('copy-link-btn');
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.classList.remove('btn-outline-secondary');
                copyBtn.classList.add('btn-success');

                // Show tooltip or message
                const messageContainer = document.getElementById('message-container');
                messageContainer.innerHTML = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        Link copied to clipboard! Share it to help the artist get more votes.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Reset button after 2 seconds
                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.classList.remove('btn-success');
                    copyBtn.classList.add('btn-outline-secondary');
                }, 2000);
            })
            .catch(err => {
                console.error('Could not copy text: ', err);
                alert('Could not copy link. Please try again or copy it manually.');
            });
    }

    // Function to copy share link to clipboard (mobile version)
    function copyShareLinkMobile() {
        const shareUrl = document.getElementById('share-url-mobile').value;

        navigator.clipboard.writeText(shareUrl)
            .then(() => {
                // Change button text/icon temporarily to show success
                const copyBtn = document.getElementById('copy-link-btn-mobile');
                const originalHTML = copyBtn.innerHTML;
                copyBtn.innerHTML = '<i class="fas fa-check"></i>';
                copyBtn.classList.remove('btn-outline-secondary');
                copyBtn.classList.add('btn-success');

                // Show tooltip or message
                const messageContainer = document.getElementById('message-container');
                messageContainer.innerHTML = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        Link copied to clipboard!
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;

                // Reset button after 2 seconds
                setTimeout(() => {
                    copyBtn.innerHTML = originalHTML;
                    copyBtn.classList.remove('btn-success');
                    copyBtn.classList.add('btn-outline-secondary');
                }, 2000);
            })
            .catch(err => {
                console.error('Could not copy text: ', err);
                alert('Could not copy link. Please try again or copy it manually.');
            });
    }

    // Function for mobile share using Web Share API
    function mobileShare() {
        // Check if Web Share API is supported
        if (navigator.share) {
            navigator.share({
                title: '{{ submission.title }} - TempusQuest Contest',
                text: 'Check out this amazing artwork for #TempusQuest by {{ submission.user.username }}!',
                url: document.getElementById('share-url').value
            })
            .then(() => {
                console.log('Successfully shared');
                // Show success message
                const messageContainer = document.getElementById('message-container');
                messageContainer.innerHTML = `
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        Thanks for sharing! Your support helps the artist get more votes.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            })
            .catch(error => console.log('Error sharing:', error));
        } else {
            // Fallback for browsers that don't support the Web Share API
            alert('Web Share API not supported on this browser. Please use the social sharing buttons below.');
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Like/Unlike functionality
        const likeBtn = document.getElementById('like-btn');
        const unlikeBtn = document.getElementById('unlike-btn');
        const likeCount = document.getElementById('like-count');

        if (likeBtn) {
            likeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                fetch(likeBtn.getAttribute('data-url'), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        likeCount.textContent = data.likes_count;
                        likeBtn.classList.add('d-none');
                        unlikeBtn.classList.remove('d-none');

                        // Show success message
                        const messageContainer = document.getElementById('message-container');
                        messageContainer.innerHTML = `
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        }

        if (unlikeBtn) {
            unlikeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                fetch(unlikeBtn.getAttribute('data-url'), {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        likeCount.textContent = data.likes_count;
                        unlikeBtn.classList.add('d-none');
                        likeBtn.classList.remove('d-none');

                        // Show success message
                        const messageContainer = document.getElementById('message-container');
                        messageContainer.innerHTML = `
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                    }
                })
                .catch(error => console.error('Error:', error));
            });
        }
    });
</script>
{% endblock %}

{% block contest_content %}
<div id="message-container">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        {% endfor %}
    {% endif %}
</div>

<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center flex-wrap">
                <h2 class="card-title mb-0">{{ submission.title }}</h2>
                <div class="nav-buttons mt-2 mt-md-0">
                    <a href="{% url 'contest:contest_detail' slug=submission.contest.slug %}" class="btn btn-nav me-2">
                        <i class="fas fa-trophy me-1"></i> Back to Contest
                    </a>
                    <a href="{% url 'contest:submission_gallery' slug=submission.contest.slug %}" class="btn btn-nav">
                        <i class="fas fa-images me-1"></i> Back to Gallery
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="submission-image-container text-center mb-4">
                            {% if submission.file.url|lower|slice:"-4:" == '.jpg' or submission.file.url|lower|slice:"-5:" == '.jpeg' or submission.file.url|lower|slice:"-4:" == '.png' %}
                                <div class="protected-image position-relative">
                                    <img src="{{ submission.file.url }}" alt="{{ submission.title }}" class="img-fluid rounded" oncontextmenu="return false;">
                                </div>
                            {% elif submission.file.url|lower|slice:"-4:" == '.obj' or submission.file.url|lower|slice:"-5:" == '.gltf' %}
                                <div class="model-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <div class="text-center">
                                        <i class="fas fa-cube fa-5x text-muted"></i>
                                        <p class="mt-3">3D Model ({{ submission.file.url|slice:"-4:"|upper }})</p>
                                        <a href="{{ submission.file.url }}" class="btn btn-primary" download>Download Model</a>
                                    </div>
                                </div>
                            {% else %}
                                <div class="file-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 400px;">
                                    <div class="text-center">
                                        <i class="fas fa-file fa-5x text-muted"></i>
                                        <p class="mt-3">File: {{ submission.file.url|cut:"/" }}</p>
                                        <a href="{{ submission.file.url }}" class="btn btn-primary" download>Download File</a>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <div class="submission-description mb-4">
                            <h3>Description</h3>
                            <div class="card">
                                <div class="card-body">
                                    <p>{{ submission.description|urlize }}</p>
                                </div>
                            </div>
                        </div>

                        {% if "3D" in submission.category.name %}
                        <div class="submission-3d-model mb-4">
                            <h3>3D Model</h3>
                            {% if submission.model_3d_url %}
                                <div class="card">
                                    <div class="card-body">
                                        {% if "sketchfab.com" in submission.model_3d_url %}
                                            <!-- Embed Sketchfab viewer -->
                                            <div class="ratio ratio-16x9 mb-3">
                                                <!-- Debug info to help troubleshoot -->
                                                <div class="alert alert-info mb-2">
                                                    <p><strong>Original URL:</strong> {{ submission.model_3d_url }}</p>
                                                </div>

                                                <!-- Convert regular URL to embed URL -->
                                                {% if "/models/" in submission.model_3d_url and "/embed" in submission.model_3d_url %}
                                                    <!-- Already an embed URL -->
                                                    <iframe
                                                        title="{{ submission.title }} - 3D Model"
                                                        src="{{ submission.model_3d_url }}"
                                                        frameborder="0"
                                                        allow="autoplay; fullscreen; xr-spatial-tracking; accelerometer"
                                                        allowfullscreen
                                                        mozallowfullscreen="true"
                                                        webkitallowfullscreen="true"
                                                    ></iframe>
                                                {% elif "/3d-models/" in submission.model_3d_url %}
                                                    <!-- Extract model ID using our custom filter -->
                                                    {% with model_id=submission.model_3d_url|extract_sketchfab_id %}
                                                        {% if model_id %}
                                                            <iframe
                                                                title="{{ submission.title }} - 3D Model"
                                                                src="https://sketchfab.com/models/{{ model_id }}/embed?autostart=0&ui_controls=1&ui_infos=1&ui_inspector=1&ui_stop=0&ui_watermark=1&ui_watermark_link=1"
                                                                frameborder="0"
                                                                allow="autoplay; fullscreen; xr-spatial-tracking; accelerometer"
                                                                allowfullscreen
                                                                mozallowfullscreen="true"
                                                                webkitallowfullscreen="true"
                                                            ></iframe>
                                                        {% else %}
                                                            <!-- Fallback to a known working model for testing -->
                                                            <iframe
                                                                title="{{ submission.title }} - 3D Model"
                                                                src="https://sketchfab.com/models/a575b36467d840be84d8f91d5074d7d4/embed?autostart=0&ui_controls=1&ui_infos=1&ui_inspector=1&ui_stop=0&ui_watermark=1&ui_watermark_link=1"
                                                                frameborder="0"
                                                                allow="autoplay; fullscreen; xr-spatial-tracking; accelerometer"
                                                                allowfullscreen
                                                                mozallowfullscreen="true"
                                                                webkitallowfullscreen="true"
                                                            ></iframe>
                                                            <div class="alert alert-warning mt-2">
                                                                <p><strong>Note:</strong> Could not extract model ID from URL. Using a test model instead.</p>
                                                            </div>
                                                        {% endif %}
                                                    {% endwith %}
                                                {% else %}
                                                    <!-- Try to extract model ID from unknown format -->
                                                    {% with model_id=submission.model_3d_url|extract_sketchfab_id %}
                                                        {% if model_id %}
                                                            <iframe
                                                                title="{{ submission.title }} - 3D Model"
                                                                src="https://sketchfab.com/models/{{ model_id }}/embed?autostart=0&ui_controls=1&ui_infos=1&ui_inspector=1&ui_stop=0&ui_watermark=1&ui_watermark_link=1"
                                                                frameborder="0"
                                                                allow="autoplay; fullscreen; xr-spatial-tracking; accelerometer"
                                                                allowfullscreen
                                                                mozallowfullscreen="true"
                                                                webkitallowfullscreen="true"
                                                            ></iframe>
                                                        {% else %}
                                                            <!-- Unknown format, try direct embedding -->
                                                            <iframe
                                                                title="{{ submission.title }} - 3D Model"
                                                                src="{{ submission.model_3d_url }}"
                                                                frameborder="0"
                                                                allow="autoplay; fullscreen; xr-spatial-tracking; accelerometer"
                                                                allowfullscreen
                                                                mozallowfullscreen="true"
                                                                webkitallowfullscreen="true"
                                                            ></iframe>
                                                        {% endif %}
                                                    {% endwith %}
                                                {% endif %}
                                            </div>
                                            <div class="alert alert-info mt-2">
                                                <i class="fas fa-info-circle me-2"></i> If the 3D model doesn't load correctly, you can <a href="{{ submission.model_3d_url }}" target="_blank">view it directly on Sketchfab <i class="fas fa-external-link-alt"></i></a>
                                            </div>
                                        {% else %}
                                            <p>View the 3D model: <a href="{{ submission.model_3d_url }}" target="_blank" class="btn btn-primary btn-sm">
                                                <i class="fas fa-cube me-1"></i> Open 3D Model <i class="fas fa-external-link-alt ms-1"></i>
                                            </a></p>
                                            <p class="text-muted small">The 3D model is hosted on an external platform and will open in a new tab.</p>
                                        {% endif %}
                                    </div>
                                </div>
                            {% else %}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> This is a 3D model submission, but the artist has not provided a link to the 3D model.
                                </div>
                            {% endif %}
                        </div>
                        {% endif %}

                        <div class="submission-review mb-4">
                            <h3 class="h4">Book Review</h3>
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center py-2">
                                    <span>Rating:</span>
                                    <div class="rating">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= submission.rating %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="card-body py-3">
                                    <blockquote class="blockquote mb-0">
                                        <p class="mb-2" style="font-size: 0.95rem;">{{ submission.review_text }}</p>
                                        <footer class="blockquote-footer text-end mt-2">
                                            Inspired by <a href="{% url 'work_detail' work_id=submission.book_part.id %}" title="View book details"><cite>{{ submission.book_part.name }}</cite></a>
                                        </footer>
                                    </blockquote>
                                </div>
                            </div>
                        </div>

                        <div class="submission-fragment mb-4">
                            <h3 class="h4">Book Fragment</h3>
                            <div class="card">
                                <div class="card-header py-2">
                                    {% if "Part" in submission.chapter_reference %}
                                        {{ submission.chapter_reference|cut:"Part 1: The Origin - "|cut:"Part 2: The Scrutiny - "|cut:"Part 3: The Tempus - " }}
                                    {% else %}
                                        {{ submission.chapter_reference }}
                                    {% endif %}
                                </div>
                                <div class="card-body py-3">
                                    <blockquote class="blockquote mb-0">
                                        <p class="mb-0 fst-italic" style="font-size: 0.95rem;">{{ submission.book_fragment }}</p>
                                    </blockquote>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h3 class="card-title mb-0">Submission Details</h3>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Artist
                                        <span>{{ submission.user.username }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Category
                                        <span>{{ submission.category.name }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Submitted
                                        <span>{{ submission.submitted_at|date:"F j, Y" }}</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Status
                                        {% if submission.is_approved %}
                                            <span class="badge bg-success">Approved</span>
                                        {% else %}
                                            <span class="badge bg-warning text-dark">Pending</span>
                                        {% endif %}
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        Likes
                                        <span id="like-count" class="badge bg-primary rounded-pill">{{ submission.total_likes }}</span>
                                    </li>
                                    {% if submission.ai_tools_used %}
                                    <li class="list-group-item">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span>AI Tools Used</span>
                                            <span class="badge bg-info">Yes</span>
                                        </div>
                                        {% if submission.ai_tools_details %}
                                        <div class="mt-2 small">
                                            <strong>Details:</strong><br>
                                            {{ submission.ai_tools_details }}
                                        </div>
                                        {% endif %}
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>

                        <!-- Share Card -->
                        <div class="card mb-4">
                            <div class="card-header bg-purple text-white" style="background-color: #6f42c1;">
                                <h3 class="card-title mb-0">Share This Artwork</h3>
                            </div>
                            <div class="card-body">
                                <p class="small text-muted mb-3">Share this artwork to help the artist get more votes! Use hashtag <strong>#TempusQuest</strong> when sharing.</p>

                                <!-- Desktop Layout (visible on screens >= 992px) -->
                                <div class="d-none d-lg-block">
                                    <!-- Copy Link Button -->
                                    <div class="input-group mb-3">
                                        <input type="text" id="share-url" class="form-control" value="{{ request.build_absolute_uri }}" readonly>
                                        <button class="btn btn-outline-secondary" type="button" id="copy-link-btn" onclick="copyShareLink()">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>

                                    <!-- Social Share Buttons -->
                                    <div class="d-flex justify-content-between">
                                        <!-- Facebook -->
                                        <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri|urlencode }}"
                                           target="_blank" class="btn btn-outline-primary" title="Share on Facebook">
                                            <i class="fab fa-facebook-f"></i>
                                        </a>

                                        <!-- Twitter -->
                                        <a href="https://twitter.com/intent/tweet?text=Check out this amazing artwork for %23TempusQuest!&url={{ request.build_absolute_uri|urlencode }}"
                                           target="_blank" class="btn btn-outline-info" title="Share on Twitter">
                                            <i class="fab fa-twitter"></i>
                                        </a>

                                        <!-- Pinterest -->
                                        <a href="https://pinterest.com/pin/create/button/?url={{ request.build_absolute_uri|urlencode }}&media={{ request.scheme }}://{{ request.get_host }}{{ submission.file.url|urlencode }}&description=Amazing artwork by {{ submission.user.username }} for %23TempusQuest"
                                           target="_blank" class="btn btn-outline-danger" title="Pin on Pinterest">
                                            <i class="fab fa-pinterest-p"></i>
                                        </a>

                                        <!-- WhatsApp -->
                                        <a href="https://api.whatsapp.com/send?text=Check out this amazing artwork for %23TempusQuest! {{ request.build_absolute_uri|urlencode }}"
                                           target="_blank" class="btn btn-outline-success" title="Share on WhatsApp">
                                            <i class="fab fa-whatsapp"></i>
                                        </a>

                                        <!-- Email -->
                                        <a href="mailto:?subject=Amazing artwork for TempusQuest&body=Check out this amazing artwork for the TempusQuest contest: {{ request.build_absolute_uri|urlencode }}"
                                           class="btn btn-outline-secondary" title="Share via Email">
                                            <i class="fas fa-envelope"></i>
                                        </a>
                                    </div>
                                </div>

                                <!-- Mobile Layout (visible on screens < 992px) -->
                                <div class="d-lg-none">
                                    <div class="d-flex justify-content-between mb-3">
                                        <!-- Mobile Share Button (uses Web Share API) -->
                                        <button id="mobile-share-btn" class="btn btn-purple flex-grow-1 me-2" style="background-color: #6f42c1; color: white;" onclick="mobileShare()">
                                            <i class="fas fa-share-alt me-2"></i> Share
                                        </button>

                                        <!-- Copy Link Button (simplified for mobile) -->
                                        <button class="btn btn-outline-secondary" type="button" id="copy-link-btn-mobile" onclick="copyShareLinkMobile()">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>

                                    <!-- Hidden input for mobile copy functionality -->
                                    <input type="hidden" id="share-url-mobile" value="{{ request.build_absolute_uri }}">
                                </div>
                            </div>
                        </div>

                        {% if user.is_authenticated and user != submission.user %}
                            <div class="card mb-4">
                                <div class="card-header bg-success text-white">
                                    <h3 class="card-title mb-0">Vote</h3>
                                </div>
                                <div class="card-body text-center">
                                    {% if user_liked %}
                                        <button id="unlike-btn" class="btn btn-danger" data-url="{% url 'contest:submission_unlike' slug=submission.slug %}">
                                            <i class="fas fa-thumbs-down"></i> Remove Vote
                                        </button>
                                        <button id="like-btn" class="btn btn-success d-none" data-url="{% url 'contest:submission_like' slug=submission.slug %}">
                                            <i class="fas fa-thumbs-up"></i> Vote for This Submission
                                        </button>
                                    {% else %}
                                        <button id="like-btn" class="btn btn-success" data-url="{% url 'contest:submission_like' slug=submission.slug %}">
                                            <i class="fas fa-thumbs-up"></i> Vote for This Submission
                                        </button>
                                        <button id="unlike-btn" class="btn btn-danger d-none" data-url="{% url 'contest:submission_unlike' slug=submission.slug %}">
                                            <i class="fas fa-thumbs-down"></i> Remove Vote
                                        </button>
                                    {% endif %}
                                    <p class="mt-3 small text-muted">Remember: You need to vote for at least 3 submissions to be eligible for the grand prize!</p>
                                </div>
                            </div>
                        {% endif %}

                        {% if user.is_authenticated and user == submission.user %}
                            <div class="card mb-4">
                                <div class="card-header bg-secondary text-white">
                                    <h3 class="card-title mb-0">Manage Submission</h3>
                                </div>
                                <div class="card-body text-center">
                                    <div class="d-grid gap-2">
                                        <a href="{% url 'contest:submission_edit' slug=submission.slug %}" class="btn btn-warning">
                                            <i class="fas fa-edit"></i> Edit Submission
                                        </a>
                                        <a href="{% url 'contest:submission_delete' slug=submission.slug %}" class="btn btn-danger">
                                            <i class="fas fa-trash"></i> Delete Submission
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                        {% if user.is_staff %}
                            <div class="card mb-4">
                                <div class="card-header bg-dark text-white">
                                    <h3 class="card-title mb-0">Jury Actions</h3>
                                </div>
                                <div class="card-body text-center">
                                    <a href="{% url 'contest:jury_score_submission' slug=submission.slug %}" class="btn btn-primary">
                                        <i class="fas fa-star"></i> Score Submission
                                    </a>

                                    {% if jury_score %}
                                        <div class="mt-3">
                                            <p>Your score: <strong>{{ jury_score.points }}</strong>/100</p>
                                            {% if jury_score.comment %}
                                                <p>Comment: {{ jury_score.comment }}</p>
                                            {% endif %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Navigation Controls -->
                <div class="row mt-4 mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body py-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        {% if prev_submission %}
                                            <a href="{% url 'contest:submission_detail' slug=prev_submission.slug %}" class="btn btn-outline-primary">
                                                <i class="fas fa-chevron-left me-2"></i> Previous Submission
                                            </a>
                                        {% else %}
                                            <button class="btn btn-outline-secondary" disabled>
                                                <i class="fas fa-chevron-left me-2"></i> Previous Submission
                                            </button>
                                        {% endif %}
                                    </div>

                                    <div class="text-center">
                                        <a href="{% url 'contest:submission_gallery' slug=submission.contest.slug %}" class="btn btn-outline-dark mx-2">
                                            <i class="fas fa-th me-1"></i> Gallery
                                        </a>
                                        <div class="btn-group mx-2">
                                            <a href="{% url 'contest:submission_gallery' slug=submission.contest.slug %}?sort=likes" class="btn btn-outline-secondary">
                                                <i class="fas fa-thumbs-up"></i> Most Liked
                                            </a>
                                            <a href="{% url 'contest:submission_gallery' slug=submission.contest.slug %}?sort=date" class="btn btn-outline-secondary">
                                                <i class="fas fa-calendar-alt"></i> Newest
                                            </a>
                                        </div>
                                        {% if submission.category %}
                                            <a href="{% url 'contest:submission_gallery' slug=submission.contest.slug %}?category={{ submission.category.name }}" class="btn btn-outline-info mx-2">
                                                <i class="fas fa-tag me-1"></i> {{ submission.category.name }}
                                            </a>
                                        {% endif %}
                                    </div>

                                    <div>
                                        {% if next_submission %}
                                            <a href="{% url 'contest:submission_detail' slug=next_submission.slug %}" class="btn btn-outline-primary">
                                                Next Submission <i class="fas fa-chevron-right ms-2"></i>
                                            </a>
                                        {% else %}
                                            <button class="btn btn-outline-secondary" disabled>
                                                Next Submission <i class="fas fa-chevron-right ms-2"></i>
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h3 class="card-title mb-0">Comments</h3>
                            </div>
                            <div class="card-body">
                                {% if user.is_authenticated %}
                                    <div class="comment-form mb-4">
                                        <form method="POST">
                                            {% csrf_token %}
                                            <div class="form-group">
                                                {{ comment_form.content }}
                                            </div>
                                            <button type="submit" class="btn btn-primary mt-2">Post Comment</button>
                                        </form>
                                    </div>
                                {% else %}
                                    <div class="alert alert-info">
                                        <a href="{% url 'account_login' %}">Log in</a> to leave a comment.
                                    </div>
                                {% endif %}

                                <div class="comments-list">
                                    {% for comment in comments %}
                                        <div class="comment card mb-3">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between">
                                                    <h5 class="card-title">{{ comment.user.username }}</h5>
                                                    <small class="text-muted">{{ comment.created_at|date:"F j, Y, g:i a" }}</small>
                                                </div>
                                                <p class="card-text">{{ comment.content }}</p>
                                            </div>
                                        </div>
                                    {% empty %}
                                        <div class="alert alert-light">
                                            No comments yet. Be the first to comment!
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
