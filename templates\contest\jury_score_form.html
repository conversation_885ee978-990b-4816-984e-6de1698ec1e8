{% extends "contest/base.html" %}
{% load static %}
{% load crispy_forms_tags %}

{% block contest_title %}Score Submission - {{ submission.title }}{% endblock %}

{% block contest_content %}
<div class="row">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h2 class="card-title mb-0">Score Submission</h2>
                <a href="{% url 'contest:submission_detail' slug=submission.slug %}" class="btn btn-light btn-sm">Back to Submission</a>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h3>{{ submission.title }}</h3>
                        <p>By: {{ submission.user.username }}</p>
                        <p>Category: {{ submission.category.name }}</p>

                        <div class="submission-image-container text-center mb-4">
                            {% if submission.file.url|lower|slice:"-4:" == '.jpg' or submission.file.url|lower|slice:"-5:" == '.jpeg' or submission.file.url|lower|slice:"-4:" == '.png' %}
                                <img src="{{ submission.file.url }}" alt="{{ submission.title }}" class="img-fluid rounded">
                            {% elif submission.file.url|lower|slice:"-4:" == '.obj' or submission.file.url|lower|slice:"-5:" == '.gltf' %}
                                <div class="model-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <div class="text-center">
                                        <i class="fas fa-cube fa-5x text-muted"></i>
                                        <p class="mt-3">3D Model ({{ submission.file.url|slice:"-4:"|upper }})</p>
                                        <a href="{{ submission.file.url }}" class="btn btn-primary" download>Download Model</a>
                                    </div>
                                </div>
                            {% else %}
                                <div class="file-placeholder bg-light d-flex align-items-center justify-content-center" style="height: 300px;">
                                    <div class="text-center">
                                        <i class="fas fa-file fa-5x text-muted"></i>
                                        <p class="mt-3">File: {{ submission.file.url|cut:"/" }}</p>
                                        <a href="{{ submission.file.url }}" class="btn btn-primary" download>Download File</a>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <div class="submission-description mb-4">
                            <h4>Description</h4>
                            <p>{{ submission.description }}</p>
                        </div>

                        <div class="submission-fragment mb-4">
                            <h4>Book Fragment</h4>
                            <div class="card">
                                <div class="card-header">
                                    {{ submission.chapter_reference }}
                                </div>
                                <div class="card-body">
                                    <blockquote class="blockquote">
                                        <p class="mb-0">{{ submission.book_fragment }}</p>
                                    </blockquote>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <h3 class="card-title mb-0">Jury Scoring</h3>
                            </div>
                            <div class="card-body">
                                <form method="POST" class="jury-score-form">
                                    {% csrf_token %}

                                    <div class="alert alert-info">
                                        <p><strong>Scoring Guidelines:</strong></p>
                                        <ul>
                                            <li>Each jury member can award up to 100 points to a submission.</li>
                                            <li>Consider originality, technical skill, and relevance to the book.</li>
                                            <li>You can distribute your points among up to three finalists.</li>
                                            <li>Once submitted, scores cannot be changed.</li>
                                        </ul>
                                    </div>

                                    {{ form|crispy }}

                                    <div class="text-center mt-4">
                                        <button type="submit" class="btn btn-success btn-lg">Submit Score</button>
                                        <a href="{% url 'contest:jury_dashboard' slug=submission.contest.slug %}" class="btn btn-secondary btn-lg">Cancel</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
