from django.contrib import admin
from .models import Category, Product, Review, Genre, Language, File_format, ProductAttachment


class CategoryAdmin(admin.ModelAdmin):
    """
    Admin interface for managing product categories.
    """
    list_display = (
        'friendly_name',
        'name',
    )


@admin.register(Genre)
class GenreAdmin(admin.ModelAdmin):
    """
    Admin interface for managing genres.
    """
    list_display = ('name',)
    search_fields = ('name',)


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    """
    Admin interface for managing languages.
    """
    list_display = ('name',)
    search_fields = ('name',)


@admin.register(File_format)
class File_formatAdmin(admin.ModelAdmin):
    """
    Admin interface for managing languages.
    """
    list_display = ('name',)
    search_fields = ('name',)


class ProductAttachmentInline(admin.TabularInline):
    """
    Inline admin interface for managing product attachments.
    """
    model = ProductAttachment
    extra = 1
    fields = ('name', 'description', 'attachment_type', 'file', 'link', 'is_promotional')


class ProductAdmin(admin.ModelAdmin):
    """
    Admin interface for managing products/works.
    """
    list_display = (
        'name',
        'category',
        'price',
        'book_part',
        'pages',
        'duration',
        'language',
    )
    readonly_fields = (
        'created_date',
        'updated_date',
    )
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'category', 'price', 'description')
        }),
        ('Book Part Information', {
            'fields': ('book_part', 'related_products'),
            'description': 'Identify which part of the book this product represents and link related formats'
        }),
        ('Work Details', {
            'fields': (
                'pages', 'duration', 'genre', 'language',
                'file_format', 'excerpt'
            )
        }),
        ('Media', {
            'fields': ('image', 'image_url', 'ebook_file', 'audiobook_link', 'audiobook_sample_link')
        }),
        ('Timestamps', {
            'fields': ('created_date', 'updated_date'),
            'classes': ('collapse',)
        })
    )
    inlines = [ProductAttachmentInline]
    list_filter = (
        'category',
        'book_part',
        'created_date',
        'language',
        'file_format',
    )
    ordering = (
        'category',
        'name',
        '-created_date',
        '-updated_date',
    )
    search_fields = (
        'name',
        'description',
        'friendly_name',
    )


@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    """
    Admin interface for managing product reviews.
    """
    list_display = ('product', 'user', 'rating', 'created_date', 'approved')
    list_filter = ('approved', 'created_date', 'rating')
    search_fields = ('product__name', 'user__username', 'comment')
    actions = ['approve_reviews']

    def approve_reviews(self, request, queryset):
        """
        Bulk action to approve selected reviews.
        """
        queryset.update(approved=True)
    approve_reviews.short_description = "Approve selected reviews"


@admin.register(ProductAttachment)
class ProductAttachmentAdmin(admin.ModelAdmin):
    """
    Admin interface for managing product attachments.
    """
    list_display = ('name', 'product', 'attachment_type', 'is_promotional', 'created_date')
    list_filter = ('attachment_type', 'is_promotional', 'created_date')
    search_fields = ('name', 'description', 'product__name')
    readonly_fields = ('created_date', 'updated_date')
    fieldsets = (
        ('Basic Information', {
            'fields': ('product', 'name', 'description', 'is_promotional')
        }),
        ('Attachment Details', {
            'fields': ('attachment_type', 'file', 'link')
        }),
        ('Timestamps', {
            'fields': ('created_date', 'updated_date'),
            'classes': ('collapse',)
        })
    )


admin.site.register(Category, CategoryAdmin)
admin.site.register(Product, ProductAdmin)
