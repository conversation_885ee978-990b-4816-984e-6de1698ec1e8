# Generated by Django 5.1.5 on 2025-04-29 18:11

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('works', '0014_fix_download_link_expiry_days'),
    ]

    operations = [
        migrations.AddField(
            model_name='product',
            name='book_part',
            field=models.CharField(choices=[('full', 'Full Book'), ('part1', 'Part 1: The Origin'), ('part2', 'Part 2: The Scrutiny'), ('part3', 'Part 3: The Tempus'), ('other', 'Other')], default='other', help_text='Which part of the book this product represents', max_length=10),
        ),
        migrations.AddField(
            model_name='product',
            name='related_products',
            field=models.ManyToManyField(blank=True, help_text='Other formats of the same book part (e.g., audiobook version of an ebook)', to='works.product'),
        ),
    ]
