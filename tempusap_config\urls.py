from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.http import JsonResponse
from tempusap.views import service_worker, manifest, offline_page
from django.contrib.sitemaps.views import sitemap
from .sitemap import sitemaps

# Simple stub API view for works/chapters
def stub_chapters_api(request, work_id):
    # Return a sample list of chapters
    return JsonResponse({
        'chapters': [
            {
                'id': 1,
                'title': 'Chapter 1: Introduction',
                'duration': 360,  # 6 minutes
                'file_url': 'https://example.com/audio/chapter1.mp3',
                'order': 1
            },
            {
                'id': 2,
                'title': 'Chapter 2: Getting Started',
                'duration': 480,  # 8 minutes
                'file_url': 'https://example.com/audio/chapter2.mp3',
                'order': 2
            },
            {
                'id': 3,
                'title': 'Chapter 3: Advanced Topics',
                'duration': 720,  # 12 minutes
                'file_url': 'https://example.com/audio/chapter3.mp3',
                'order': 3
            }
        ]
    })

# Simple stub API view for works
def stub_works_api(request, work_id):
    # Return sample work details
    return JsonResponse({
        'id': work_id,
        'title': 'Sample Audiobook',
        'author': 'John Doe',
        'description': 'This is a sample audiobook for testing purposes.',
        'duration': 1560,  # 26 minutes total
        'cover_image': 'https://example.com/covers/sample.jpg',
        'publication_date': '2025-01-15',
        'narrator': 'Jane Smith',
        'publisher': 'Tempus Publishing',
        'language': 'English',
        'rating': 4.5,
        'total_chapters': 3
    })

# Simple stub API view for analytics
def stub_analytics_api(request):
    if request.method == 'POST':
        # Just return success without processing the data
        # This avoids any potential errors from trying to process the data
        return JsonResponse({
            'status': 'success',
            'message': 'Analytics data received'
        })
    return JsonResponse({
        'status': 'error',
        'message': 'Method not allowed'
    }, status=405)

urlpatterns = [
    path('admin/', admin.site.urls),
    # Custom admin views
    path('admin/contest/user-eligibility/',
         admin.site.admin_view(
             __import__('contest.admin').admin.UserEligibilityAdmin(
                 model=__import__('contest.models').models.Like,
                 admin_site=admin.site
             ).changelist_view
         ),
         name='user-eligibility'),
    path('', include('home.urls')),
    path('works/', include('works.urls')),
    path('checkout/', include('checkout.urls')),
    path('profile/', include('profiles.urls')),
    path('about/', include('about.urls')),
    path('blog/', include('blog.urls')),
    path('newsletter/', include('newsletter.urls')),
    path('contest/', include('contest.urls')),
    path('accounts/', include('allauth.urls')),
    path('select2/', include('django_select2.urls')),
    path('serviceworker.js', service_worker, name='serviceworker'),
    path('manifest.json', manifest, name='manifest'),
    path('offline/', offline_page, name='offline'),

    # Sitemap
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),

    # API stub endpoints
    path('api/works/<int:work_id>/', stub_works_api, name='api_works'),
    path(
        'api/works/<int:work_id>/chapters/',
        stub_chapters_api,
        name='api_chapters'
    ),
    path('api/analytics/audio/', stub_analytics_api, name='api_analytics'),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Add static files serving in development
if settings.DEBUG:
    urlpatterns += static(
        settings.STATIC_URL, document_root=settings.STATIC_ROOT
    )

handler404 = 'django.views.defaults.page_not_found'
handler500 = 'django.views.defaults.server_error'
