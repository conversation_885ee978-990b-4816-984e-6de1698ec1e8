"""
Script to run the email queue processor as a background service.

Usage:
    python run_email_queue.py

This script will continuously process the email queue, respecting rate limits.
It can be run as a background service or scheduled task.
"""
import os
import sys
import django
import time
import logging
from datetime import datetime

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'tempusap_config.settings')
django.setup()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('email_queue.log')
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Run the email queue processor"""
    from django.core.management import call_command
    
    logger.info("Starting email queue processor")
    
    try:
        # Call the management command to process the queue continuously
        call_command('process_email_queue')
    except KeyboardInterrupt:
        logger.info("Email queue processor stopped by user")
    except Exception as e:
        logger.error(f"Error in email queue processor: {str(e)}")
        
    logger.info("Email queue processor stopped")

if __name__ == '__main__':
    main()
