/* Django-Select2 Customization */
.django-select2 {
    width: 100% !important;
    min-width: 200px !important;
}

.django-select2-container {
    width: 100% !important;
}

.select2-container {
    width: 100% !important;
    margin: 0 !important;
}

.select2-container .select2-selection--multiple {
    min-height: 38px !important;
    border: 1px solid #ccc !important;
    border-radius: 4px !important;
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #86b7fe !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #0d6efd !important;
    border: none !important;
    color: #fff !important;
    border-radius: 4px !important;
    padding: 2px 8px !important;
    margin: 4px !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #fff !important;
    margin-right: 5px !important;
    padding: 0 4px !important;
    border: none !important;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #fff !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #0d6efd !important;
    color: #fff !important;
}

.select2-container--default .select2-search--inline .select2-search__field {
    margin-top: 6px !important;
    padding-left: 6px !important;
}

/* Fix for the flash of unstyled content */
.select2 {
    visibility: hidden;
}

.select2-container {
    visibility: visible;
}
