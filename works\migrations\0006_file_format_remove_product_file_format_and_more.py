# Generated by Django 5.1.5 on 2025-02-20 13:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('works', '0005_genre_language'),
    ]

    operations = [
        migrations.CreateModel(
            name='File_format',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
            ],
        ),
        migrations.RemoveField(
            model_name='product',
            name='file_format',
        ),
        migrations.RemoveField(
            model_name='product',
            name='genre',
        ),
        migrations.AlterField(
            model_name='product',
            name='language',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='works.language'),
        ),
        migrations.AddField(
            model_name='product',
            name='file_format',
            field=models.ManyToManyField(blank=True, to='works.file_format'),
        ),
        migrations.AddField(
            model_name='product',
            name='genre',
            field=models.ManyToManyField(blank=True, to='works.genre'),
        ),
    ]
