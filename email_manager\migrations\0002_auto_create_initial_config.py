from django.db import migrations

def create_initial_config(apps, schema_editor):
    """
    Create the initial email configuration after migrations.
    """
    EmailConfiguration = apps.get_model('email_manager', 'EmailConfiguration')
    
    # Check if a configuration already exists
    if not EmailConfiguration.objects.exists():
        # Create the default configuration
        EmailConfiguration.objects.create(
            pk=1,
            require_email_verification=True,
            hourly_email_limit=90,
            queue_batch_size=10,
            queue_batch_delay=30,
            send_welcome_emails=True,
            send_newsletter_confirmation=False,
            combine_order_emails=True,
            notify_admin_on_limit=True
        )

class Migration(migrations.Migration):

    dependencies = [
        ('email_manager', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_initial_config),
    ]
